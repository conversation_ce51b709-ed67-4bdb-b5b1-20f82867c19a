{"ast": null, "code": "import LlmService from '@/services/LlmService';\nimport ColorExtractor from './ColorExtractor';\n\n/**\r\n * AI驱动的智能配色分析器\r\n * 通过AI模型理解壁纸内容，生成符合主题且有良好对比度的卡片样式\r\n */\nclass AIColorAnalyzer {\n  /**\r\n   * 分析壁纸并生成智能配色方案\r\n   * @param {string} imageUrl - 壁纸图片URL\r\n   * @param {string} scenePrompt - 场景描述\r\n   * @param {string} optimizedPrompt - LLM优化后的提示词\r\n   * @returns {Promise<Object>} 智能配色方案\r\n   */\n  static async analyzeWallpaperAndGenerateColors(imageUrl, scenePrompt = '', optimizedPrompt = '') {\n    try {\n      console.log('🎨 开始AI智能配色分析...');\n\n      // 1. 首先尝试传统颜色提取\n      const extractedColors = await ColorExtractor.extractColors(imageUrl, scenePrompt);\n\n      // 2. 使用AI分析壁纸内容和情感\n      const aiAnalysis = await this.analyzeWallpaperContent(scenePrompt, optimizedPrompt);\n\n      // 3. 结合AI分析和颜色提取生成智能配色\n      const intelligentColors = await this.generateIntelligentColorScheme(extractedColors, aiAnalysis, scenePrompt);\n      console.log('🎨 AI智能配色分析完成:', intelligentColors);\n      return intelligentColors;\n    } catch (error) {\n      console.error('AI配色分析失败:', error);\n      // 降级到基础颜色提取\n      return await ColorExtractor.extractColors(imageUrl, scenePrompt);\n    }\n  }\n\n  /**\r\n   * 使用AI分析壁纸内容和情感色彩\r\n   */\n  static async analyzeWallpaperContent(scenePrompt, optimizedPrompt) {\n    const analysisPrompt = `\n请分析以下壁纸场景的视觉特征和情感色彩，为UI设计提供配色建议：\n\n场景描述: ${scenePrompt}\n详细描述: ${optimizedPrompt}\n\n请从以下维度分析并返回JSON格式：\n{\n  \"mood\": \"情感氛围(如: 温暖、冷静、活力、神秘、优雅等)\",\n  \"dominantColors\": [\"主要颜色1\", \"主要颜色2\", \"主要颜色3\"],\n  \"brightness\": \"整体亮度(bright/medium/dark)\",\n  \"contrast\": \"对比度需求(high/medium/low)\",\n  \"atmosphere\": \"氛围描述\",\n  \"cardStyleSuggestion\": {\n    \"backgroundOpacity\": \"建议背景透明度(0.1-0.8)\",\n    \"blurIntensity\": \"建议模糊强度(8-20px)\",\n    \"borderStyle\": \"边框风格建议\",\n    \"textContrast\": \"文字对比度建议\"\n  },\n  \"colorHarmony\": \"色彩和谐度分析\"\n}\n\n请确保返回有效的JSON格式。`;\n    try {\n      const llmService = new LlmService();\n      const response = await llmService.generateResponse(analysisPrompt);\n\n      // 尝试解析JSON响应\n      const jsonMatch = response.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        return JSON.parse(jsonMatch[0]);\n      }\n\n      // 如果无法解析JSON，返回默认分析\n      return this.getDefaultAnalysis(scenePrompt);\n    } catch (error) {\n      console.error('AI内容分析失败:', error);\n      return this.getDefaultAnalysis(scenePrompt);\n    }\n  }\n\n  /**\r\n   * 获取默认分析结果\r\n   */\n  static getDefaultAnalysis(scenePrompt) {\n    const prompt = scenePrompt.toLowerCase();\n    if (prompt.includes('夜') || prompt.includes('night')) {\n      return {\n        mood: '神秘',\n        dominantColors: ['#2c3e50', '#34495e', '#9b59b6'],\n        brightness: 'dark',\n        contrast: 'high',\n        atmosphere: '夜晚神秘氛围',\n        cardStyleSuggestion: {\n          backgroundOpacity: '0.6',\n          blurIntensity: '15px',\n          borderStyle: '亮色边框',\n          textContrast: '高对比度白色文字'\n        }\n      };\n    } else if (prompt.includes('阳光') || prompt.includes('bright') || prompt.includes('morning')) {\n      return {\n        mood: '活力',\n        dominantColors: ['#f39c12', '#e67e22', '#3498db'],\n        brightness: 'bright',\n        contrast: 'medium',\n        atmosphere: '明亮活力氛围',\n        cardStyleSuggestion: {\n          backgroundOpacity: '0.3',\n          blurIntensity: '12px',\n          borderStyle: '柔和边框',\n          textContrast: '中等对比度深色文字'\n        }\n      };\n    }\n    return {\n      mood: '平衡',\n      dominantColors: ['#3498db', '#2ecc71', '#95a5a6'],\n      brightness: 'medium',\n      contrast: 'medium',\n      atmosphere: '平衡舒适氛围',\n      cardStyleSuggestion: {\n        backgroundOpacity: '0.4',\n        blurIntensity: '12px',\n        borderStyle: '标准边框',\n        textContrast: '标准对比度'\n      }\n    };\n  }\n\n  /**\r\n   * 结合AI分析生成智能配色方案\r\n   */\n  static async generateIntelligentColorScheme(extractedColors, aiAnalysis, scenePrompt) {\n    // 基于AI分析调整颜色方案\n    const baseColors = extractedColors.isSceneBased ? this.getAIEnhancedSceneColors(scenePrompt, aiAnalysis) : extractedColors;\n\n    // 生成智能卡片样式\n    const cardStyles = this.generateIntelligentCardStyles(baseColors, aiAnalysis);\n\n    // 生成智能文字颜色\n    const textColors = this.generateIntelligentTextColors(baseColors, aiAnalysis);\n\n    // 生成智能按钮样式\n    const buttonStyles = this.generateIntelligentButtonStyles(baseColors, aiAnalysis);\n    return {\n      ...baseColors,\n      // AI增强的样式\n      aiAnalysis: aiAnalysis,\n      intelligentCardStyles: cardStyles,\n      intelligentTextColors: textColors,\n      intelligentButtonStyles: buttonStyles,\n      // 覆盖原有样式\n      glassBackground: cardStyles.background,\n      glassBorder: cardStyles.border,\n      cardTitleColor: textColors.title,\n      cardContentColor: textColors.content,\n      buttonBackground: buttonStyles.background,\n      buttonColor: buttonStyles.color,\n      buttonBorder: buttonStyles.border,\n      buttonHoverBackground: buttonStyles.hoverBackground,\n      // 标记为AI增强\n      isAIEnhanced: true\n    };\n  }\n\n  /**\r\n   * 基于AI分析获取增强的场景颜色\r\n   */\n  static getAIEnhancedSceneColors(scenePrompt, aiAnalysis) {\n    const dominantColors = aiAnalysis.dominantColors || ['#3498db', '#2ecc71', '#95a5a6'];\n    const primaryColorHex = dominantColors[0];\n    // eslint-disable-next-line no-unused-vars\n    const primaryColor = ColorExtractor.hexToRgb(primaryColorHex) || {\n      r: 52,\n      g: 152,\n      b: 219\n    }; // 保留用于未来扩展，提供默认值\n\n    return {\n      primary: primaryColorHex,\n      secondary: dominantColors[1] || '#2ecc71',\n      accent: dominantColors[2] || '#95a5a6',\n      mood: aiAnalysis.mood,\n      brightness: aiAnalysis.brightness,\n      atmosphere: aiAnalysis.atmosphere,\n      isSceneBased: true,\n      isAIAnalyzed: true\n    };\n  }\n\n  /**\r\n   * 生成智能卡片样式\r\n   */\n  static generateIntelligentCardStyles(colors, aiAnalysis) {\n    const suggestion = aiAnalysis.cardStyleSuggestion || {};\n    const primaryColor = ColorExtractor.hexToRgb(colors.primary) || {\n      r: 52,\n      g: 152,\n      b: 219\n    };\n    const opacity = parseFloat(suggestion.backgroundOpacity) || 0.4;\n    const blurIntensity = suggestion.blurIntensity || '12px';\n\n    // 根据亮度和情感调整卡片背景\n    let background, border;\n    if (aiAnalysis.brightness === 'dark') {\n      // 暗色背景：使用亮色半透明卡片\n      background = `rgba(${Math.min(255, primaryColor.r + 60)}, ${Math.min(255, primaryColor.g + 60)}, ${Math.min(255, primaryColor.b + 60)}, ${opacity + 0.2})`;\n      border = `1px solid rgba(255, 255, 255, 0.3)`;\n    } else if (aiAnalysis.brightness === 'bright') {\n      // 亮色背景：使用深色半透明卡片\n      background = `rgba(${Math.max(0, primaryColor.r - 40)}, ${Math.max(0, primaryColor.g - 40)}, ${Math.max(0, primaryColor.b - 40)}, ${opacity + 0.1})`;\n      border = `1px solid rgba(0, 0, 0, 0.2)`;\n    } else {\n      // 中等亮度：平衡处理\n      background = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, ${opacity})`;\n      border = `1px solid rgba(255, 255, 255, 0.25)`;\n    }\n    return {\n      background,\n      border,\n      backdropFilter: `blur(${blurIntensity})`,\n      borderRadius: this.getIntelligentBorderRadius(aiAnalysis.mood),\n      boxShadow: this.getIntelligentShadow(aiAnalysis.brightness, aiAnalysis.mood)\n    };\n  }\n\n  /**\r\n   * 生成智能文字颜色\r\n   */\n  static generateIntelligentTextColors(colors, aiAnalysis) {\n    const contrast = aiAnalysis.contrast || 'medium';\n    const brightness = aiAnalysis.brightness || 'medium';\n    let titleColor, contentColor;\n    if (brightness === 'dark') {\n      titleColor = contrast === 'high' ? '#ffffff' : '#f8f9fa';\n      contentColor = contrast === 'high' ? '#e9ecef' : '#dee2e6';\n    } else if (brightness === 'bright') {\n      titleColor = contrast === 'high' ? '#212529' : '#343a40';\n      contentColor = contrast === 'high' ? '#495057' : '#6c757d';\n    } else {\n      titleColor = '#ffffff';\n      contentColor = '#e9ecef';\n    }\n    return {\n      title: titleColor,\n      content: contentColor,\n      textShadow: brightness === 'dark' ? '0 1px 3px rgba(0, 0, 0, 0.8)' : '0 1px 3px rgba(255, 255, 255, 0.8)'\n    };\n  }\n\n  /**\r\n   * 生成智能按钮样式\r\n   */\n  static generateIntelligentButtonStyles(colors, aiAnalysis) {\n    // eslint-disable-next-line no-unused-vars\n    const primaryColor = ColorExtractor.hexToRgb(colors.primary) || {\n      r: 52,\n      g: 152,\n      b: 219\n    }; // 用于计算按钮颜色，提供默认值\n    const mood = aiAnalysis.mood || '平衡';\n    const brightness = aiAnalysis.brightness || 'medium';\n\n    // 根据情感调整按钮样式\n    let buttonBg, buttonColor, buttonBorder, hoverBg;\n    if (mood.includes('活力') || mood.includes('温暖')) {\n      // 活力/温暖：使用饱和度高的颜色\n      buttonBg = `rgba(${Math.min(255, primaryColor.r + 20)}, ${Math.min(255, primaryColor.g + 20)}, ${Math.min(255, primaryColor.b + 20)}, 0.8)`;\n      buttonColor = brightness === 'bright' ? '#ffffff' : '#ffffff';\n      buttonBorder = '1px solid rgba(255, 255, 255, 0.6)';\n      hoverBg = `rgba(${Math.min(255, primaryColor.r + 40)}, ${Math.min(255, primaryColor.g + 40)}, ${Math.min(255, primaryColor.b + 40)}, 0.9)`;\n    } else if (mood.includes('神秘') || mood.includes('优雅')) {\n      // 神秘/优雅：使用深色调\n      buttonBg = `rgba(${Math.max(0, primaryColor.r - 20)}, ${Math.max(0, primaryColor.g - 20)}, ${Math.max(0, primaryColor.b - 20)}, 0.7)`;\n      buttonColor = '#ffffff';\n      buttonBorder = '1px solid rgba(255, 255, 255, 0.4)';\n      hoverBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.8)`;\n    } else {\n      // 平衡：标准处理\n      buttonBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.7)`;\n      buttonColor = brightness === 'bright' ? '#ffffff' : '#ffffff';\n      buttonBorder = '1px solid rgba(255, 255, 255, 0.5)';\n      hoverBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.8)`;\n    }\n    return {\n      background: buttonBg,\n      color: buttonColor,\n      border: buttonBorder,\n      hoverBackground: hoverBg,\n      textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)'\n    };\n  }\n\n  /**\r\n   * 获取智能圆角\r\n   */\n  static getIntelligentBorderRadius(mood) {\n    if (mood.includes('现代') || mood.includes('商务')) return '8px';\n    if (mood.includes('温暖') || mood.includes('舒适')) return '16px';\n    if (mood.includes('优雅') || mood.includes('神秘')) return '12px';\n    return '12px';\n  }\n\n  /**\r\n   * 获取智能阴影\r\n   */\n  static getIntelligentShadow(brightness, mood) {\n    let intensity = 0.1;\n    if (brightness === 'dark') intensity = 0.3;else if (brightness === 'bright') intensity = 0.05;\n    if (mood.includes('神秘')) intensity += 0.1;\n    if (mood.includes('活力')) intensity -= 0.05;\n    return `0 8px 32px rgba(0, 0, 0, ${Math.max(0.05, Math.min(0.4, intensity))})`;\n  }\n}\nexport default AIColorAnalyzer;", "map": {"version": 3, "names": ["LlmService", "ColorExtractor", "AIColorAnalyzer", "analyzeWallpaperAndGenerateColors", "imageUrl", "scenePrompt", "optimizedPrompt", "console", "log", "extractedColors", "extractColors", "aiAnalysis", "analyzeWallpaperContent", "intelligentColors", "generateIntelligentColorScheme", "error", "analysisPrompt", "llmService", "response", "generateResponse", "jsonMatch", "match", "JSON", "parse", "getDefaultAnalysis", "prompt", "toLowerCase", "includes", "mood", "dominantColors", "brightness", "contrast", "atmosphere", "cardStyleSuggestion", "backgroundOpacity", "blurIntensity", "borderStyle", "textContrast", "baseColors", "isSceneBased", "getAIEnhancedSceneColors", "cardStyles", "generateIntelligentCardStyles", "textColors", "generateIntelligentTextColors", "buttonStyles", "generateIntelligentButtonStyles", "intelligentCardStyles", "intelligentTextColors", "intelligentButtonStyles", "glassBackground", "background", "glassBorder", "border", "cardTitleColor", "title", "cardContentColor", "content", "buttonBackground", "buttonColor", "color", "buttonBorder", "buttonHoverBackground", "hoverBackground", "isAIEnhanced", "primaryColorHex", "primaryColor", "hexToRgb", "r", "g", "b", "primary", "secondary", "accent", "isAIAnalyzed", "colors", "suggestion", "opacity", "parseFloat", "Math", "min", "max", "<PERSON><PERSON>ilter", "borderRadius", "getIntelligentBorderRadius", "boxShadow", "getIntelligentShadow", "titleColor", "contentColor", "textShadow", "buttonBg", "hoverBg", "intensity"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/utils/AIColorAnalyzer.js"], "sourcesContent": ["import LlmService from '@/services/LlmService'\r\nimport ColorExtractor from './ColorExtractor'\r\n\r\n/**\r\n * AI驱动的智能配色分析器\r\n * 通过AI模型理解壁纸内容，生成符合主题且有良好对比度的卡片样式\r\n */\r\nclass AIColorAnalyzer {\r\n  /**\r\n   * 分析壁纸并生成智能配色方案\r\n   * @param {string} imageUrl - 壁纸图片URL\r\n   * @param {string} scenePrompt - 场景描述\r\n   * @param {string} optimizedPrompt - LLM优化后的提示词\r\n   * @returns {Promise<Object>} 智能配色方案\r\n   */\r\n  static async analyzeWallpaperAndGenerateColors(imageUrl, scenePrompt = '', optimizedPrompt = '') {\r\n    try {\r\n      console.log('🎨 开始AI智能配色分析...')\r\n      \r\n      // 1. 首先尝试传统颜色提取\r\n      const extractedColors = await ColorExtractor.extractColors(imageUrl, scenePrompt)\r\n      \r\n      // 2. 使用AI分析壁纸内容和情感\r\n      const aiAnalysis = await this.analyzeWallpaperContent(scenePrompt, optimizedPrompt)\r\n      \r\n      // 3. 结合AI分析和颜色提取生成智能配色\r\n      const intelligentColors = await this.generateIntelligentColorScheme(\r\n        extractedColors, \r\n        aiAnalysis, \r\n        scenePrompt\r\n      )\r\n      \r\n      console.log('🎨 AI智能配色分析完成:', intelligentColors)\r\n      return intelligentColors\r\n      \r\n    } catch (error) {\r\n      console.error('AI配色分析失败:', error)\r\n      // 降级到基础颜色提取\r\n      return await ColorExtractor.extractColors(imageUrl, scenePrompt)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 使用AI分析壁纸内容和情感色彩\r\n   */\r\n  static async analyzeWallpaperContent(scenePrompt, optimizedPrompt) {\r\n    const analysisPrompt = `\r\n请分析以下壁纸场景的视觉特征和情感色彩，为UI设计提供配色建议：\r\n\r\n场景描述: ${scenePrompt}\r\n详细描述: ${optimizedPrompt}\r\n\r\n请从以下维度分析并返回JSON格式：\r\n{\r\n  \"mood\": \"情感氛围(如: 温暖、冷静、活力、神秘、优雅等)\",\r\n  \"dominantColors\": [\"主要颜色1\", \"主要颜色2\", \"主要颜色3\"],\r\n  \"brightness\": \"整体亮度(bright/medium/dark)\",\r\n  \"contrast\": \"对比度需求(high/medium/low)\",\r\n  \"atmosphere\": \"氛围描述\",\r\n  \"cardStyleSuggestion\": {\r\n    \"backgroundOpacity\": \"建议背景透明度(0.1-0.8)\",\r\n    \"blurIntensity\": \"建议模糊强度(8-20px)\",\r\n    \"borderStyle\": \"边框风格建议\",\r\n    \"textContrast\": \"文字对比度建议\"\r\n  },\r\n  \"colorHarmony\": \"色彩和谐度分析\"\r\n}\r\n\r\n请确保返回有效的JSON格式。`\r\n\r\n    try {\r\n      const llmService = new LlmService()\r\n      const response = await llmService.generateResponse(analysisPrompt)\r\n      \r\n      // 尝试解析JSON响应\r\n      const jsonMatch = response.match(/\\{[\\s\\S]*\\}/)\r\n      if (jsonMatch) {\r\n        return JSON.parse(jsonMatch[0])\r\n      }\r\n      \r\n      // 如果无法解析JSON，返回默认分析\r\n      return this.getDefaultAnalysis(scenePrompt)\r\n      \r\n    } catch (error) {\r\n      console.error('AI内容分析失败:', error)\r\n      return this.getDefaultAnalysis(scenePrompt)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取默认分析结果\r\n   */\r\n  static getDefaultAnalysis(scenePrompt) {\r\n    const prompt = scenePrompt.toLowerCase()\r\n    \r\n    if (prompt.includes('夜') || prompt.includes('night')) {\r\n      return {\r\n        mood: '神秘',\r\n        dominantColors: ['#2c3e50', '#34495e', '#9b59b6'],\r\n        brightness: 'dark',\r\n        contrast: 'high',\r\n        atmosphere: '夜晚神秘氛围',\r\n        cardStyleSuggestion: {\r\n          backgroundOpacity: '0.6',\r\n          blurIntensity: '15px',\r\n          borderStyle: '亮色边框',\r\n          textContrast: '高对比度白色文字'\r\n        }\r\n      }\r\n    } else if (prompt.includes('阳光') || prompt.includes('bright') || prompt.includes('morning')) {\r\n      return {\r\n        mood: '活力',\r\n        dominantColors: ['#f39c12', '#e67e22', '#3498db'],\r\n        brightness: 'bright',\r\n        contrast: 'medium',\r\n        atmosphere: '明亮活力氛围',\r\n        cardStyleSuggestion: {\r\n          backgroundOpacity: '0.3',\r\n          blurIntensity: '12px',\r\n          borderStyle: '柔和边框',\r\n          textContrast: '中等对比度深色文字'\r\n        }\r\n      }\r\n    }\r\n    \r\n    return {\r\n      mood: '平衡',\r\n      dominantColors: ['#3498db', '#2ecc71', '#95a5a6'],\r\n      brightness: 'medium',\r\n      contrast: 'medium',\r\n      atmosphere: '平衡舒适氛围',\r\n      cardStyleSuggestion: {\r\n        backgroundOpacity: '0.4',\r\n        blurIntensity: '12px',\r\n        borderStyle: '标准边框',\r\n        textContrast: '标准对比度'\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 结合AI分析生成智能配色方案\r\n   */\r\n  static async generateIntelligentColorScheme(extractedColors, aiAnalysis, scenePrompt) {\r\n    // 基于AI分析调整颜色方案\r\n    const baseColors = extractedColors.isSceneBased ? \r\n      this.getAIEnhancedSceneColors(scenePrompt, aiAnalysis) : \r\n      extractedColors\r\n\r\n    // 生成智能卡片样式\r\n    const cardStyles = this.generateIntelligentCardStyles(baseColors, aiAnalysis)\r\n    \r\n    // 生成智能文字颜色\r\n    const textColors = this.generateIntelligentTextColors(baseColors, aiAnalysis)\r\n    \r\n    // 生成智能按钮样式\r\n    const buttonStyles = this.generateIntelligentButtonStyles(baseColors, aiAnalysis)\r\n\r\n    return {\r\n      ...baseColors,\r\n      // AI增强的样式\r\n      aiAnalysis: aiAnalysis,\r\n      intelligentCardStyles: cardStyles,\r\n      intelligentTextColors: textColors,\r\n      intelligentButtonStyles: buttonStyles,\r\n      // 覆盖原有样式\r\n      glassBackground: cardStyles.background,\r\n      glassBorder: cardStyles.border,\r\n      cardTitleColor: textColors.title,\r\n      cardContentColor: textColors.content,\r\n      buttonBackground: buttonStyles.background,\r\n      buttonColor: buttonStyles.color,\r\n      buttonBorder: buttonStyles.border,\r\n      buttonHoverBackground: buttonStyles.hoverBackground,\r\n      // 标记为AI增强\r\n      isAIEnhanced: true\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 基于AI分析获取增强的场景颜色\r\n   */\r\n  static getAIEnhancedSceneColors(scenePrompt, aiAnalysis) {\r\n    const dominantColors = aiAnalysis.dominantColors || ['#3498db', '#2ecc71', '#95a5a6']\r\n    const primaryColorHex = dominantColors[0]\r\n    // eslint-disable-next-line no-unused-vars\r\n    const primaryColor = ColorExtractor.hexToRgb(primaryColorHex) || { r: 52, g: 152, b: 219 } // 保留用于未来扩展，提供默认值\r\n\r\n    return {\r\n      primary: primaryColorHex,\r\n      secondary: dominantColors[1] || '#2ecc71',\r\n      accent: dominantColors[2] || '#95a5a6',\r\n      mood: aiAnalysis.mood,\r\n      brightness: aiAnalysis.brightness,\r\n      atmosphere: aiAnalysis.atmosphere,\r\n      isSceneBased: true,\r\n      isAIAnalyzed: true\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 生成智能卡片样式\r\n   */\r\n  static generateIntelligentCardStyles(colors, aiAnalysis) {\r\n    const suggestion = aiAnalysis.cardStyleSuggestion || {}\r\n    const primaryColor = ColorExtractor.hexToRgb(colors.primary) || { r: 52, g: 152, b: 219 }\r\n    const opacity = parseFloat(suggestion.backgroundOpacity) || 0.4\r\n    const blurIntensity = suggestion.blurIntensity || '12px'\r\n\r\n    // 根据亮度和情感调整卡片背景\r\n    let background, border\r\n    \r\n    if (aiAnalysis.brightness === 'dark') {\r\n      // 暗色背景：使用亮色半透明卡片\r\n      background = `rgba(${Math.min(255, primaryColor.r + 60)}, ${Math.min(255, primaryColor.g + 60)}, ${Math.min(255, primaryColor.b + 60)}, ${opacity + 0.2})`\r\n      border = `1px solid rgba(255, 255, 255, 0.3)`\r\n    } else if (aiAnalysis.brightness === 'bright') {\r\n      // 亮色背景：使用深色半透明卡片\r\n      background = `rgba(${Math.max(0, primaryColor.r - 40)}, ${Math.max(0, primaryColor.g - 40)}, ${Math.max(0, primaryColor.b - 40)}, ${opacity + 0.1})`\r\n      border = `1px solid rgba(0, 0, 0, 0.2)`\r\n    } else {\r\n      // 中等亮度：平衡处理\r\n      background = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, ${opacity})`\r\n      border = `1px solid rgba(255, 255, 255, 0.25)`\r\n    }\r\n\r\n    return {\r\n      background,\r\n      border,\r\n      backdropFilter: `blur(${blurIntensity})`,\r\n      borderRadius: this.getIntelligentBorderRadius(aiAnalysis.mood),\r\n      boxShadow: this.getIntelligentShadow(aiAnalysis.brightness, aiAnalysis.mood)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 生成智能文字颜色\r\n   */\r\n  static generateIntelligentTextColors(colors, aiAnalysis) {\r\n    const contrast = aiAnalysis.contrast || 'medium'\r\n    const brightness = aiAnalysis.brightness || 'medium'\r\n\r\n    let titleColor, contentColor\r\n\r\n    if (brightness === 'dark') {\r\n      titleColor = contrast === 'high' ? '#ffffff' : '#f8f9fa'\r\n      contentColor = contrast === 'high' ? '#e9ecef' : '#dee2e6'\r\n    } else if (brightness === 'bright') {\r\n      titleColor = contrast === 'high' ? '#212529' : '#343a40'\r\n      contentColor = contrast === 'high' ? '#495057' : '#6c757d'\r\n    } else {\r\n      titleColor = '#ffffff'\r\n      contentColor = '#e9ecef'\r\n    }\r\n\r\n    return {\r\n      title: titleColor,\r\n      content: contentColor,\r\n      textShadow: brightness === 'dark' ? \r\n        '0 1px 3px rgba(0, 0, 0, 0.8)' : \r\n        '0 1px 3px rgba(255, 255, 255, 0.8)'\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 生成智能按钮样式\r\n   */\r\n  static generateIntelligentButtonStyles(colors, aiAnalysis) {\r\n    // eslint-disable-next-line no-unused-vars\r\n    const primaryColor = ColorExtractor.hexToRgb(colors.primary) || { r: 52, g: 152, b: 219 } // 用于计算按钮颜色，提供默认值\r\n    const mood = aiAnalysis.mood || '平衡'\r\n    const brightness = aiAnalysis.brightness || 'medium'\r\n\r\n    // 根据情感调整按钮样式\r\n    let buttonBg, buttonColor, buttonBorder, hoverBg\r\n\r\n    if (mood.includes('活力') || mood.includes('温暖')) {\r\n      // 活力/温暖：使用饱和度高的颜色\r\n      buttonBg = `rgba(${Math.min(255, primaryColor.r + 20)}, ${Math.min(255, primaryColor.g + 20)}, ${Math.min(255, primaryColor.b + 20)}, 0.8)`\r\n      buttonColor = brightness === 'bright' ? '#ffffff' : '#ffffff'\r\n      buttonBorder = '1px solid rgba(255, 255, 255, 0.6)'\r\n      hoverBg = `rgba(${Math.min(255, primaryColor.r + 40)}, ${Math.min(255, primaryColor.g + 40)}, ${Math.min(255, primaryColor.b + 40)}, 0.9)`\r\n    } else if (mood.includes('神秘') || mood.includes('优雅')) {\r\n      // 神秘/优雅：使用深色调\r\n      buttonBg = `rgba(${Math.max(0, primaryColor.r - 20)}, ${Math.max(0, primaryColor.g - 20)}, ${Math.max(0, primaryColor.b - 20)}, 0.7)`\r\n      buttonColor = '#ffffff'\r\n      buttonBorder = '1px solid rgba(255, 255, 255, 0.4)'\r\n      hoverBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.8)`\r\n    } else {\r\n      // 平衡：标准处理\r\n      buttonBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.7)`\r\n      buttonColor = brightness === 'bright' ? '#ffffff' : '#ffffff'\r\n      buttonBorder = '1px solid rgba(255, 255, 255, 0.5)'\r\n      hoverBg = `rgba(${primaryColor.r}, ${primaryColor.g}, ${primaryColor.b}, 0.8)`\r\n    }\r\n\r\n    return {\r\n      background: buttonBg,\r\n      color: buttonColor,\r\n      border: buttonBorder,\r\n      hoverBackground: hoverBg,\r\n      textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)'\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取智能圆角\r\n   */\r\n  static getIntelligentBorderRadius(mood) {\r\n    if (mood.includes('现代') || mood.includes('商务')) return '8px'\r\n    if (mood.includes('温暖') || mood.includes('舒适')) return '16px'\r\n    if (mood.includes('优雅') || mood.includes('神秘')) return '12px'\r\n    return '12px'\r\n  }\r\n\r\n  /**\r\n   * 获取智能阴影\r\n   */\r\n  static getIntelligentShadow(brightness, mood) {\r\n    let intensity = 0.1\r\n    \r\n    if (brightness === 'dark') intensity = 0.3\r\n    else if (brightness === 'bright') intensity = 0.05\r\n    \r\n    if (mood.includes('神秘')) intensity += 0.1\r\n    if (mood.includes('活力')) intensity -= 0.05\r\n    \r\n    return `0 8px 32px rgba(0, 0, 0, ${Math.max(0.05, Math.min(0.4, intensity))})`\r\n  }\r\n}\r\n\r\nexport default AIColorAnalyzer\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,cAAc,MAAM,kBAAkB;;AAE7C;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;EACE,aAAaC,iCAAiCA,CAACC,QAAQ,EAAEC,WAAW,GAAG,EAAE,EAAEC,eAAe,GAAG,EAAE,EAAE;IAC/F,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;MAE/B;MACA,MAAMC,eAAe,GAAG,MAAMR,cAAc,CAACS,aAAa,CAACN,QAAQ,EAAEC,WAAW,CAAC;;MAEjF;MACA,MAAMM,UAAU,GAAG,MAAM,IAAI,CAACC,uBAAuB,CAACP,WAAW,EAAEC,eAAe,CAAC;;MAEnF;MACA,MAAMO,iBAAiB,GAAG,MAAM,IAAI,CAACC,8BAA8B,CACjEL,eAAe,EACfE,UAAU,EACVN,WACF,CAAC;MAEDE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEK,iBAAiB,CAAC;MAChD,OAAOA,iBAAiB;IAE1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACA,OAAO,MAAMd,cAAc,CAACS,aAAa,CAACN,QAAQ,EAAEC,WAAW,CAAC;IAClE;EACF;;EAEA;AACF;AACA;EACE,aAAaO,uBAAuBA,CAACP,WAAW,EAAEC,eAAe,EAAE;IACjE,MAAMU,cAAc,GAAG;AAC3B;AACA;AACA,QAAQX,WAAW;AACnB,QAAQC,eAAe;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;IAEZ,IAAI;MACF,MAAMW,UAAU,GAAG,IAAIjB,UAAU,CAAC,CAAC;MACnC,MAAMkB,QAAQ,GAAG,MAAMD,UAAU,CAACE,gBAAgB,CAACH,cAAc,CAAC;;MAElE;MACA,MAAMI,SAAS,GAAGF,QAAQ,CAACG,KAAK,CAAC,aAAa,CAAC;MAC/C,IAAID,SAAS,EAAE;QACb,OAAOE,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;MACjC;;MAEA;MACA,OAAO,IAAI,CAACI,kBAAkB,CAACnB,WAAW,CAAC;IAE7C,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO,IAAI,CAACS,kBAAkB,CAACnB,WAAW,CAAC;IAC7C;EACF;;EAEA;AACF;AACA;EACE,OAAOmB,kBAAkBA,CAACnB,WAAW,EAAE;IACrC,MAAMoB,MAAM,GAAGpB,WAAW,CAACqB,WAAW,CAAC,CAAC;IAExC,IAAID,MAAM,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,MAAM,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MACpD,OAAO;QACLC,IAAI,EAAE,IAAI;QACVC,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QACjDC,UAAU,EAAE,MAAM;QAClBC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,QAAQ;QACpBC,mBAAmB,EAAE;UACnBC,iBAAiB,EAAE,KAAK;UACxBC,aAAa,EAAE,MAAM;UACrBC,WAAW,EAAE,MAAM;UACnBC,YAAY,EAAE;QAChB;MACF,CAAC;IACH,CAAC,MAAM,IAAIZ,MAAM,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,MAAM,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,MAAM,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC3F,OAAO;QACLC,IAAI,EAAE,IAAI;QACVC,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QACjDC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,QAAQ;QACpBC,mBAAmB,EAAE;UACnBC,iBAAiB,EAAE,KAAK;UACxBC,aAAa,EAAE,MAAM;UACrBC,WAAW,EAAE,MAAM;UACnBC,YAAY,EAAE;QAChB;MACF,CAAC;IACH;IAEA,OAAO;MACLT,IAAI,EAAE,IAAI;MACVC,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACjDC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,QAAQ;MACpBC,mBAAmB,EAAE;QACnBC,iBAAiB,EAAE,KAAK;QACxBC,aAAa,EAAE,MAAM;QACrBC,WAAW,EAAE,MAAM;QACnBC,YAAY,EAAE;MAChB;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACE,aAAavB,8BAA8BA,CAACL,eAAe,EAAEE,UAAU,EAAEN,WAAW,EAAE;IACpF;IACA,MAAMiC,UAAU,GAAG7B,eAAe,CAAC8B,YAAY,GAC7C,IAAI,CAACC,wBAAwB,CAACnC,WAAW,EAAEM,UAAU,CAAC,GACtDF,eAAe;;IAEjB;IACA,MAAMgC,UAAU,GAAG,IAAI,CAACC,6BAA6B,CAACJ,UAAU,EAAE3B,UAAU,CAAC;;IAE7E;IACA,MAAMgC,UAAU,GAAG,IAAI,CAACC,6BAA6B,CAACN,UAAU,EAAE3B,UAAU,CAAC;;IAE7E;IACA,MAAMkC,YAAY,GAAG,IAAI,CAACC,+BAA+B,CAACR,UAAU,EAAE3B,UAAU,CAAC;IAEjF,OAAO;MACL,GAAG2B,UAAU;MACb;MACA3B,UAAU,EAAEA,UAAU;MACtBoC,qBAAqB,EAAEN,UAAU;MACjCO,qBAAqB,EAAEL,UAAU;MACjCM,uBAAuB,EAAEJ,YAAY;MACrC;MACAK,eAAe,EAAET,UAAU,CAACU,UAAU;MACtCC,WAAW,EAAEX,UAAU,CAACY,MAAM;MAC9BC,cAAc,EAAEX,UAAU,CAACY,KAAK;MAChCC,gBAAgB,EAAEb,UAAU,CAACc,OAAO;MACpCC,gBAAgB,EAAEb,YAAY,CAACM,UAAU;MACzCQ,WAAW,EAAEd,YAAY,CAACe,KAAK;MAC/BC,YAAY,EAAEhB,YAAY,CAACQ,MAAM;MACjCS,qBAAqB,EAAEjB,YAAY,CAACkB,eAAe;MACnD;MACAC,YAAY,EAAE;IAChB,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOxB,wBAAwBA,CAACnC,WAAW,EAAEM,UAAU,EAAE;IACvD,MAAMkB,cAAc,GAAGlB,UAAU,CAACkB,cAAc,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IACrF,MAAMoC,eAAe,GAAGpC,cAAc,CAAC,CAAC,CAAC;IACzC;IACA,MAAMqC,YAAY,GAAGjE,cAAc,CAACkE,QAAQ,CAACF,eAAe,CAAC,IAAI;MAAEG,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC,EAAC;;IAE3F,OAAO;MACLC,OAAO,EAAEN,eAAe;MACxBO,SAAS,EAAE3C,cAAc,CAAC,CAAC,CAAC,IAAI,SAAS;MACzC4C,MAAM,EAAE5C,cAAc,CAAC,CAAC,CAAC,IAAI,SAAS;MACtCD,IAAI,EAAEjB,UAAU,CAACiB,IAAI;MACrBE,UAAU,EAAEnB,UAAU,CAACmB,UAAU;MACjCE,UAAU,EAAErB,UAAU,CAACqB,UAAU;MACjCO,YAAY,EAAE,IAAI;MAClBmC,YAAY,EAAE;IAChB,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOhC,6BAA6BA,CAACiC,MAAM,EAAEhE,UAAU,EAAE;IACvD,MAAMiE,UAAU,GAAGjE,UAAU,CAACsB,mBAAmB,IAAI,CAAC,CAAC;IACvD,MAAMiC,YAAY,GAAGjE,cAAc,CAACkE,QAAQ,CAACQ,MAAM,CAACJ,OAAO,CAAC,IAAI;MAAEH,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IACzF,MAAMO,OAAO,GAAGC,UAAU,CAACF,UAAU,CAAC1C,iBAAiB,CAAC,IAAI,GAAG;IAC/D,MAAMC,aAAa,GAAGyC,UAAU,CAACzC,aAAa,IAAI,MAAM;;IAExD;IACA,IAAIgB,UAAU,EAAEE,MAAM;IAEtB,IAAI1C,UAAU,CAACmB,UAAU,KAAK,MAAM,EAAE;MACpC;MACAqB,UAAU,GAAG,QAAQ4B,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACE,CAAC,GAAG,EAAE,CAAC,KAAKW,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACG,CAAC,GAAG,EAAE,CAAC,KAAKU,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACI,CAAC,GAAG,EAAE,CAAC,KAAKO,OAAO,GAAG,GAAG,GAAG;MAC1JxB,MAAM,GAAG,oCAAoC;IAC/C,CAAC,MAAM,IAAI1C,UAAU,CAACmB,UAAU,KAAK,QAAQ,EAAE;MAC7C;MACAqB,UAAU,GAAG,QAAQ4B,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEf,YAAY,CAACE,CAAC,GAAG,EAAE,CAAC,KAAKW,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEf,YAAY,CAACG,CAAC,GAAG,EAAE,CAAC,KAAKU,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEf,YAAY,CAACI,CAAC,GAAG,EAAE,CAAC,KAAKO,OAAO,GAAG,GAAG,GAAG;MACpJxB,MAAM,GAAG,8BAA8B;IACzC,CAAC,MAAM;MACL;MACAF,UAAU,GAAG,QAAQe,YAAY,CAACE,CAAC,KAAKF,YAAY,CAACG,CAAC,KAAKH,YAAY,CAACI,CAAC,KAAKO,OAAO,GAAG;MACxFxB,MAAM,GAAG,qCAAqC;IAChD;IAEA,OAAO;MACLF,UAAU;MACVE,MAAM;MACN6B,cAAc,EAAE,QAAQ/C,aAAa,GAAG;MACxCgD,YAAY,EAAE,IAAI,CAACC,0BAA0B,CAACzE,UAAU,CAACiB,IAAI,CAAC;MAC9DyD,SAAS,EAAE,IAAI,CAACC,oBAAoB,CAAC3E,UAAU,CAACmB,UAAU,EAAEnB,UAAU,CAACiB,IAAI;IAC7E,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOgB,6BAA6BA,CAAC+B,MAAM,EAAEhE,UAAU,EAAE;IACvD,MAAMoB,QAAQ,GAAGpB,UAAU,CAACoB,QAAQ,IAAI,QAAQ;IAChD,MAAMD,UAAU,GAAGnB,UAAU,CAACmB,UAAU,IAAI,QAAQ;IAEpD,IAAIyD,UAAU,EAAEC,YAAY;IAE5B,IAAI1D,UAAU,KAAK,MAAM,EAAE;MACzByD,UAAU,GAAGxD,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;MACxDyD,YAAY,GAAGzD,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;IAC5D,CAAC,MAAM,IAAID,UAAU,KAAK,QAAQ,EAAE;MAClCyD,UAAU,GAAGxD,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;MACxDyD,YAAY,GAAGzD,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;IAC5D,CAAC,MAAM;MACLwD,UAAU,GAAG,SAAS;MACtBC,YAAY,GAAG,SAAS;IAC1B;IAEA,OAAO;MACLjC,KAAK,EAAEgC,UAAU;MACjB9B,OAAO,EAAE+B,YAAY;MACrBC,UAAU,EAAE3D,UAAU,KAAK,MAAM,GAC/B,8BAA8B,GAC9B;IACJ,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOgB,+BAA+BA,CAAC6B,MAAM,EAAEhE,UAAU,EAAE;IACzD;IACA,MAAMuD,YAAY,GAAGjE,cAAc,CAACkE,QAAQ,CAACQ,MAAM,CAACJ,OAAO,CAAC,IAAI;MAAEH,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC,EAAC;IAC1F,MAAM1C,IAAI,GAAGjB,UAAU,CAACiB,IAAI,IAAI,IAAI;IACpC,MAAME,UAAU,GAAGnB,UAAU,CAACmB,UAAU,IAAI,QAAQ;;IAEpD;IACA,IAAI4D,QAAQ,EAAE/B,WAAW,EAAEE,YAAY,EAAE8B,OAAO;IAEhD,IAAI/D,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,IAAIC,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC9C;MACA+D,QAAQ,GAAG,QAAQX,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACE,CAAC,GAAG,EAAE,CAAC,KAAKW,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACG,CAAC,GAAG,EAAE,CAAC,KAAKU,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACI,CAAC,GAAG,EAAE,CAAC,QAAQ;MAC3IX,WAAW,GAAG7B,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;MAC7D+B,YAAY,GAAG,oCAAoC;MACnD8B,OAAO,GAAG,QAAQZ,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACE,CAAC,GAAG,EAAE,CAAC,KAAKW,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACG,CAAC,GAAG,EAAE,CAAC,KAAKU,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEd,YAAY,CAACI,CAAC,GAAG,EAAE,CAAC,QAAQ;IAC5I,CAAC,MAAM,IAAI1C,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,IAAIC,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrD;MACA+D,QAAQ,GAAG,QAAQX,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEf,YAAY,CAACE,CAAC,GAAG,EAAE,CAAC,KAAKW,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEf,YAAY,CAACG,CAAC,GAAG,EAAE,CAAC,KAAKU,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEf,YAAY,CAACI,CAAC,GAAG,EAAE,CAAC,QAAQ;MACrIX,WAAW,GAAG,SAAS;MACvBE,YAAY,GAAG,oCAAoC;MACnD8B,OAAO,GAAG,QAAQzB,YAAY,CAACE,CAAC,KAAKF,YAAY,CAACG,CAAC,KAAKH,YAAY,CAACI,CAAC,QAAQ;IAChF,CAAC,MAAM;MACL;MACAoB,QAAQ,GAAG,QAAQxB,YAAY,CAACE,CAAC,KAAKF,YAAY,CAACG,CAAC,KAAKH,YAAY,CAACI,CAAC,QAAQ;MAC/EX,WAAW,GAAG7B,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;MAC7D+B,YAAY,GAAG,oCAAoC;MACnD8B,OAAO,GAAG,QAAQzB,YAAY,CAACE,CAAC,KAAKF,YAAY,CAACG,CAAC,KAAKH,YAAY,CAACI,CAAC,QAAQ;IAChF;IAEA,OAAO;MACLnB,UAAU,EAAEuC,QAAQ;MACpB9B,KAAK,EAAED,WAAW;MAClBN,MAAM,EAAEQ,YAAY;MACpBE,eAAe,EAAE4B,OAAO;MACxBF,UAAU,EAAE;IACd,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOL,0BAA0BA,CAACxD,IAAI,EAAE;IACtC,IAAIA,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,IAAIC,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,KAAK;IAC5D,IAAIC,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,IAAIC,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,MAAM;IAC7D,IAAIC,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,IAAIC,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,MAAM;IAC7D,OAAO,MAAM;EACf;;EAEA;AACF;AACA;EACE,OAAO2D,oBAAoBA,CAACxD,UAAU,EAAEF,IAAI,EAAE;IAC5C,IAAIgE,SAAS,GAAG,GAAG;IAEnB,IAAI9D,UAAU,KAAK,MAAM,EAAE8D,SAAS,GAAG,GAAG,MACrC,IAAI9D,UAAU,KAAK,QAAQ,EAAE8D,SAAS,GAAG,IAAI;IAElD,IAAIhE,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAEiE,SAAS,IAAI,GAAG;IACzC,IAAIhE,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAEiE,SAAS,IAAI,IAAI;IAE1C,OAAO,4BAA4Bb,IAAI,CAACE,GAAG,CAAC,IAAI,EAAEF,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEY,SAAS,CAAC,CAAC,GAAG;EAChF;AACF;AAEA,eAAe1F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}