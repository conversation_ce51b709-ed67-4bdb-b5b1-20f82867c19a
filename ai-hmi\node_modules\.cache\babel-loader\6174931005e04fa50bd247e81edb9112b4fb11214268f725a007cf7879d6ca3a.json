{"ast": null, "code": "// Store入口文件\nexport { useLayoutStore } from './modules/layout';\nexport { useVpaStore } from './modules/vpa';\n\n// 可以在这里添加其他store的导出\n// export { useSceneStore } from './modules/scene'\n// export { useThemeStore } from './modules/theme'\n// export { useUserStore } from './modules/user'", "map": {"version": 3, "names": ["useLayoutStore", "useVpaStore"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/store/index.js"], "sourcesContent": ["// Store入口文件\r\nexport { useLayoutStore } from './modules/layout'\r\nexport { useVpaStore } from './modules/vpa'\r\n\r\n// 可以在这里添加其他store的导出\r\n// export { useSceneStore } from './modules/scene'\r\n// export { useThemeStore } from './modules/theme'\r\n// export { useUserStore } from './modules/user'\r\n"], "mappings": "AAAA;AACA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;;AAE3C;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}