{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n// runtime helper for setting properties on components\n// in a tree-shakable way\nexports.default = (sfc, props) => {\n  const target = sfc.__vccOpts || sfc;\n  for (const [key, val] of props) {\n    target[key] = val;\n  }\n  return target;\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "sfc", "props", "target", "__vccOpts", "key", "val"], "sources": ["D:/code/pythonWork/theme/ai-hmi/node_modules/vue-loader/dist/exportHelper.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// runtime helper for setting properties on components\n// in a tree-shakable way\nexports.default = (sfc, props) => {\n    const target = sfc.__vccOpts || sfc;\n    for (const [key, val] of props) {\n        target[key] = val;\n    }\n    return target;\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D;AACA;AACAD,OAAO,CAACE,OAAO,GAAG,CAACC,GAAG,EAAEC,KAAK,KAAK;EAC9B,MAAMC,MAAM,GAAGF,GAAG,CAACG,SAAS,IAAIH,GAAG;EACnC,KAAK,MAAM,CAACI,GAAG,EAAEC,GAAG,CAAC,IAAIJ,KAAK,EAAE;IAC5BC,MAAM,CAACE,GAAG,CAAC,GAAGC,GAAG;EACrB;EACA,OAAOH,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}