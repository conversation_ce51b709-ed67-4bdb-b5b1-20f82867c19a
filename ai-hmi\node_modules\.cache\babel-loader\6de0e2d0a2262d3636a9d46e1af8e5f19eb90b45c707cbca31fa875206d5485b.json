{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, vModelText as _vModelText, withDirectives as _withDirectives, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"music-content\"\n};\nconst _hoisted_2 = {\n  class: \"music-info\"\n};\nconst _hoisted_3 = {\n  class: \"album-cover\"\n};\nconst _hoisted_4 = [\"src\", \"alt\"];\nconst _hoisted_5 = {\n  key: 1,\n  class: \"cover-placeholder\"\n};\nconst _hoisted_6 = {\n  class: \"sound-waves\"\n};\nconst _hoisted_7 = {\n  class: \"song-details\"\n};\nconst _hoisted_8 = {\n  class: \"song-title\"\n};\nconst _hoisted_9 = {\n  class: \"song-artist\"\n};\nconst _hoisted_10 = {\n  class: \"song-album\"\n};\nconst _hoisted_11 = {\n  class: \"progress-section\"\n};\nconst _hoisted_12 = {\n  class: \"time-display\"\n};\nconst _hoisted_13 = {\n  class: \"current-time\"\n};\nconst _hoisted_14 = {\n  class: \"total-time\"\n};\nconst _hoisted_15 = {\n  class: \"control-section\"\n};\nconst _hoisted_16 = {\n  class: \"main-controls\"\n};\nconst _hoisted_17 = {\n  class: \"secondary-controls\"\n};\nconst _hoisted_18 = {\n  class: \"volume-control\"\n};\nconst _hoisted_19 = {\n  class: \"volume-slider\"\n};\nconst _hoisted_20 = {\n  class: \"playlist-preview\"\n};\nconst _hoisted_21 = {\n  class: \"playlist-header\"\n};\nconst _hoisted_22 = {\n  class: \"playlist-items\"\n};\nconst _hoisted_23 = [\"onClick\"];\nconst _hoisted_24 = {\n  class: \"item-info\"\n};\nconst _hoisted_25 = {\n  class: \"item-title\"\n};\nconst _hoisted_26 = {\n  class: \"item-artist\"\n};\nconst _hoisted_27 = {\n  class: \"item-duration\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": \"music\",\n    size: \"large\",\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    title: '音乐控制',\n    icon: 'fas fa-music',\n    clickable: true,\n    \"show-header\": false,\n    onClick: $setup.handleCardClick,\n    class: \"music-control-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 专辑封面和歌曲信息 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [$setup.currentSong.cover ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: $setup.currentSong.cover,\n      alt: $setup.currentSong.title,\n      class: \"cover-image\"\n    }, null, 8 /* PROPS */, _hoisted_4)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[9] || (_cache[9] = [_createElementVNode(\"i\", {\n      class: \"fas fa-music\"\n    }, null, -1 /* CACHED */)]))), _createCommentVNode(\" 播放状态覆盖层 \"), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"play-overlay\", {\n        active: $setup.isPlaying\n      }])\n    }, [_createElementVNode(\"div\", _hoisted_6, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(4, i => {\n      return _createElementVNode(\"div\", {\n        class: \"wave\",\n        key: i,\n        style: _normalizeStyle({\n          animationDelay: `${i * 0.1}s`\n        })\n      }, null, 4 /* STYLE */);\n    }), 64 /* STABLE_FRAGMENT */))])], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h3\", _hoisted_8, _toDisplayString($setup.currentSong.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_9, _toDisplayString($setup.currentSong.artist), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_10, _toDisplayString($setup.currentSong.album), 1 /* TEXT */)])]), _createCommentVNode(\" 播放进度 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", _hoisted_13, _toDisplayString($setup.formatTime($setup.currentTime)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_14, _toDisplayString($setup.formatTime($setup.currentSong.duration)), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n      class: \"progress-bar\",\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.seekTo && $setup.seekTo(...args))\n    }, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n      class: \"progress-track\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: `${$setup.progressPercentage}%`\n      })\n    }, null, 4 /* STYLE */), _createElementVNode(\"div\", {\n      class: \"progress-thumb\",\n      style: _normalizeStyle({\n        left: `${$setup.progressPercentage}%`\n      })\n    }, null, 4 /* STYLE */)])]), _createCommentVNode(\" 播放控制 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", {\n      onClick: _cache[1] || (_cache[1] = (...args) => $setup.previousSong && $setup.previousSong(...args)),\n      class: \"control-btn\"\n    }, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-backward\"\n    }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n      onClick: _cache[2] || (_cache[2] = (...args) => $setup.togglePlay && $setup.togglePlay(...args)),\n      class: \"control-btn play-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.isPlaying ? 'fas fa-pause' : 'fas fa-play')\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"button\", {\n      onClick: _cache[3] || (_cache[3] = (...args) => $setup.nextSong && $setup.nextSong(...args)),\n      class: \"control-btn\"\n    }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-forward\"\n    }, null, -1 /* CACHED */)]))]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"button\", {\n      onClick: _cache[4] || (_cache[4] = (...args) => $setup.toggleShuffle && $setup.toggleShuffle(...args)),\n      class: _normalizeClass(['control-btn', {\n        active: $setup.isShuffled\n      }])\n    }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n      class: \"fas fa-random\"\n    }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n      onClick: _cache[5] || (_cache[5] = (...args) => $setup.toggleRepeat && $setup.toggleRepeat(...args)),\n      class: _normalizeClass(['control-btn', {\n        active: $setup.isRepeating\n      }])\n    }, _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n      class: \"fas fa-redo\"\n    }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"button\", {\n      onClick: _cache[6] || (_cache[6] = (...args) => $setup.toggleMute && $setup.toggleMute(...args)),\n      class: \"control-btn volume-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.volumeIcon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_19, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"range\",\n      min: \"0\",\n      max: \"100\",\n      \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.volume = $event),\n      class: \"volume-input\"\n    }, null, 512 /* NEED_PATCH */), [[_vModelText, $setup.volume]])])])])]), _createCommentVNode(\" 播放列表预览 \"), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", null, \"播放列表\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n      onClick: _cache[8] || (_cache[8] = (...args) => $setup.openFullPlaylist && $setup.openFullPlaylist(...args)),\n      class: \"playlist-btn\"\n    }, _cache[15] || (_cache[15] = [_createElementVNode(\"i\", {\n      class: \"fas fa-list\"\n    }, null, -1 /* CACHED */)]))]), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.playlist.slice(0, 3), (song, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: _normalizeClass(['playlist-item', {\n          active: $setup.currentSongIndex === index\n        }]),\n        onClick: $event => $setup.playSong(index)\n      }, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"span\", _hoisted_25, _toDisplayString(song.title), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_26, _toDisplayString(song.artist), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_27, _toDisplayString($setup.formatTime(song.duration)), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_23);\n    }), 128 /* KEYED_FRAGMENT */))])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"position\", \"theme\", \"theme-colors\", \"onClick\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "position", "$props", "theme", "themeColors", "title", "icon", "clickable", "onClick", "$setup", "handleCardClick", "_createElementVNode", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "currentSong", "cover", "_createElementBlock", "src", "alt", "_hoisted_5", "_cache", "_normalizeClass", "active", "isPlaying", "_hoisted_6", "_Fragment", "_renderList", "i", "key", "style", "_normalizeStyle", "animationDelay", "_hoisted_7", "_hoisted_8", "_toDisplayString", "_hoisted_9", "artist", "_hoisted_10", "album", "_hoisted_11", "_hoisted_12", "_hoisted_13", "formatTime", "currentTime", "_hoisted_14", "duration", "args", "seekTo", "width", "progressPercentage", "left", "_hoisted_15", "_hoisted_16", "previousSong", "togglePlay", "nextSong", "_hoisted_17", "toggleShuffle", "isShuffled", "toggleRepeat", "isRepeating", "_hoisted_18", "toggleMute", "volumeIcon", "_hoisted_19", "type", "min", "max", "volume", "$event", "_hoisted_20", "_hoisted_21", "openFullPlaylist", "_hoisted_22", "playlist", "slice", "song", "index", "currentSongIndex", "playSong", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue"], "sourcesContent": ["<template>\r\n  <BaseCard\r\n    card-type=\"music\"\r\n    size=\"large\"\r\n    :position=\"position\"\r\n    :theme=\"theme\"\r\n    :theme-colors=\"themeColors\"\r\n    :title=\"'音乐控制'\"\r\n    :icon=\"'fas fa-music'\"\r\n    :clickable=\"true\"\r\n    :show-header=\"false\"\r\n    @click=\"handleCardClick\"\r\n    class=\"music-control-card\"\r\n  >\r\n    <div class=\"music-content\">\r\n      <!-- 专辑封面和歌曲信息 -->\r\n      <div class=\"music-info\">\r\n        <div class=\"album-cover\">\r\n          <img \r\n            v-if=\"currentSong.cover\"\r\n            :src=\"currentSong.cover\"\r\n            :alt=\"currentSong.title\"\r\n            class=\"cover-image\"\r\n          />\r\n          <div v-else class=\"cover-placeholder\">\r\n            <i class=\"fas fa-music\"></i>\r\n          </div>\r\n          \r\n          <!-- 播放状态覆盖层 -->\r\n          <div class=\"play-overlay\" :class=\"{ active: isPlaying }\">\r\n            <div class=\"sound-waves\">\r\n              <div class=\"wave\" v-for=\"i in 4\" :key=\"i\" :style=\"{ animationDelay: `${i * 0.1}s` }\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"song-details\">\r\n          <h3 class=\"song-title\">{{ currentSong.title }}</h3>\r\n          <p class=\"song-artist\">{{ currentSong.artist }}</p>\r\n          <p class=\"song-album\">{{ currentSong.album }}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 播放进度 -->\r\n      <div class=\"progress-section\">\r\n        <div class=\"time-display\">\r\n          <span class=\"current-time\">{{ formatTime(currentTime) }}</span>\r\n          <span class=\"total-time\">{{ formatTime(currentSong.duration) }}</span>\r\n        </div>\r\n        <div class=\"progress-bar\" @click=\"seekTo\">\r\n          <div class=\"progress-track\"></div>\r\n          <div \r\n            class=\"progress-fill\" \r\n            :style=\"{ width: `${progressPercentage}%` }\"\r\n          ></div>\r\n          <div \r\n            class=\"progress-thumb\" \r\n            :style=\"{ left: `${progressPercentage}%` }\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 播放控制 -->\r\n      <div class=\"control-section\">\r\n        <div class=\"main-controls\">\r\n          <button @click=\"previousSong\" class=\"control-btn\">\r\n            <i class=\"fas fa-step-backward\"></i>\r\n          </button>\r\n          \r\n          <button @click=\"togglePlay\" class=\"control-btn play-btn\">\r\n            <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\r\n          </button>\r\n          \r\n          <button @click=\"nextSong\" class=\"control-btn\">\r\n            <i class=\"fas fa-step-forward\"></i>\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"secondary-controls\">\r\n          <button @click=\"toggleShuffle\" :class=\"['control-btn', { active: isShuffled }]\">\r\n            <i class=\"fas fa-random\"></i>\r\n          </button>\r\n          \r\n          <button @click=\"toggleRepeat\" :class=\"['control-btn', { active: isRepeating }]\">\r\n            <i class=\"fas fa-redo\"></i>\r\n          </button>\r\n          \r\n          <div class=\"volume-control\">\r\n            <button @click=\"toggleMute\" class=\"control-btn volume-btn\">\r\n              <i :class=\"volumeIcon\"></i>\r\n            </button>\r\n            <div class=\"volume-slider\">\r\n              <input \r\n                type=\"range\" \r\n                min=\"0\" \r\n                max=\"100\" \r\n                v-model=\"volume\"\r\n                class=\"volume-input\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 播放列表预览 -->\r\n      <div class=\"playlist-preview\">\r\n        <div class=\"playlist-header\">\r\n          <span>播放列表</span>\r\n          <button @click=\"openFullPlaylist\" class=\"playlist-btn\">\r\n            <i class=\"fas fa-list\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"playlist-items\">\r\n          <div \r\n            v-for=\"(song, index) in playlist.slice(0, 3)\" \r\n            :key=\"index\"\r\n            :class=\"['playlist-item', { active: currentSongIndex === index }]\"\r\n            @click=\"playSong(index)\"\r\n          >\r\n            <div class=\"item-info\">\r\n              <span class=\"item-title\">{{ song.title }}</span>\r\n              <span class=\"item-artist\">{{ song.artist }}</span>\r\n            </div>\r\n            <div class=\"item-duration\">{{ formatTime(song.duration) }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </BaseCard>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport BaseCard from '../BaseCard.vue'\r\n\r\nexport default {\r\n  name: 'MusicControlCard',\r\n  components: {\r\n    BaseCard\r\n  },\r\n  props: {\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 2 })\r\n    },\r\n    theme: {\r\n      type: String,\r\n      default: 'glass'\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({\r\n        primary: '#9b59b6',\r\n        secondary: '#e74c3c',\r\n        background: 'rgba(155, 89, 182, 0.1)',\r\n        text: '#ffffff'\r\n      })\r\n    }\r\n  },\r\n  \r\n  emits: ['card-click', 'song-changed', 'play-state-changed'],\r\n  \r\n  setup(props, { emit }) {\r\n    // 响应式状态\r\n    const isPlaying = ref(false)\r\n    const isShuffled = ref(false)\r\n    const isRepeating = ref(false)\r\n    const isMuted = ref(false)\r\n    const volume = ref(75)\r\n    const currentTime = ref(0)\r\n    const currentSongIndex = ref(0)\r\n    \r\n    // 播放列表数据\r\n    const playlist = ref([\r\n      {\r\n        title: '夜空中最亮的星',\r\n        artist: '逃跑计划',\r\n        album: '世界',\r\n        duration: 245,\r\n        cover: null\r\n      },\r\n      {\r\n        title: '成都',\r\n        artist: '赵雷',\r\n        album: '无法长大',\r\n        duration: 327,\r\n        cover: null\r\n      },\r\n      {\r\n        title: '南山南',\r\n        artist: '马頔',\r\n        album: '孤岛',\r\n        duration: 290,\r\n        cover: null\r\n      },\r\n      {\r\n        title: '理想',\r\n        artist: '赵雷',\r\n        album: '赵小雷',\r\n        duration: 268,\r\n        cover: null\r\n      }\r\n    ])\r\n    \r\n    // 计算属性\r\n    const currentSong = computed(() => playlist.value[currentSongIndex.value])\r\n    \r\n    const progressPercentage = computed(() => {\r\n      if (currentSong.value.duration === 0) return 0\r\n      return (currentTime.value / currentSong.value.duration) * 100\r\n    })\r\n    \r\n    const volumeIcon = computed(() => {\r\n      if (isMuted.value || volume.value === 0) {\r\n        return 'fas fa-volume-mute'\r\n      } else if (volume.value < 50) {\r\n        return 'fas fa-volume-down'\r\n      } else {\r\n        return 'fas fa-volume-up'\r\n      }\r\n    })\r\n    \r\n    // 播放控制\r\n    let playTimer = null\r\n    \r\n    const togglePlay = () => {\r\n      isPlaying.value = !isPlaying.value\r\n      \r\n      if (isPlaying.value) {\r\n        startPlayTimer()\r\n      } else {\r\n        stopPlayTimer()\r\n      }\r\n      \r\n      emit('play-state-changed', {\r\n        isPlaying: isPlaying.value,\r\n        song: currentSong.value\r\n      })\r\n    }\r\n    \r\n    const startPlayTimer = () => {\r\n      playTimer = setInterval(() => {\r\n        currentTime.value += 1\r\n        \r\n        // 歌曲播放完毕\r\n        if (currentTime.value >= currentSong.value.duration) {\r\n          if (isRepeating.value) {\r\n            currentTime.value = 0\r\n          } else {\r\n            nextSong()\r\n          }\r\n        }\r\n      }, 1000)\r\n    }\r\n    \r\n    const stopPlayTimer = () => {\r\n      if (playTimer) {\r\n        clearInterval(playTimer)\r\n        playTimer = null\r\n      }\r\n    }\r\n    \r\n    const previousSong = () => {\r\n      if (isShuffled.value) {\r\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\r\n      } else {\r\n        currentSongIndex.value = currentSongIndex.value > 0 \r\n          ? currentSongIndex.value - 1 \r\n          : playlist.value.length - 1\r\n      }\r\n      currentTime.value = 0\r\n      emit('song-changed', currentSong.value)\r\n    }\r\n    \r\n    const nextSong = () => {\r\n      if (isShuffled.value) {\r\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\r\n      } else {\r\n        currentSongIndex.value = currentSongIndex.value < playlist.value.length - 1 \r\n          ? currentSongIndex.value + 1 \r\n          : 0\r\n      }\r\n      currentTime.value = 0\r\n      emit('song-changed', currentSong.value)\r\n    }\r\n    \r\n    const playSong = (index) => {\r\n      currentSongIndex.value = index\r\n      currentTime.value = 0\r\n      if (!isPlaying.value) {\r\n        togglePlay()\r\n      }\r\n      emit('song-changed', currentSong.value)\r\n    }\r\n    \r\n    const seekTo = (event) => {\r\n      const progressBar = event.currentTarget\r\n      const rect = progressBar.getBoundingClientRect()\r\n      const clickX = event.clientX - rect.left\r\n      const percentage = clickX / rect.width\r\n      currentTime.value = Math.floor(percentage * currentSong.value.duration)\r\n    }\r\n    \r\n    const toggleShuffle = () => {\r\n      isShuffled.value = !isShuffled.value\r\n    }\r\n    \r\n    const toggleRepeat = () => {\r\n      isRepeating.value = !isRepeating.value\r\n    }\r\n    \r\n    const toggleMute = () => {\r\n      isMuted.value = !isMuted.value\r\n    }\r\n    \r\n    const openFullPlaylist = () => {\r\n      console.log('打开完整播放列表')\r\n    }\r\n    \r\n    const handleCardClick = () => {\r\n      emit('card-click', 'music')\r\n    }\r\n    \r\n    // 工具函数\r\n    const formatTime = (seconds) => {\r\n      const mins = Math.floor(seconds / 60)\r\n      const secs = seconds % 60\r\n      return `${mins}:${secs.toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 生命周期\r\n    onMounted(() => {\r\n      console.log('音乐控制卡片已加载')\r\n    })\r\n    \r\n    onUnmounted(() => {\r\n      stopPlayTimer()\r\n    })\r\n    \r\n    return {\r\n      isPlaying,\r\n      isShuffled,\r\n      isRepeating,\r\n      isMuted,\r\n      volume,\r\n      currentTime,\r\n      currentSongIndex,\r\n      playlist,\r\n      currentSong,\r\n      progressPercentage,\r\n      volumeIcon,\r\n      togglePlay,\r\n      previousSong,\r\n      nextSong,\r\n      playSong,\r\n      seekTo,\r\n      toggleShuffle,\r\n      toggleRepeat,\r\n      toggleMute,\r\n      openFullPlaylist,\r\n      handleCardClick,\r\n      formatTime\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.music-control-card {\r\n  background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);\r\n}\r\n\r\n.music-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  height: 100%;\r\n}\r\n\r\n/* 音乐信息区域 */\r\n.music-info {\r\n  display: flex;\r\n  gap: 15px;\r\n  align-items: center;\r\n}\r\n\r\n.album-cover {\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.cover-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.cover-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  color: var(--card-primary-color, #9b59b6);\r\n}\r\n\r\n.play-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.play-overlay.active {\r\n  opacity: 1;\r\n}\r\n\r\n.sound-waves {\r\n  display: flex;\r\n  gap: 2px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.wave {\r\n  width: 3px;\r\n  height: 10px;\r\n  background: white;\r\n  border-radius: 2px;\r\n  animation: wave 1s infinite ease-in-out;\r\n}\r\n\r\n.song-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.song-title {\r\n  margin: 0 0 5px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: var(--card-text-color, #ffffff);\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.song-artist, .song-album {\r\n  margin: 0;\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 进度区域 */\r\n.progress-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.time-display {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 11px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n}\r\n\r\n.progress-bar {\r\n  position: relative;\r\n  height: 4px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 2px;\r\n  cursor: pointer;\r\n}\r\n\r\n.progress-track {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 2px;\r\n}\r\n\r\n.progress-fill {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  bottom: 0;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  border-radius: 2px;\r\n  transition: width 0.1s ease;\r\n}\r\n\r\n.progress-thumb {\r\n  position: absolute;\r\n  top: -4px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  border-radius: 50%;\r\n  transform: translateX(-50%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.progress-bar:hover .progress-thumb {\r\n  opacity: 1;\r\n}\r\n\r\n/* 控制区域 */\r\n.control-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.main-controls {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.control-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.control-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.control-btn.active {\r\n  background: var(--card-primary-color, #9b59b6);\r\n}\r\n\r\n.play-btn {\r\n  width: 50px;\r\n  height: 50px;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  font-size: 18px;\r\n}\r\n\r\n.play-btn:hover {\r\n  background: var(--card-secondary-color, #e74c3c);\r\n}\r\n\r\n.secondary-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.volume-control {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.volume-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n.volume-slider {\r\n  width: 60px;\r\n}\r\n\r\n.volume-input {\r\n  width: 100%;\r\n  height: 4px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 2px;\r\n  outline: none;\r\n  -webkit-appearance: none;\r\n}\r\n\r\n.volume-input::-webkit-slider-thumb {\r\n  -webkit-appearance: none;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 播放列表预览 */\r\n.playlist-preview {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 10px;\r\n  padding: 10px;\r\n  min-height: 0;\r\n}\r\n\r\n.playlist-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n}\r\n\r\n.playlist-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: var(--card-text-color, #ffffff);\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n}\r\n\r\n.playlist-items {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n}\r\n\r\n.playlist-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 6px 8px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.playlist-item:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.playlist-item.active {\r\n  background: var(--card-primary-color, #9b59b6);\r\n}\r\n\r\n.item-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.item-title {\r\n  display: block;\r\n  font-size: 11px;\r\n  color: var(--card-text-color, #ffffff);\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.item-artist {\r\n  display: block;\r\n  font-size: 10px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.7;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.item-duration {\r\n  font-size: 10px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.7;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes wave {\r\n  0%, 100% { height: 10px; }\r\n  50% { height: 20px; }\r\n}\r\n</style>\r\n"], "mappings": ";;EAcSA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;;;EAOVA,KAAK,EAAC;;;EAMXA,KAAK,EAAC;AAAa;;EAMvBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAY;;EAKpBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAY;;EAgBvBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAe;;EAcrBA,KAAK,EAAC;AAAoB;;EASxBA,KAAK,EAAC;AAAgB;;EAIpBA,KAAK,EAAC;AAAe;;EAc3BA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAiB;;EAMvBA,KAAK,EAAC;AAAgB;;;EAOlBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAClBA,KAAK,EAAC;AAAa;;EAEtBA,KAAK,EAAC;AAAe;;;uBA1HpCC,YAAA,CA+HWC,mBAAA;IA9HT,WAAS,EAAC,OAAO;IACjBC,IAAI,EAAC,OAAO;IACXC,QAAQ,EAAEC,MAAA,CAAAD,QAAQ;IAClBE,KAAK,EAAED,MAAA,CAAAC,KAAK;IACZ,cAAY,EAAED,MAAA,CAAAE,WAAW;IACzBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAE,IAAI;IACf,aAAW,EAAE,KAAK;IAClBC,OAAK,EAAEC,MAAA,CAAAC,eAAe;IACvBb,KAAK,EAAC;;sBAEN,MAiHM,CAjHNc,mBAAA,CAiHM,OAjHNC,UAiHM,GAhHJC,mBAAA,eAAkB,EAClBF,mBAAA,CAyBM,OAzBNG,UAyBM,GAxBJH,mBAAA,CAiBM,OAjBNI,UAiBM,GAfIN,MAAA,CAAAO,WAAW,CAACC,KAAK,I,cADzBC,mBAAA,CAKE;;MAHCC,GAAG,EAAEV,MAAA,CAAAO,WAAW,CAACC,KAAK;MACtBG,GAAG,EAAEX,MAAA,CAAAO,WAAW,CAACX,KAAK;MACvBR,KAAK,EAAC;0DAERqB,mBAAA,CAEM,OAFNG,UAEM,EAAAC,MAAA,QAAAA,MAAA,OADJX,mBAAA,CAA4B;MAAzBd,KAAK,EAAC;IAAc,0B,KAGzBgB,mBAAA,aAAgB,EAChBF,mBAAA,CAIM;MAJDd,KAAK,EAAA0B,eAAA,EAAC,cAAc;QAAAC,MAAA,EAAmBf,MAAA,CAAAgB;MAAS;QACnDd,mBAAA,CAEM,OAFNe,UAEM,I,cADJR,mBAAA,CAA2FS,SAAA,QAAAC,WAAA,CAA7D,CAAC,EAANC,CAAC;aAA1BlB,mBAAA,CAA2F;QAAtFd,KAAK,EAAC,MAAM;QAAiBiC,GAAG,EAAED,CAAC;QAAGE,KAAK,EAAAC,eAAA;UAAAC,cAAA,KAAuBJ,CAAC;QAAA;;yDAK9ElB,mBAAA,CAIM,OAJNuB,UAIM,GAHJvB,mBAAA,CAAmD,MAAnDwB,UAAmD,EAAAC,gBAAA,CAAzB3B,MAAA,CAAAO,WAAW,CAACX,KAAK,kBAC3CM,mBAAA,CAAmD,KAAnD0B,UAAmD,EAAAD,gBAAA,CAAzB3B,MAAA,CAAAO,WAAW,CAACsB,MAAM,kBAC5C3B,mBAAA,CAAiD,KAAjD4B,WAAiD,EAAAH,gBAAA,CAAxB3B,MAAA,CAAAO,WAAW,CAACwB,KAAK,iB,KAI9C3B,mBAAA,UAAa,EACbF,mBAAA,CAgBM,OAhBN8B,WAgBM,GAfJ9B,mBAAA,CAGM,OAHN+B,WAGM,GAFJ/B,mBAAA,CAA+D,QAA/DgC,WAA+D,EAAAP,gBAAA,CAAjC3B,MAAA,CAAAmC,UAAU,CAACnC,MAAA,CAAAoC,WAAW,mBACpDlC,mBAAA,CAAsE,QAAtEmC,WAAsE,EAAAV,gBAAA,CAA1C3B,MAAA,CAAAmC,UAAU,CAACnC,MAAA,CAAAO,WAAW,CAAC+B,QAAQ,kB,GAE7DpC,mBAAA,CAUM;MAVDd,KAAK,EAAC,cAAc;MAAEW,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAwC,MAAA,IAAAxC,MAAA,CAAAwC,MAAA,IAAAD,IAAA,CAAM;oCACtCrC,mBAAA,CAAkC;MAA7Bd,KAAK,EAAC;IAAgB,4BAC3Bc,mBAAA,CAGO;MAFLd,KAAK,EAAC,eAAe;MACpBkC,KAAK,EAAAC,eAAA;QAAAkB,KAAA,KAAczC,MAAA,CAAA0C,kBAAkB;MAAA;6BAExCxC,mBAAA,CAGO;MAFLd,KAAK,EAAC,gBAAgB;MACrBkC,KAAK,EAAAC,eAAA;QAAAoB,IAAA,KAAa3C,MAAA,CAAA0C,kBAAkB;MAAA;iCAK3CtC,mBAAA,UAAa,EACbF,mBAAA,CAuCM,OAvCN0C,WAuCM,GAtCJ1C,mBAAA,CAYM,OAZN2C,WAYM,GAXJ3C,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAA8C,YAAA,IAAA9C,MAAA,CAAA8C,YAAA,IAAAP,IAAA,CAAY;MAAEnD,KAAK,EAAC;oCAClCc,mBAAA,CAAoC;MAAjCd,KAAK,EAAC;IAAsB,0B,IAGjCc,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAA+C,UAAA,IAAA/C,MAAA,CAAA+C,UAAA,IAAAR,IAAA,CAAU;MAAEnD,KAAK,EAAC;QAChCc,mBAAA,CAA2D;MAAvDd,KAAK,EAAA0B,eAAA,CAAEd,MAAA,CAAAgB,SAAS;+BAGtBd,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAgD,QAAA,IAAAhD,MAAA,CAAAgD,QAAA,IAAAT,IAAA,CAAQ;MAAEnD,KAAK,EAAC;oCAC9Bc,mBAAA,CAAmC;MAAhCd,KAAK,EAAC;IAAqB,0B,MAIlCc,mBAAA,CAuBM,OAvBN+C,WAuBM,GAtBJ/C,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAkD,aAAA,IAAAlD,MAAA,CAAAkD,aAAA,IAAAX,IAAA,CAAa;MAAGnD,KAAK,EAAA0B,eAAA;QAAAC,MAAA,EAA4Bf,MAAA,CAAAmD;MAAU;oCACzEjD,mBAAA,CAA6B;MAA1Bd,KAAK,EAAC;IAAe,0B,mBAG1Bc,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAoD,YAAA,IAAApD,MAAA,CAAAoD,YAAA,IAAAb,IAAA,CAAY;MAAGnD,KAAK,EAAA0B,eAAA;QAAAC,MAAA,EAA4Bf,MAAA,CAAAqD;MAAW;oCACzEnD,mBAAA,CAA2B;MAAxBd,KAAK,EAAC;IAAa,0B,mBAGxBc,mBAAA,CAaM,OAbNoD,WAaM,GAZJpD,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAuD,UAAA,IAAAvD,MAAA,CAAAuD,UAAA,IAAAhB,IAAA,CAAU;MAAEnD,KAAK,EAAC;QAChCc,mBAAA,CAA2B;MAAvBd,KAAK,EAAA0B,eAAA,CAAEd,MAAA,CAAAwD,UAAU;+BAEvBtD,mBAAA,CAQM,OARNuD,WAQM,G,gBAPJvD,mBAAA,CAME;MALAwD,IAAI,EAAC,OAAO;MACZC,GAAG,EAAC,GAAG;MACPC,GAAG,EAAC,KAAK;iEACA5D,MAAA,CAAA6D,MAAM,GAAAC,MAAA;MACf1E,KAAK,EAAC;mDADGY,MAAA,CAAA6D,MAAM,E,WAQzBzD,mBAAA,YAAe,EACfF,mBAAA,CAqBM,OArBN6D,WAqBM,GApBJ7D,mBAAA,CAKM,OALN8D,WAKM,G,4BAJJ9D,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAES;MAFAH,OAAK,EAAAc,MAAA,QAAAA,MAAA,UAAA0B,IAAA,KAAEvC,MAAA,CAAAiE,gBAAA,IAAAjE,MAAA,CAAAiE,gBAAA,IAAA1B,IAAA,CAAgB;MAAEnD,KAAK,EAAC;oCACtCc,mBAAA,CAA2B;MAAxBd,KAAK,EAAC;IAAa,0B,MAG1Bc,mBAAA,CAaM,OAbNgE,WAaM,I,kBAZJzD,mBAAA,CAWMS,SAAA,QAAAC,WAAA,CAVoBnB,MAAA,CAAAmE,QAAQ,CAACC,KAAK,SAA9BC,IAAI,EAAEC,KAAK;2BADrB7D,mBAAA,CAWM;QATHY,GAAG,EAAEiD,KAAK;QACVlF,KAAK,EAAA0B,eAAA;UAAAC,MAAA,EAA8Bf,MAAA,CAAAuE,gBAAgB,KAAKD;QAAK;QAC7DvE,OAAK,EAAA+D,MAAA,IAAE9D,MAAA,CAAAwE,QAAQ,CAACF,KAAK;UAEtBpE,mBAAA,CAGM,OAHNuE,WAGM,GAFJvE,mBAAA,CAAgD,QAAhDwE,WAAgD,EAAA/C,gBAAA,CAApB0C,IAAI,CAACzE,KAAK,kBACtCM,mBAAA,CAAkD,QAAlDyE,WAAkD,EAAAhD,gBAAA,CAArB0C,IAAI,CAACxC,MAAM,iB,GAE1C3B,mBAAA,CAAgE,OAAhE0E,WAAgE,EAAAjD,gBAAA,CAAlC3B,MAAA,CAAAmC,UAAU,CAACkC,IAAI,CAAC/B,QAAQ,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}