{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createVNode as _createVNode, Fragment as _Fragment, createElementBlock as _createElementBlock, withCtx as _withCtx } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nconst _hoisted_2 = {\n  class: \"mode-toggle\"\n};\nconst _hoisted_3 = {\n  class: \"normal-mode\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_TestSceneGeneration = _resolveComponent(\"TestSceneGeneration\");\n  const _component_SceneManager = _resolveComponent(\"SceneManager\");\n  const _component_DynamicWallpaperManager = _resolveComponent(\"DynamicWallpaperManager\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 动态壁纸管理器 - 作为最底层 \"), _createVNode(_component_DynamicWallpaperManager, {\n    \"scene-prompt\": $setup.currentWallpaperPrompt,\n    \"auto-generate\": true,\n    \"enable-config\": true,\n    onWallpaperChanged: $setup.handleWallpaperChanged,\n    onColorsExtracted: $setup.handleColorsExtracted\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 测试模式切换 - 移到右下角 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"button\", {\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleMode && $setup.toggleMode(...args)),\n      class: \"toggle-btn\"\n    }, _toDisplayString($setup.isTestMode ? '🚗 切换到正常模式' : '🧪 切换到测试模式'), 1 /* TEXT */)]), _createCommentVNode(\" 测试模式 \"), $setup.isTestMode ? (_openBlock(), _createBlock(_component_TestSceneGeneration, {\n      key: 0,\n      \"theme-colors\": $setup.themeColors,\n      onColorsExtracted: $setup.handleColorsExtracted\n    }, null, 8 /* PROPS */, [\"theme-colors\", \"onColorsExtracted\"])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 正常模式 \"), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_SceneManager, {\n      \"initial-scene\": $setup.initialScene,\n      \"show-indicator\": true,\n      \"auto-switch\": $setup.autoSwitchEnabled,\n      \"theme-colors\": $setup.themeColors,\n      onSceneChanged: $setup.handleSceneChanged,\n      onWallpaperPromptReady: $setup.handleWallpaperPrompt\n    }, null, 8 /* PROPS */, [\"initial-scene\", \"auto-switch\", \"theme-colors\", \"onSceneChanged\", \"onWallpaperPromptReady\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"scene-prompt\", \"onWallpaperChanged\", \"onColorsExtracted\"])]);\n}", "map": {"version": 3, "names": ["id", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_DynamicWallpaperManager", "$setup", "currentWallpaperPrompt", "onWallpaperChanged", "handleWallpaperChanged", "onColorsExtracted", "handleColorsExtracted", "_createElementVNode", "_hoisted_2", "onClick", "_cache", "args", "toggleMode", "isTestMode", "_createBlock", "_component_TestSceneGeneration", "themeColors", "_Fragment", "key", "_hoisted_3", "_component_SceneManager", "initialScene", "autoSwitchEnabled", "onSceneChanged", "handleSceneChanged", "onWallpaperPromptReady", "handleWallpaperPrompt"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <!-- 动态壁纸管理器 - 作为最底层 -->\r\n    <DynamicWallpaperManager\r\n      :scene-prompt=\"currentWallpaperPrompt\"\r\n      :auto-generate=\"true\"\r\n      :enable-config=\"true\"\r\n      @wallpaper-changed=\"handleWallpaperChanged\"\r\n      @colors-extracted=\"handleColorsExtracted\"\r\n    >\r\n      <!-- 测试模式切换 - 移到右下角 -->\r\n      <div class=\"mode-toggle\">\r\n        <button @click=\"toggleMode\" class=\"toggle-btn\">\r\n          {{ isTestMode ? '🚗 切换到正常模式' : '🧪 切换到测试模式' }}\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 测试模式 -->\r\n      <TestSceneGeneration\r\n        v-if=\"isTestMode\"\r\n        :theme-colors=\"themeColors\"\r\n        @colors-extracted=\"handleColorsExtracted\"\r\n      />\r\n\r\n      <!-- 正常模式 -->\r\n      <div v-else class=\"normal-mode\">\r\n        <SceneManager\r\n          :initial-scene=\"initialScene\"\r\n          :show-indicator=\"true\"\r\n          :auto-switch=\"autoSwitchEnabled\"\r\n          :theme-colors=\"themeColors\"\r\n          @scene-changed=\"handleSceneChanged\"\r\n          @wallpaper-prompt-ready=\"handleWallpaperPrompt\"\r\n        />\r\n      </div>\r\n    </DynamicWallpaperManager>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, onMounted } from 'vue'\r\nimport SceneManager from './components/SceneManager.vue'\r\nimport TestSceneGeneration from './components/TestSceneGeneration.vue'\r\nimport DynamicWallpaperManager from './components/DynamicWallpaperManager.vue'\r\n\r\nexport default {\r\n  name: 'App',\r\n  components: {\r\n    SceneManager,\r\n    TestSceneGeneration,\r\n    DynamicWallpaperManager\r\n  },\r\n\r\n  setup() {\r\n    const isTestMode = ref(false) // 默认启动正常模式\r\n    const initialScene = ref('default')\r\n    const autoSwitchEnabled = ref(false) // 禁用自动切换，避免意外的场景切换\r\n    const currentWallpaperPrompt = ref('动漫卡通风格的温馨小屋，柔和的阳光，可爱的卡通元素，温馨治愈的氛围')\r\n    const themeColors = ref(null)\r\n\r\n    const toggleMode = () => {\r\n      isTestMode.value = !isTestMode.value\r\n    }\r\n\r\n    const handleWallpaperPrompt = (promptData) => {\r\n      console.log('收到壁纸提示词:', promptData)\r\n      \r\n      // 处理不同格式的提示词数据\r\n      if (typeof promptData === 'string') {\r\n        currentWallpaperPrompt.value = promptData\r\n      } else if (promptData && promptData.prompt) {\r\n        // 使用生成的情感化提示词\r\n        currentWallpaperPrompt.value = promptData.prompt\r\n        \r\n        // 记录详细的生成信息\r\n        console.log('🎨 情感化提示词生成详情:', {\r\n          prompt: promptData.prompt,\r\n          scene: promptData.scene?.name,\r\n          context: promptData.context,\r\n          originalPrompt: promptData.originalPrompt\r\n        })\r\n      }\r\n    }\r\n\r\n    const handleSceneChanged = (event) => {\r\n      console.log('场景切换:', event)\r\n\r\n      // 场景切换时不再直接设置提示词\r\n      // 等待SceneManager生成情感化提示词并通过wallpaper-prompt-ready事件传递\r\n      console.log('等待情感化提示词生成...')\r\n    }\r\n\r\n    const handleWallpaperChanged = (wallpaper) => {\r\n      console.log('壁纸已更换:', wallpaper)\r\n    }\r\n\r\n    const handleColorsExtracted = (colors) => {\r\n      console.log('颜色已提取:', colors)\r\n      themeColors.value = colors\r\n    }\r\n\r\n    // 页面加载时的欢迎语音\r\n    const welcomeUser = async () => {\r\n      console.log('欢迎使用AI-HMI智能场景系统')\r\n      \r\n      // 根据时间设置初始场景\r\n      const now = new Date()\r\n      const hour = now.getHours()\r\n      \r\n      if (hour >= 7 && hour <= 9) {\r\n        initialScene.value = 'morningCommuteFamily'\r\n      } else if (hour >= 17 && hour <= 19) {\r\n        initialScene.value = 'eveningCommute'\r\n      } else if (hour >= 20 || hour <= 6) {\r\n        initialScene.value = 'rainyNight'\r\n      }\r\n    }\r\n\r\n    onMounted(() => {\r\n      welcomeUser()\r\n    })\r\n\r\n    return {\r\n      isTestMode,\r\n      toggleMode,\r\n      initialScene,\r\n      autoSwitchEnabled,\r\n      currentWallpaperPrompt,\r\n      themeColors,\r\n      handleWallpaperPrompt,\r\n      handleSceneChanged,\r\n      handleWallpaperChanged,\r\n      handleColorsExtracted\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n#app {\r\n  width: 100%;\r\n  height: 100vh;\r\n  margin: 0;\r\n  padding: 0;\r\n  font-family: 'Noto Sans', sans-serif;\r\n  overflow: hidden;\r\n  /* 移除默认背景，由DynamicWallpaperManager管理 */\r\n}\r\n\r\n.mode-toggle {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  z-index: 9999;\r\n}\r\n\r\n.toggle-btn {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 20px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #333;\r\n  font-weight: 600;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.toggle-btn:hover {\r\n  background: white;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.normal-mode {\r\n  width: 100%;\r\n  height: 100vh;\r\n}\r\n\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  padding: 0;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 全局字体引入 */\r\n@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');\r\n\r\n/* 图标库引入 */\r\n@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');\r\n</style>\r\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;EAUJC,KAAK,EAAC;AAAa;;EAcZA,KAAK,EAAC;AAAa;;;;;uBAxBnCC,mBAAA,CAmCM,OAnCNC,UAmCM,GAlCJC,mBAAA,qBAAwB,EACxBC,YAAA,CAgC0BC,kCAAA;IA/BvB,cAAY,EAAEC,MAAA,CAAAC,sBAAsB;IACpC,eAAa,EAAE,IAAI;IACnB,eAAa,EAAE,IAAI;IACnBC,kBAAiB,EAAEF,MAAA,CAAAG,sBAAsB;IACzCC,iBAAgB,EAAEJ,MAAA,CAAAK;;sBAEnB,MAAuB,CAAvBR,mBAAA,oBAAuB,EACvBS,mBAAA,CAIM,OAJNC,UAIM,GAHJD,mBAAA,CAES;MAFAE,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAW,UAAA,IAAAX,MAAA,CAAAW,UAAA,IAAAD,IAAA,CAAU;MAAEhB,KAAK,EAAC;wBAC7BM,MAAA,CAAAY,UAAU,+C,GAIjBf,mBAAA,UAAa,EAELG,MAAA,CAAAY,UAAU,I,cADlBC,YAAA,CAIEC,8BAAA;;MAFC,cAAY,EAAEd,MAAA,CAAAe,WAAW;MACzBX,iBAAgB,EAAEJ,MAAA,CAAAK;qFAIrBV,mBAAA,CASMqB,SAAA;MAAAC,GAAA;IAAA,IAVNpB,mBAAA,UAAa,EACbS,mBAAA,CASM,OATNY,UASM,GARJpB,YAAA,CAOEqB,uBAAA;MANC,eAAa,EAAEnB,MAAA,CAAAoB,YAAY;MAC3B,gBAAc,EAAE,IAAI;MACpB,aAAW,EAAEpB,MAAA,CAAAqB,iBAAiB;MAC9B,cAAY,EAAErB,MAAA,CAAAe,WAAW;MACzBO,cAAa,EAAEtB,MAAA,CAAAuB,kBAAkB;MACjCC,sBAAsB,EAAExB,MAAA,CAAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}