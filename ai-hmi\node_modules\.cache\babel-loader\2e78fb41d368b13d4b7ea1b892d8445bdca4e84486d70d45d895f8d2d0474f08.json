{"ast": null, "code": "import { defineStore } from 'pinia';\nexport const useLayoutStore = defineStore('layout', {\n  state: () => ({\n    // 16x9网格系统配置\n    gridConfig: {\n      columns: 16,\n      rows: 9,\n      gap: 'min(1vw, 10px)',\n      padding: 'min(1vw, 10px)'\n    },\n    // 当前布局模式\n    currentLayout: 'default',\n    // 可用布局模式\n    availableLayouts: {\n      default: {\n        name: '默认布局',\n        description: '标准16x9网格布局',\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\n      },\n      family: {\n        name: '家庭出行',\n        description: '适合家庭出行的布局',\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\n      },\n      focus: {\n        name: '专注通勤',\n        description: '专注工作的通勤布局',\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\n      },\n      entertainment: {\n        name: '娱乐模式',\n        description: '娱乐和休闲布局',\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\n      },\n      minimal: {\n        name: '极简模式',\n        description: '极简雨夜模式布局',\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\n      },\n      immersive: {\n        name: '沉浸式',\n        description: '沉浸式桌面壁纸布局',\n        gridTemplate: '1fr'\n      }\n    },\n    // 组件尺寸预设\n    componentSizes: {\n      // 灵动岛\n      dynamicIsland: {\n        width: 16,\n        height: 1\n      },\n      // VPA组件尺寸\n      vpaWidget: {\n        width: 2,\n        height: 2\n      },\n      vpaWidgetMedium: {\n        width: 2,\n        height: 4\n      },\n      vpaWidgetLarge: {\n        width: 3,\n        height: 3\n      },\n      vpaInteractionSmall: {\n        width: 4,\n        height: 4\n      },\n      vpaInteractionLarge: {\n        width: 8,\n        height: 9\n      },\n      // 卡片组件尺寸\n      cardSmall: {\n        width: 4,\n        height: 2\n      },\n      cardMedium: {\n        width: 8,\n        height: 4\n      },\n      cardLarge: {\n        width: 8,\n        height: 8\n      },\n      cardFull: {\n        width: 16,\n        height: 9\n      },\n      // 特定组件尺寸\n      kidEducation: {\n        width: 8,\n        height: 8\n      },\n      pedia: {\n        width: 8,\n        height: 3\n      },\n      music: {\n        width: 8,\n        height: 8\n      },\n      todo: {\n        width: 8,\n        height: 3\n      },\n      orderStatus: {\n        width: 4,\n        height: 2\n      },\n      smartHome: {\n        width: 8,\n        height: 4\n      },\n      news: {\n        width: 8,\n        height: 4\n      },\n      entertainment: {\n        width: 16,\n        height: 5\n      },\n      // 安全组件尺寸\n      fatigueWarning: {\n        width: 8,\n        height: 4\n      },\n      emergencyContact: {\n        width: 8,\n        height: 6\n      },\n      firstAid: {\n        width: 8,\n        height: 6\n      }\n    },\n    // 响应式断点\n    breakpoints: {\n      mobile: 768,\n      tablet: 1024,\n      desktop: 1440,\n      large: 1920\n    },\n    // 当前屏幕尺寸\n    screenSize: 'desktop',\n    // 布局动画配置\n    animations: {\n      duration: 0.5,\n      easing: 'power2.out',\n      stagger: 0.1\n    }\n  }),\n  getters: {\n    // 获取当前网格配置\n    currentGridConfig: state => {\n      return {\n        gridTemplateColumns: `repeat(${state.gridConfig.columns}, 1fr)`,\n        gridTemplateRows: `repeat(${state.gridConfig.rows}, 1fr)`,\n        gap: state.gridConfig.gap,\n        padding: state.gridConfig.padding\n      };\n    },\n    // 获取组件网格位置\n    getComponentPosition: state => (componentType, position = {\n      x: 1,\n      y: 1\n    }) => {\n      const size = state.componentSizes[componentType] || {\n        width: 4,\n        height: 2\n      };\n      return {\n        gridColumn: `${position.x} / ${position.x + size.width}`,\n        gridRow: `${position.y} / ${position.y + size.height}`,\n        width: '100%',\n        height: '100%',\n        minHeight: 0,\n        overflow: 'hidden'\n      };\n    },\n    // 获取响应式网格配置\n    responsiveGridConfig: state => {\n      const baseConfig = state.currentGridConfig;\n      switch (state.screenSize) {\n        case 'mobile':\n          return {\n            ...baseConfig,\n            gridTemplateColumns: 'repeat(8, 1fr)',\n            gridTemplateRows: 'repeat(12, 1fr)'\n          };\n        case 'tablet':\n          return {\n            ...baseConfig,\n            gridTemplateColumns: 'repeat(12, 1fr)',\n            gridTemplateRows: 'repeat(10, 1fr)'\n          };\n        default:\n          return baseConfig;\n      }\n    }\n  },\n  actions: {\n    // 切换布局模式\n    switchLayout(layoutName) {\n      if (this.availableLayouts[layoutName]) {\n        this.currentLayout = layoutName;\n        console.log(`布局已切换到: ${layoutName}`);\n      }\n    },\n    // 更新屏幕尺寸\n    updateScreenSize(width) {\n      if (width <= this.breakpoints.mobile) {\n        this.screenSize = 'mobile';\n      } else if (width <= this.breakpoints.tablet) {\n        this.screenSize = 'tablet';\n      } else if (width <= this.breakpoints.desktop) {\n        this.screenSize = 'desktop';\n      } else {\n        this.screenSize = 'large';\n      }\n    },\n    // 注册自定义组件尺寸\n    registerComponentSize(componentType, size) {\n      this.componentSizes[componentType] = size;\n    },\n    // 获取组件的CSS网格样式\n    getComponentGridStyle(componentType, position = {\n      x: 1,\n      y: 1\n    }) {\n      return this.getComponentPosition(componentType, position);\n    }\n  }\n});", "map": {"version": 3, "names": ["defineStore", "useLayoutStore", "state", "gridConfig", "columns", "rows", "gap", "padding", "currentLayout", "availableLayouts", "default", "name", "description", "gridTemplate", "family", "focus", "entertainment", "minimal", "immersive", "componentSizes", "dynamicIsland", "width", "height", "vpaWidget", "vpaWidgetMedium", "vpaWidgetLarge", "vpaInteractionSmall", "vpaInteractionLarge", "cardSmall", "cardMedium", "cardLarge", "cardFull", "kidEducation", "pedia", "music", "todo", "orderStatus", "smartHome", "news", "fatigueWarning", "emergencyContact", "firstAid", "breakpoints", "mobile", "tablet", "desktop", "large", "screenSize", "animations", "duration", "easing", "stagger", "getters", "currentGridConfig", "gridTemplateColumns", "gridTemplateRows", "getComponentPosition", "componentType", "position", "x", "y", "size", "gridColumn", "gridRow", "minHeight", "overflow", "responsiveGridConfig", "baseConfig", "actions", "switchLayout", "layoutName", "console", "log", "updateScreenSize", "registerComponentSize", "getComponentGridStyle"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/store/modules/layout.js"], "sourcesContent": ["import { defineStore } from 'pinia'\r\n\r\nexport const useLayoutStore = defineStore('layout', {\r\n  state: () => ({\r\n    // 16x9网格系统配置\r\n    gridConfig: {\r\n      columns: 16,\r\n      rows: 9,\r\n      gap: 'min(1vw, 10px)',\r\n      padding: 'min(1vw, 10px)'\r\n    },\r\n    \r\n    // 当前布局模式\r\n    currentLayout: 'default',\r\n    \r\n    // 可用布局模式\r\n    availableLayouts: {\r\n      default: {\r\n        name: '默认布局',\r\n        description: '标准16x9网格布局',\r\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\r\n      },\r\n      family: {\r\n        name: '家庭出行',\r\n        description: '适合家庭出行的布局',\r\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\r\n      },\r\n      focus: {\r\n        name: '专注通勤',\r\n        description: '专注工作的通勤布局',\r\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\r\n      },\r\n      entertainment: {\r\n        name: '娱乐模式',\r\n        description: '娱乐和休闲布局',\r\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\r\n      },\r\n      minimal: {\r\n        name: '极简模式',\r\n        description: '极简雨夜模式布局',\r\n        gridTemplate: 'repeat(16, 1fr) / repeat(9, 1fr)'\r\n      },\r\n      immersive: {\r\n        name: '沉浸式',\r\n        description: '沉浸式桌面壁纸布局',\r\n        gridTemplate: '1fr'\r\n      }\r\n    },\r\n    \r\n    // 组件尺寸预设\r\n    componentSizes: {\r\n      // 灵动岛\r\n      dynamicIsland: { width: 16, height: 1 },\r\n      \r\n      // VPA组件尺寸\r\n      vpaWidget: { width: 2, height: 2 },\r\n      vpaWidgetMedium: { width: 2, height: 4 },\r\n      vpaWidgetLarge: { width: 3, height: 3 },\r\n      vpaInteractionSmall: { width: 4, height: 4 },\r\n      vpaInteractionLarge: { width: 8, height: 9 },\r\n      \r\n      // 卡片组件尺寸\r\n      cardSmall: { width: 4, height: 2 },\r\n      cardMedium: { width: 8, height: 4 },\r\n      cardLarge: { width: 8, height: 8 },\r\n      cardFull: { width: 16, height: 9 },\r\n      \r\n      // 特定组件尺寸\r\n      kidEducation: { width: 8, height: 8 },\r\n      pedia: { width: 8, height: 3 },\r\n      music: { width: 8, height: 8 },\r\n      todo: { width: 8, height: 3 },\r\n      orderStatus: { width: 4, height: 2 },\r\n      smartHome: { width: 8, height: 4 },\r\n      news: { width: 8, height: 4 },\r\n      entertainment: { width: 16, height: 5 },\r\n      \r\n      // 安全组件尺寸\r\n      fatigueWarning: { width: 8, height: 4 },\r\n      emergencyContact: { width: 8, height: 6 },\r\n      firstAid: { width: 8, height: 6 }\r\n    },\r\n    \r\n    // 响应式断点\r\n    breakpoints: {\r\n      mobile: 768,\r\n      tablet: 1024,\r\n      desktop: 1440,\r\n      large: 1920\r\n    },\r\n    \r\n    // 当前屏幕尺寸\r\n    screenSize: 'desktop',\r\n    \r\n    // 布局动画配置\r\n    animations: {\r\n      duration: 0.5,\r\n      easing: 'power2.out',\r\n      stagger: 0.1\r\n    }\r\n  }),\r\n  \r\n  getters: {\r\n    // 获取当前网格配置\r\n    currentGridConfig: (state) => {\r\n      return {\r\n        gridTemplateColumns: `repeat(${state.gridConfig.columns}, 1fr)`,\r\n        gridTemplateRows: `repeat(${state.gridConfig.rows}, 1fr)`,\r\n        gap: state.gridConfig.gap,\r\n        padding: state.gridConfig.padding\r\n      }\r\n    },\r\n    \r\n    // 获取组件网格位置\r\n    getComponentPosition: (state) => (componentType, position = { x: 1, y: 1 }) => {\r\n      const size = state.componentSizes[componentType] || { width: 4, height: 2 }\r\n      return {\r\n        gridColumn: `${position.x} / ${position.x + size.width}`,\r\n        gridRow: `${position.y} / ${position.y + size.height}`,\r\n        width: '100%',\r\n        height: '100%',\r\n        minHeight: 0,\r\n        overflow: 'hidden'\r\n      }\r\n    },\r\n    \r\n    // 获取响应式网格配置\r\n    responsiveGridConfig: (state) => {\r\n      const baseConfig = state.currentGridConfig\r\n      \r\n      switch (state.screenSize) {\r\n        case 'mobile':\r\n          return {\r\n            ...baseConfig,\r\n            gridTemplateColumns: 'repeat(8, 1fr)',\r\n            gridTemplateRows: 'repeat(12, 1fr)'\r\n          }\r\n        case 'tablet':\r\n          return {\r\n            ...baseConfig,\r\n            gridTemplateColumns: 'repeat(12, 1fr)',\r\n            gridTemplateRows: 'repeat(10, 1fr)'\r\n          }\r\n        default:\r\n          return baseConfig\r\n      }\r\n    }\r\n  },\r\n  \r\n  actions: {\r\n    // 切换布局模式\r\n    switchLayout(layoutName) {\r\n      if (this.availableLayouts[layoutName]) {\r\n        this.currentLayout = layoutName\r\n        console.log(`布局已切换到: ${layoutName}`)\r\n      }\r\n    },\r\n    \r\n    // 更新屏幕尺寸\r\n    updateScreenSize(width) {\r\n      if (width <= this.breakpoints.mobile) {\r\n        this.screenSize = 'mobile'\r\n      } else if (width <= this.breakpoints.tablet) {\r\n        this.screenSize = 'tablet'\r\n      } else if (width <= this.breakpoints.desktop) {\r\n        this.screenSize = 'desktop'\r\n      } else {\r\n        this.screenSize = 'large'\r\n      }\r\n    },\r\n    \r\n    // 注册自定义组件尺寸\r\n    registerComponentSize(componentType, size) {\r\n      this.componentSizes[componentType] = size\r\n    },\r\n    \r\n    // 获取组件的CSS网格样式\r\n    getComponentGridStyle(componentType, position = { x: 1, y: 1 }) {\r\n      return this.getComponentPosition(componentType, position)\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AAEnC,OAAO,MAAMC,cAAc,GAAGD,WAAW,CAAC,QAAQ,EAAE;EAClDE,KAAK,EAAEA,CAAA,MAAO;IACZ;IACAC,UAAU,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,gBAAgB;MACrBC,OAAO,EAAE;IACX,CAAC;IAED;IACAC,aAAa,EAAE,SAAS;IAExB;IACAC,gBAAgB,EAAE;MAChBC,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,YAAY;QACzBC,YAAY,EAAE;MAChB,CAAC;MACDC,MAAM,EAAE;QACNH,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,WAAW;QACxBC,YAAY,EAAE;MAChB,CAAC;MACDE,KAAK,EAAE;QACLJ,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,WAAW;QACxBC,YAAY,EAAE;MAChB,CAAC;MACDG,aAAa,EAAE;QACbL,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,SAAS;QACtBC,YAAY,EAAE;MAChB,CAAC;MACDI,OAAO,EAAE;QACPN,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,UAAU;QACvBC,YAAY,EAAE;MAChB,CAAC;MACDK,SAAS,EAAE;QACTP,IAAI,EAAE,KAAK;QACXC,WAAW,EAAE,WAAW;QACxBC,YAAY,EAAE;MAChB;IACF,CAAC;IAED;IACAM,cAAc,EAAE;MACd;MACAC,aAAa,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC;MAEvC;MACAC,SAAS,EAAE;QAAEF,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCE,eAAe,EAAE;QAAEH,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACxCG,cAAc,EAAE;QAAEJ,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACvCI,mBAAmB,EAAE;QAAEL,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC5CK,mBAAmB,EAAE;QAAEN,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAE5C;MACAM,SAAS,EAAE;QAAEP,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCO,UAAU,EAAE;QAAER,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACnCQ,SAAS,EAAE;QAAET,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCS,QAAQ,EAAE;QAAEV,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC;MAElC;MACAU,YAAY,EAAE;QAAEX,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACrCW,KAAK,EAAE;QAAEZ,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9BY,KAAK,EAAE;QAAEb,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9Ba,IAAI,EAAE;QAAEd,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC7Bc,WAAW,EAAE;QAAEf,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACpCe,SAAS,EAAE;QAAEhB,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCgB,IAAI,EAAE;QAAEjB,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC7BN,aAAa,EAAE;QAAEK,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC;MAEvC;MACAiB,cAAc,EAAE;QAAElB,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACvCkB,gBAAgB,EAAE;QAAEnB,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACzCmB,QAAQ,EAAE;QAAEpB,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE;IAClC,CAAC;IAED;IACAoB,WAAW,EAAE;MACXC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACT,CAAC;IAED;IACAC,UAAU,EAAE,SAAS;IAErB;IACAC,UAAU,EAAE;MACVC,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAE,YAAY;MACpBC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EAEFC,OAAO,EAAE;IACP;IACAC,iBAAiB,EAAGnD,KAAK,IAAK;MAC5B,OAAO;QACLoD,mBAAmB,EAAE,UAAUpD,KAAK,CAACC,UAAU,CAACC,OAAO,QAAQ;QAC/DmD,gBAAgB,EAAE,UAAUrD,KAAK,CAACC,UAAU,CAACE,IAAI,QAAQ;QACzDC,GAAG,EAAEJ,KAAK,CAACC,UAAU,CAACG,GAAG;QACzBC,OAAO,EAAEL,KAAK,CAACC,UAAU,CAACI;MAC5B,CAAC;IACH,CAAC;IAED;IACAiD,oBAAoB,EAAGtD,KAAK,IAAK,CAACuD,aAAa,EAAEC,QAAQ,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,KAAK;MAC7E,MAAMC,IAAI,GAAG3D,KAAK,CAACiB,cAAc,CAACsC,aAAa,CAAC,IAAI;QAAEpC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC3E,OAAO;QACLwC,UAAU,EAAE,GAAGJ,QAAQ,CAACC,CAAC,MAAMD,QAAQ,CAACC,CAAC,GAAGE,IAAI,CAACxC,KAAK,EAAE;QACxD0C,OAAO,EAAE,GAAGL,QAAQ,CAACE,CAAC,MAAMF,QAAQ,CAACE,CAAC,GAAGC,IAAI,CAACvC,MAAM,EAAE;QACtDD,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACd0C,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;IAED;IACAC,oBAAoB,EAAGhE,KAAK,IAAK;MAC/B,MAAMiE,UAAU,GAAGjE,KAAK,CAACmD,iBAAiB;MAE1C,QAAQnD,KAAK,CAAC6C,UAAU;QACtB,KAAK,QAAQ;UACX,OAAO;YACL,GAAGoB,UAAU;YACbb,mBAAmB,EAAE,gBAAgB;YACrCC,gBAAgB,EAAE;UACpB,CAAC;QACH,KAAK,QAAQ;UACX,OAAO;YACL,GAAGY,UAAU;YACbb,mBAAmB,EAAE,iBAAiB;YACtCC,gBAAgB,EAAE;UACpB,CAAC;QACH;UACE,OAAOY,UAAU;MACrB;IACF;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,YAAYA,CAACC,UAAU,EAAE;MACvB,IAAI,IAAI,CAAC7D,gBAAgB,CAAC6D,UAAU,CAAC,EAAE;QACrC,IAAI,CAAC9D,aAAa,GAAG8D,UAAU;QAC/BC,OAAO,CAACC,GAAG,CAAC,WAAWF,UAAU,EAAE,CAAC;MACtC;IACF,CAAC;IAED;IACAG,gBAAgBA,CAACpD,KAAK,EAAE;MACtB,IAAIA,KAAK,IAAI,IAAI,CAACqB,WAAW,CAACC,MAAM,EAAE;QACpC,IAAI,CAACI,UAAU,GAAG,QAAQ;MAC5B,CAAC,MAAM,IAAI1B,KAAK,IAAI,IAAI,CAACqB,WAAW,CAACE,MAAM,EAAE;QAC3C,IAAI,CAACG,UAAU,GAAG,QAAQ;MAC5B,CAAC,MAAM,IAAI1B,KAAK,IAAI,IAAI,CAACqB,WAAW,CAACG,OAAO,EAAE;QAC5C,IAAI,CAACE,UAAU,GAAG,SAAS;MAC7B,CAAC,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,OAAO;MAC3B;IACF,CAAC;IAED;IACA2B,qBAAqBA,CAACjB,aAAa,EAAEI,IAAI,EAAE;MACzC,IAAI,CAAC1C,cAAc,CAACsC,aAAa,CAAC,GAAGI,IAAI;IAC3C,CAAC;IAED;IACAc,qBAAqBA,CAAClB,aAAa,EAAEC,QAAQ,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,EAAE;MAC9D,OAAO,IAAI,CAACJ,oBAAoB,CAACC,aAAa,EAAEC,QAAQ,CAAC;IAC3D;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}