{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nclass ColorExtractor {\n  static async extractColors(imageUrl, fallbackPrompt = '') {\n    return new Promise(resolve => {\n      const img = new Image();\n      img.crossOrigin = 'anonymous';\n      img.onload = () => {\n        try {\n          const canvas = document.createElement('canvas');\n          const ctx = canvas.getContext('2d');\n          canvas.width = img.width;\n          canvas.height = img.height;\n          ctx.drawImage(img, 0, 0);\n          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n          const colors = this.analyzeColors(imageData);\n          console.log('颜色提取成功:', colors);\n          resolve(colors);\n        } catch (error) {\n          console.error('颜色分析失败:', error);\n          resolve(this.getSceneBasedColors(fallbackPrompt));\n        }\n      };\n      img.onerror = error => {\n        console.error('图片加载失败:', error);\n        // 根据场景提示词生成智能默认颜色\n        resolve(this.getSceneBasedColors(fallbackPrompt));\n      };\n      img.src = imageUrl;\n    });\n  }\n  static analyzeColors(imageData) {\n    const data = imageData.data;\n    const colorMap = {};\n\n    // 采样像素点\n    for (let i = 0; i < data.length; i += 16) {\n      const r = data[i];\n      const g = data[i + 1];\n      const b = data[i + 2];\n      const a = data[i + 3];\n      if (a < 128) continue;\n      const color = this.quantizeColor(r, g, b);\n      const key = `${color.r},${color.g},${color.b}`;\n      colorMap[key] = (colorMap[key] || 0) + 1;\n    }\n    const sortedColors = Object.entries(colorMap).sort(([, a], [, b]) => b - a).slice(0, 10).map(([key]) => {\n      const [r, g, b] = key.split(',').map(Number);\n      return {\n        r,\n        g,\n        b\n      };\n    });\n    return this.generateGlassmorphismPalette(sortedColors);\n  }\n  static generateGlassmorphismPalette(colors) {\n    if (colors.length === 0) return this.getDefaultGlassmorphismColors();\n    const primaryColor = colors[0];\n    const secondaryColor = colors[1] || colors[0];\n\n    // 计算背景亮度\n    const backgroundBrightness = (primaryColor.r * 299 + primaryColor.g * 587 + primaryColor.b * 114) / 1000;\n\n    // 生成高对比度的文字颜色\n    const textColor = this.getHighContrastTextColor(primaryColor);\n\n    // 生成卡片背景颜色（基于主色调整）\n    const cardBackground = this.getGlassBackground(primaryColor);\n\n    // 生成卡片边框颜色\n    const cardBorder = this.getGlassBorder(primaryColor);\n\n    // 生成按钮颜色（基于主色但确保对比度）\n    const buttonColors = this.generateButtonColors(primaryColor);\n\n    // 生成标题颜色（确保在卡片背景上可见）\n    const titleColor = this.getTitleColor(primaryColor);\n\n    // 生成内容文字颜色（确保在卡片背景上可见）\n    const contentTextColor = this.getContentTextColor(primaryColor);\n    return {\n      primary: this.rgbToHex(primaryColor),\n      secondary: this.rgbToHex(secondaryColor),\n      glassBackground: cardBackground,\n      glassBorder: cardBorder,\n      text: textColor,\n      // 卡片专用颜色\n      cardTitleColor: titleColor,\n      cardContentColor: contentTextColor,\n      // 按钮相关颜色\n      buttonBackground: buttonColors.background,\n      buttonColor: buttonColors.color,\n      buttonBorder: buttonColors.border,\n      buttonHoverBackground: buttonColors.hoverBackground,\n      buttonTextShadow: buttonColors.textShadow,\n      // 背景信息\n      backgroundBrightness: backgroundBrightness,\n      contrastRatio: this.getContrastRatio(primaryColor, textColor === '#FFFFFF' ? {\n        r: 255,\n        g: 255,\n        b: 255\n      } : {\n        r: 0,\n        g: 0,\n        b: 0\n      })\n    };\n  }\n  static getGlassBackground(color) {\n    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000;\n\n    // 根据壁纸亮度动态调整卡片背景，确保与壁纸有足够对比度\n    if (brightness > 180) {\n      // 很亮的背景：使用深色半透明卡片，增强对比度\n      return `rgba(${Math.max(0, color.r - 80)}, ${Math.max(0, color.g - 80)}, ${Math.max(0, color.b - 80)}, 0.75)`;\n    } else if (brightness > 128) {\n      // 中等亮度：使用稍深的半透明卡片\n      return `rgba(${Math.max(0, color.r - 40)}, ${Math.max(0, color.g - 40)}, ${Math.max(0, color.b - 40)}, 0.65)`;\n    } else if (brightness > 80) {\n      // 较暗：使用稍亮的半透明卡片\n      return `rgba(${Math.min(255, color.r + 40)}, ${Math.min(255, color.g + 40)}, ${Math.min(255, color.b + 40)}, 0.55)`;\n    } else {\n      // 很暗：使用明亮的半透明卡片\n      return `rgba(${Math.min(255, color.r + 80)}, ${Math.min(255, color.g + 80)}, ${Math.min(255, color.b + 80)}, 0.65)`;\n    }\n  }\n  static getGlassBorder(color) {\n    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000;\n\n    // 根据背景亮度动态调整边框颜色和透明度\n    if (brightness > 180) {\n      // 很亮的背景：使用深色边框\n      return `rgba(${Math.max(0, color.r - 100)}, ${Math.max(0, color.g - 100)}, ${Math.max(0, color.b - 100)}, 0.6)`;\n    } else if (brightness > 128) {\n      // 中等亮度：使用中性边框\n      return `rgba(${color.r}, ${color.g}, ${color.b}, 0.4)`;\n    } else {\n      // 较暗：使用亮色边框\n      return `rgba(${Math.min(255, color.r + 100)}, ${Math.min(255, color.g + 100)}, ${Math.min(255, color.b + 100)}, 0.5)`;\n    }\n  }\n\n  // 根据场景提示词生成智能颜色\n  static getSceneBasedColors(prompt = '') {\n    const lowerPrompt = prompt.toLowerCase();\n\n    // 定义场景颜色映射\n    const sceneColorMap = {\n      // 蓝色系 - 商务、科技、现代\n      blue: {\n        r: 52,\n        g: 152,\n        b: 219\n      },\n      // #3498db\n      // 绿色系 - 自然、放松、春天\n      green: {\n        r: 46,\n        g: 204,\n        b: 113\n      },\n      // #2ecc71\n      // 橙色系 - 温暖、日落、秋天\n      orange: {\n        r: 230,\n        g: 126,\n        b: 34\n      },\n      // #e67e22\n      // 紫色系 - 夜晚、神秘、优雅\n      purple: {\n        r: 155,\n        g: 89,\n        b: 182\n      },\n      // #9b59b6\n      // 红色系 - 激情、紧急、运动\n      red: {\n        r: 231,\n        g: 76,\n        b: 60\n      },\n      // #e74c3c\n      // 青色系 - 清新、海洋、冰雪\n      cyan: {\n        r: 26,\n        g: 188,\n        b: 156\n      },\n      // #1abc9c\n      // 黄色系 - 阳光、活力、明亮\n      yellow: {\n        r: 241,\n        g: 196,\n        b: 15\n      } // #f1c40f\n    };\n    let primaryColor = sceneColorMap.blue; // 默认蓝色\n\n    // 根据关键词匹配颜色\n    if (lowerPrompt.includes('sunset') || lowerPrompt.includes('warm') || lowerPrompt.includes('orange') || lowerPrompt.includes('日落') || lowerPrompt.includes('温暖')) {\n      primaryColor = sceneColorMap.orange;\n    } else if (lowerPrompt.includes('night') || lowerPrompt.includes('evening') || lowerPrompt.includes('purple') || lowerPrompt.includes('夜晚') || lowerPrompt.includes('晚上')) {\n      primaryColor = sceneColorMap.purple;\n    } else if (lowerPrompt.includes('nature') || lowerPrompt.includes('green') || lowerPrompt.includes('forest') || lowerPrompt.includes('spring') || lowerPrompt.includes('自然') || lowerPrompt.includes('绿色') || lowerPrompt.includes('春天')) {\n      primaryColor = sceneColorMap.green;\n    } else if (lowerPrompt.includes('ocean') || lowerPrompt.includes('sea') || lowerPrompt.includes('cyan') || lowerPrompt.includes('fresh') || lowerPrompt.includes('海洋') || lowerPrompt.includes('清新')) {\n      primaryColor = sceneColorMap.cyan;\n    } else if (lowerPrompt.includes('emergency') || lowerPrompt.includes('red') || lowerPrompt.includes('urgent') || lowerPrompt.includes('紧急') || lowerPrompt.includes('红色')) {\n      primaryColor = sceneColorMap.red;\n    } else if (lowerPrompt.includes('bright') || lowerPrompt.includes('sunny') || lowerPrompt.includes('yellow') || lowerPrompt.includes('明亮') || lowerPrompt.includes('阳光')) {\n      primaryColor = sceneColorMap.yellow;\n    } else if (lowerPrompt.includes('glass') || lowerPrompt.includes('modern') || lowerPrompt.includes('business') || lowerPrompt.includes('玻璃') || lowerPrompt.includes('现代') || lowerPrompt.includes('商务')) {\n      primaryColor = sceneColorMap.blue;\n    }\n    const buttonColors = this.generateButtonColors(primaryColor);\n    const cardBackground = this.getGlassBackground(primaryColor);\n    const titleColor = this.getTitleColor(primaryColor);\n    const contentTextColor = this.getContentTextColor(primaryColor);\n    const backgroundBrightness = (primaryColor.r * 299 + primaryColor.g * 587 + primaryColor.b * 114) / 1000;\n    return {\n      primary: this.rgbToHex(primaryColor),\n      secondary: this.rgbToHex(sceneColorMap.cyan),\n      // 辅助色使用青色\n      glassBackground: cardBackground,\n      glassBorder: this.getGlassBorder(primaryColor),\n      text: this.getHighContrastTextColor(primaryColor),\n      // 卡片专用颜色\n      cardTitleColor: titleColor,\n      cardContentColor: contentTextColor,\n      // 按钮相关颜色\n      buttonBackground: buttonColors.background,\n      buttonColor: buttonColors.color,\n      buttonBorder: buttonColors.border,\n      buttonHoverBackground: buttonColors.hoverBackground,\n      buttonTextShadow: buttonColors.textShadow,\n      // 背景信息\n      backgroundBrightness: backgroundBrightness,\n      contrastRatio: this.getContrastRatio(primaryColor, buttonColors.color === '#FFFFFF' ? {\n        r: 255,\n        g: 255,\n        b: 255\n      } : {\n        r: 0,\n        g: 0,\n        b: 0\n      }),\n      // 标记这是基于场景生成的颜色\n      isSceneBased: true,\n      scenePrompt: prompt\n    };\n  }\n  static getDefaultGlassmorphismColors() {\n    // 使用场景生成方法，传入默认场景\n    return this.getSceneBasedColors('modern glass building, business style');\n  }\n  static quantizeColor(r, g, b) {\n    // 颜色量化，减少颜色数量\n    const factor = 32;\n    return {\n      r: Math.round(r / factor) * factor,\n      g: Math.round(g / factor) * factor,\n      b: Math.round(b / factor) * factor\n    };\n  }\n  static rgbToHex(color) {\n    const toHex = c => {\n      const hex = c.toString(16);\n      return hex.length === 1 ? '0' + hex : hex;\n    };\n    return `#${toHex(color.r)}${toHex(color.g)}${toHex(color.b)}`;\n  }\n  static getContrastColor(color) {\n    // 使用WCAG 2.1标准计算对比色\n    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000;\n    return brightness > 128 ? '#000000' : '#FFFFFF';\n  }\n\n  // 将十六进制颜色转换为RGB对象\n  static hexToRgb(hex) {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result ? {\n      r: parseInt(result[1], 16),\n      g: parseInt(result[2], 16),\n      b: parseInt(result[3], 16)\n    } : null;\n  }\n\n  // 计算两个颜色之间的对比度比值\n  static getContrastRatio(color1, color2) {\n    const getLuminance = color => {\n      const rgb = typeof color === 'string' ? this.hexToRgb(color) : color;\n      const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {\n        c = c / 255;\n        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);\n      });\n      return 0.2126 * r + 0.7152 * g + 0.0722 * b;\n    };\n    const lum1 = getLuminance(color1);\n    const lum2 = getLuminance(color2);\n    const brightest = Math.max(lum1, lum2);\n    const darkest = Math.min(lum1, lum2);\n    return (brightest + 0.05) / (darkest + 0.05);\n  }\n\n  // 获取高对比度的文字颜色\n  static getHighContrastTextColor(backgroundColor) {\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor;\n\n    // 测试白色和黑色的对比度\n    const whiteContrast = this.getContrastRatio(bgColor, {\n      r: 255,\n      g: 255,\n      b: 255\n    });\n    const blackContrast = this.getContrastRatio(bgColor, {\n      r: 0,\n      g: 0,\n      b: 0\n    });\n\n    // WCAG AA标准要求对比度至少为4.5:1，AAA标准要求7:1\n    if (whiteContrast >= 4.5) {\n      return '#FFFFFF';\n    } else if (blackContrast >= 4.5) {\n      return '#000000';\n    } else {\n      // 如果都不满足，选择对比度更高的\n      return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';\n    }\n  }\n\n  // 生成适合按钮的高对比度颜色\n  static generateButtonColors(backgroundColor) {\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor;\n    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000;\n\n    // 根据背景亮度选择按钮策略\n    let buttonBg, textColor, borderColor, hoverBg;\n    if (brightness > 150) {\n      // 亮背景：使用深色按钮\n      buttonBg = `rgba(${Math.max(0, bgColor.r - 60)}, ${Math.max(0, bgColor.g - 60)}, ${Math.max(0, bgColor.b - 60)}, 0.85)`;\n      textColor = '#FFFFFF';\n      borderColor = 'rgba(255, 255, 255, 0.7)';\n      hoverBg = `rgba(${Math.max(0, bgColor.r - 80)}, ${Math.max(0, bgColor.g - 80)}, ${Math.max(0, bgColor.b - 80)}, 0.95)`;\n    } else if (brightness > 100) {\n      // 中等亮度：使用对比色按钮\n      buttonBg = `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, 0.8)`;\n      textColor = brightness > 128 ? '#000000' : '#FFFFFF';\n      borderColor = textColor === '#FFFFFF' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)';\n      hoverBg = `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, 0.9)`;\n    } else {\n      // 暗背景：使用亮色按钮\n      buttonBg = `rgba(${Math.min(255, bgColor.r + 60)}, ${Math.min(255, bgColor.g + 60)}, ${Math.min(255, bgColor.b + 60)}, 0.85)`;\n      textColor = '#000000';\n      borderColor = 'rgba(0, 0, 0, 0.7)';\n      hoverBg = `rgba(${Math.min(255, bgColor.r + 80)}, ${Math.min(255, bgColor.g + 80)}, ${Math.min(255, bgColor.b + 80)}, 0.95)`;\n    }\n    return {\n      background: buttonBg,\n      color: textColor,\n      border: borderColor,\n      hoverBackground: hoverBg,\n      textShadow: textColor === '#FFFFFF' ? '0 1px 2px rgba(0, 0, 0, 0.8)' : '0 1px 2px rgba(255, 255, 255, 0.8)'\n    };\n  }\n\n  // 生成卡片标题颜色\n  static getTitleColor(backgroundColor) {\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor;\n    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000;\n    if (brightness > 150) {\n      return '#1a1a1a'; // 深色标题\n    } else if (brightness > 100) {\n      return brightness > 128 ? '#2c3e50' : '#ecf0f1';\n    } else {\n      return '#ffffff'; // 白色标题\n    }\n  }\n\n  // 生成卡片内容文字颜色\n  static getContentTextColor(backgroundColor) {\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor;\n    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000;\n    if (brightness > 150) {\n      return '#34495e'; // 深灰色内容\n    } else if (brightness > 100) {\n      return brightness > 128 ? '#34495e' : '#bdc3c7';\n    } else {\n      return '#ecf0f1'; // 浅色内容\n    }\n  }\n}\nexport default ColorExtractor;", "map": {"version": 3, "names": ["ColorExtractor", "extractColors", "imageUrl", "fallback<PERSON>rompt", "Promise", "resolve", "img", "Image", "crossOrigin", "onload", "canvas", "document", "createElement", "ctx", "getContext", "width", "height", "drawImage", "imageData", "getImageData", "colors", "analyzeColors", "console", "log", "error", "getSceneBasedColors", "onerror", "src", "data", "colorMap", "i", "length", "r", "g", "b", "a", "color", "quantizeColor", "key", "sortedColors", "Object", "entries", "sort", "slice", "map", "split", "Number", "generateGlassmorphismPalette", "getDefaultGlassmorphismColors", "primaryColor", "secondaryColor", "backgroundBrightness", "textColor", "getHighContrastTextColor", "cardBackground", "getGlassBackground", "cardBorder", "getGlassBorder", "buttonColors", "generateButtonColors", "titleColor", "getTitleColor", "contentTextColor", "getContentTextColor", "primary", "rgbToHex", "secondary", "glassBackground", "glassBorder", "text", "cardTitleColor", "cardContentColor", "buttonBackground", "background", "buttonColor", "buttonBorder", "border", "buttonHoverBackground", "hoverBackground", "buttonTextShadow", "textShadow", "contrastRatio", "getContrastRatio", "brightness", "Math", "max", "min", "prompt", "lowerPrompt", "toLowerCase", "sceneColorMap", "blue", "green", "orange", "purple", "red", "cyan", "yellow", "includes", "isSceneBased", "scenePrompt", "factor", "round", "toHex", "c", "hex", "toString", "getContrastColor", "hexToRgb", "result", "exec", "parseInt", "color1", "color2", "getLuminance", "rgb", "pow", "lum1", "lum2", "brightest", "darkest", "backgroundColor", "bgColor", "whiteContrast", "blackContrast", "buttonBg", "borderColor", "hoverBg"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/utils/ColorExtractor.js"], "sourcesContent": ["class ColorExtractor {\r\n  static async extractColors(imageUrl, fallbackPrompt = '') {\r\n    return new Promise((resolve) => {\r\n      const img = new Image()\r\n      img.crossOrigin = 'anonymous'\r\n\r\n      img.onload = () => {\r\n        try {\r\n          const canvas = document.createElement('canvas')\r\n          const ctx = canvas.getContext('2d')\r\n\r\n          canvas.width = img.width\r\n          canvas.height = img.height\r\n          ctx.drawImage(img, 0, 0)\r\n\r\n          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)\r\n          const colors = this.analyzeColors(imageData)\r\n\r\n          console.log('颜色提取成功:', colors)\r\n          resolve(colors)\r\n        } catch (error) {\r\n          console.error('颜色分析失败:', error)\r\n          resolve(this.getSceneBasedColors(fallbackPrompt))\r\n        }\r\n      }\r\n\r\n      img.onerror = (error) => {\r\n        console.error('图片加载失败:', error)\r\n        // 根据场景提示词生成智能默认颜色\r\n        resolve(this.getSceneBasedColors(fallbackPrompt))\r\n      }\r\n\r\n      img.src = imageUrl\r\n    })\r\n  }\r\n\r\n  static analyzeColors(imageData) {\r\n    const data = imageData.data\r\n    const colorMap = {}\r\n    \r\n    // 采样像素点\r\n    for (let i = 0; i < data.length; i += 16) {\r\n      const r = data[i]\r\n      const g = data[i + 1]\r\n      const b = data[i + 2]\r\n      const a = data[i + 3]\r\n      \r\n      if (a < 128) continue\r\n      \r\n      const color = this.quantizeColor(r, g, b)\r\n      const key = `${color.r},${color.g},${color.b}`\r\n      \r\n      colorMap[key] = (colorMap[key] || 0) + 1\r\n    }\r\n    \r\n    const sortedColors = Object.entries(colorMap)\r\n      .sort(([,a], [,b]) => b - a)\r\n      .slice(0, 10)\r\n      .map(([key]) => {\r\n        const [r, g, b] = key.split(',').map(Number)\r\n        return { r, g, b }\r\n      })\r\n    \r\n    return this.generateGlassmorphismPalette(sortedColors)\r\n  }\r\n\r\n  static generateGlassmorphismPalette(colors) {\r\n    if (colors.length === 0) return this.getDefaultGlassmorphismColors()\r\n\r\n    const primaryColor = colors[0]\r\n    const secondaryColor = colors[1] || colors[0]\r\n\r\n    // 计算背景亮度\r\n    const backgroundBrightness = (primaryColor.r * 299 + primaryColor.g * 587 + primaryColor.b * 114) / 1000\r\n\r\n    // 生成高对比度的文字颜色\r\n    const textColor = this.getHighContrastTextColor(primaryColor)\r\n\r\n    // 生成卡片背景颜色（基于主色调整）\r\n    const cardBackground = this.getGlassBackground(primaryColor)\r\n\r\n    // 生成卡片边框颜色\r\n    const cardBorder = this.getGlassBorder(primaryColor)\r\n\r\n    // 生成按钮颜色（基于主色但确保对比度）\r\n    const buttonColors = this.generateButtonColors(primaryColor)\r\n\r\n    // 生成标题颜色（确保在卡片背景上可见）\r\n    const titleColor = this.getTitleColor(primaryColor)\r\n\r\n    // 生成内容文字颜色（确保在卡片背景上可见）\r\n    const contentTextColor = this.getContentTextColor(primaryColor)\r\n\r\n    return {\r\n      primary: this.rgbToHex(primaryColor),\r\n      secondary: this.rgbToHex(secondaryColor),\r\n      glassBackground: cardBackground,\r\n      glassBorder: cardBorder,\r\n      text: textColor,\r\n      // 卡片专用颜色\r\n      cardTitleColor: titleColor,\r\n      cardContentColor: contentTextColor,\r\n      // 按钮相关颜色\r\n      buttonBackground: buttonColors.background,\r\n      buttonColor: buttonColors.color,\r\n      buttonBorder: buttonColors.border,\r\n      buttonHoverBackground: buttonColors.hoverBackground,\r\n      buttonTextShadow: buttonColors.textShadow,\r\n      // 背景信息\r\n      backgroundBrightness: backgroundBrightness,\r\n      contrastRatio: this.getContrastRatio(primaryColor, textColor === '#FFFFFF' ? { r: 255, g: 255, b: 255 } : { r: 0, g: 0, b: 0 })\r\n    }\r\n  }\r\n\r\n  static getGlassBackground(color) {\r\n    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000\r\n\r\n    // 根据壁纸亮度动态调整卡片背景，确保与壁纸有足够对比度\r\n    if (brightness > 180) {\r\n      // 很亮的背景：使用深色半透明卡片，增强对比度\r\n      return `rgba(${Math.max(0, color.r - 80)}, ${Math.max(0, color.g - 80)}, ${Math.max(0, color.b - 80)}, 0.75)`\r\n    } else if (brightness > 128) {\r\n      // 中等亮度：使用稍深的半透明卡片\r\n      return `rgba(${Math.max(0, color.r - 40)}, ${Math.max(0, color.g - 40)}, ${Math.max(0, color.b - 40)}, 0.65)`\r\n    } else if (brightness > 80) {\r\n      // 较暗：使用稍亮的半透明卡片\r\n      return `rgba(${Math.min(255, color.r + 40)}, ${Math.min(255, color.g + 40)}, ${Math.min(255, color.b + 40)}, 0.55)`\r\n    } else {\r\n      // 很暗：使用明亮的半透明卡片\r\n      return `rgba(${Math.min(255, color.r + 80)}, ${Math.min(255, color.g + 80)}, ${Math.min(255, color.b + 80)}, 0.65)`\r\n    }\r\n  }\r\n\r\n  static getGlassBorder(color) {\r\n    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000\r\n\r\n    // 根据背景亮度动态调整边框颜色和透明度\r\n    if (brightness > 180) {\r\n      // 很亮的背景：使用深色边框\r\n      return `rgba(${Math.max(0, color.r - 100)}, ${Math.max(0, color.g - 100)}, ${Math.max(0, color.b - 100)}, 0.6)`\r\n    } else if (brightness > 128) {\r\n      // 中等亮度：使用中性边框\r\n      return `rgba(${color.r}, ${color.g}, ${color.b}, 0.4)`\r\n    } else {\r\n      // 较暗：使用亮色边框\r\n      return `rgba(${Math.min(255, color.r + 100)}, ${Math.min(255, color.g + 100)}, ${Math.min(255, color.b + 100)}, 0.5)`\r\n    }\r\n  }\r\n\r\n  // 根据场景提示词生成智能颜色\r\n  static getSceneBasedColors(prompt = '') {\r\n    const lowerPrompt = prompt.toLowerCase()\r\n\r\n    // 定义场景颜色映射\r\n    const sceneColorMap = {\r\n      // 蓝色系 - 商务、科技、现代\r\n      blue: { r: 52, g: 152, b: 219 }, // #3498db\r\n      // 绿色系 - 自然、放松、春天\r\n      green: { r: 46, g: 204, b: 113 }, // #2ecc71\r\n      // 橙色系 - 温暖、日落、秋天\r\n      orange: { r: 230, g: 126, b: 34 }, // #e67e22\r\n      // 紫色系 - 夜晚、神秘、优雅\r\n      purple: { r: 155, g: 89, b: 182 }, // #9b59b6\r\n      // 红色系 - 激情、紧急、运动\r\n      red: { r: 231, g: 76, b: 60 }, // #e74c3c\r\n      // 青色系 - 清新、海洋、冰雪\r\n      cyan: { r: 26, g: 188, b: 156 }, // #1abc9c\r\n      // 黄色系 - 阳光、活力、明亮\r\n      yellow: { r: 241, g: 196, b: 15 } // #f1c40f\r\n    }\r\n\r\n    let primaryColor = sceneColorMap.blue // 默认蓝色\r\n\r\n    // 根据关键词匹配颜色\r\n    if (lowerPrompt.includes('sunset') || lowerPrompt.includes('warm') || lowerPrompt.includes('orange') || lowerPrompt.includes('日落') || lowerPrompt.includes('温暖')) {\r\n      primaryColor = sceneColorMap.orange\r\n    } else if (lowerPrompt.includes('night') || lowerPrompt.includes('evening') || lowerPrompt.includes('purple') || lowerPrompt.includes('夜晚') || lowerPrompt.includes('晚上')) {\r\n      primaryColor = sceneColorMap.purple\r\n    } else if (lowerPrompt.includes('nature') || lowerPrompt.includes('green') || lowerPrompt.includes('forest') || lowerPrompt.includes('spring') || lowerPrompt.includes('自然') || lowerPrompt.includes('绿色') || lowerPrompt.includes('春天')) {\r\n      primaryColor = sceneColorMap.green\r\n    } else if (lowerPrompt.includes('ocean') || lowerPrompt.includes('sea') || lowerPrompt.includes('cyan') || lowerPrompt.includes('fresh') || lowerPrompt.includes('海洋') || lowerPrompt.includes('清新')) {\r\n      primaryColor = sceneColorMap.cyan\r\n    } else if (lowerPrompt.includes('emergency') || lowerPrompt.includes('red') || lowerPrompt.includes('urgent') || lowerPrompt.includes('紧急') || lowerPrompt.includes('红色')) {\r\n      primaryColor = sceneColorMap.red\r\n    } else if (lowerPrompt.includes('bright') || lowerPrompt.includes('sunny') || lowerPrompt.includes('yellow') || lowerPrompt.includes('明亮') || lowerPrompt.includes('阳光')) {\r\n      primaryColor = sceneColorMap.yellow\r\n    } else if (lowerPrompt.includes('glass') || lowerPrompt.includes('modern') || lowerPrompt.includes('business') || lowerPrompt.includes('玻璃') || lowerPrompt.includes('现代') || lowerPrompt.includes('商务')) {\r\n      primaryColor = sceneColorMap.blue\r\n    }\r\n\r\n    const buttonColors = this.generateButtonColors(primaryColor)\r\n    const cardBackground = this.getGlassBackground(primaryColor)\r\n    const titleColor = this.getTitleColor(primaryColor)\r\n    const contentTextColor = this.getContentTextColor(primaryColor)\r\n    const backgroundBrightness = (primaryColor.r * 299 + primaryColor.g * 587 + primaryColor.b * 114) / 1000\r\n\r\n    return {\r\n      primary: this.rgbToHex(primaryColor),\r\n      secondary: this.rgbToHex(sceneColorMap.cyan), // 辅助色使用青色\r\n      glassBackground: cardBackground,\r\n      glassBorder: this.getGlassBorder(primaryColor),\r\n      text: this.getHighContrastTextColor(primaryColor),\r\n      // 卡片专用颜色\r\n      cardTitleColor: titleColor,\r\n      cardContentColor: contentTextColor,\r\n      // 按钮相关颜色\r\n      buttonBackground: buttonColors.background,\r\n      buttonColor: buttonColors.color,\r\n      buttonBorder: buttonColors.border,\r\n      buttonHoverBackground: buttonColors.hoverBackground,\r\n      buttonTextShadow: buttonColors.textShadow,\r\n      // 背景信息\r\n      backgroundBrightness: backgroundBrightness,\r\n      contrastRatio: this.getContrastRatio(primaryColor, buttonColors.color === '#FFFFFF' ? { r: 255, g: 255, b: 255 } : { r: 0, g: 0, b: 0 }),\r\n      // 标记这是基于场景生成的颜色\r\n      isSceneBased: true,\r\n      scenePrompt: prompt\r\n    }\r\n  }\r\n\r\n  static getDefaultGlassmorphismColors() {\r\n    // 使用场景生成方法，传入默认场景\r\n    return this.getSceneBasedColors('modern glass building, business style')\r\n  }\r\n\r\n  static quantizeColor(r, g, b) {\r\n    // 颜色量化，减少颜色数量\r\n    const factor = 32\r\n    return {\r\n      r: Math.round(r / factor) * factor,\r\n      g: Math.round(g / factor) * factor,\r\n      b: Math.round(b / factor) * factor\r\n    }\r\n  }\r\n\r\n  static rgbToHex(color) {\r\n    const toHex = (c) => {\r\n      const hex = c.toString(16)\r\n      return hex.length === 1 ? '0' + hex : hex\r\n    }\r\n    return `#${toHex(color.r)}${toHex(color.g)}${toHex(color.b)}`\r\n  }\r\n\r\n  static getContrastColor(color) {\r\n    // 使用WCAG 2.1标准计算对比色\r\n    const brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000\r\n    return brightness > 128 ? '#000000' : '#FFFFFF'\r\n  }\r\n\r\n  // 将十六进制颜色转换为RGB对象\r\n  static hexToRgb(hex) {\r\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex)\r\n    return result ? {\r\n      r: parseInt(result[1], 16),\r\n      g: parseInt(result[2], 16),\r\n      b: parseInt(result[3], 16)\r\n    } : null\r\n  }\r\n\r\n  // 计算两个颜色之间的对比度比值\r\n  static getContrastRatio(color1, color2) {\r\n    const getLuminance = (color) => {\r\n      const rgb = typeof color === 'string' ? this.hexToRgb(color) : color\r\n      const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {\r\n        c = c / 255\r\n        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)\r\n      })\r\n      return 0.2126 * r + 0.7152 * g + 0.0722 * b\r\n    }\r\n\r\n    const lum1 = getLuminance(color1)\r\n    const lum2 = getLuminance(color2)\r\n    const brightest = Math.max(lum1, lum2)\r\n    const darkest = Math.min(lum1, lum2)\r\n\r\n    return (brightest + 0.05) / (darkest + 0.05)\r\n  }\r\n\r\n  // 获取高对比度的文字颜色\r\n  static getHighContrastTextColor(backgroundColor) {\r\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor\r\n\r\n    // 测试白色和黑色的对比度\r\n    const whiteContrast = this.getContrastRatio(bgColor, { r: 255, g: 255, b: 255 })\r\n    const blackContrast = this.getContrastRatio(bgColor, { r: 0, g: 0, b: 0 })\r\n\r\n    // WCAG AA标准要求对比度至少为4.5:1，AAA标准要求7:1\r\n    if (whiteContrast >= 4.5) {\r\n      return '#FFFFFF'\r\n    } else if (blackContrast >= 4.5) {\r\n      return '#000000'\r\n    } else {\r\n      // 如果都不满足，选择对比度更高的\r\n      return whiteContrast > blackContrast ? '#FFFFFF' : '#000000'\r\n    }\r\n  }\r\n\r\n  // 生成适合按钮的高对比度颜色\r\n  static generateButtonColors(backgroundColor) {\r\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor\r\n    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000\r\n\r\n    // 根据背景亮度选择按钮策略\r\n    let buttonBg, textColor, borderColor, hoverBg\r\n\r\n    if (brightness > 150) {\r\n      // 亮背景：使用深色按钮\r\n      buttonBg = `rgba(${Math.max(0, bgColor.r - 60)}, ${Math.max(0, bgColor.g - 60)}, ${Math.max(0, bgColor.b - 60)}, 0.85)`\r\n      textColor = '#FFFFFF'\r\n      borderColor = 'rgba(255, 255, 255, 0.7)'\r\n      hoverBg = `rgba(${Math.max(0, bgColor.r - 80)}, ${Math.max(0, bgColor.g - 80)}, ${Math.max(0, bgColor.b - 80)}, 0.95)`\r\n    } else if (brightness > 100) {\r\n      // 中等亮度：使用对比色按钮\r\n      buttonBg = `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, 0.8)`\r\n      textColor = brightness > 128 ? '#000000' : '#FFFFFF'\r\n      borderColor = textColor === '#FFFFFF' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)'\r\n      hoverBg = `rgba(${bgColor.r}, ${bgColor.g}, ${bgColor.b}, 0.9)`\r\n    } else {\r\n      // 暗背景：使用亮色按钮\r\n      buttonBg = `rgba(${Math.min(255, bgColor.r + 60)}, ${Math.min(255, bgColor.g + 60)}, ${Math.min(255, bgColor.b + 60)}, 0.85)`\r\n      textColor = '#000000'\r\n      borderColor = 'rgba(0, 0, 0, 0.7)'\r\n      hoverBg = `rgba(${Math.min(255, bgColor.r + 80)}, ${Math.min(255, bgColor.g + 80)}, ${Math.min(255, bgColor.b + 80)}, 0.95)`\r\n    }\r\n\r\n    return {\r\n      background: buttonBg,\r\n      color: textColor,\r\n      border: borderColor,\r\n      hoverBackground: hoverBg,\r\n      textShadow: textColor === '#FFFFFF'\r\n        ? '0 1px 2px rgba(0, 0, 0, 0.8)'\r\n        : '0 1px 2px rgba(255, 255, 255, 0.8)'\r\n    }\r\n  }\r\n\r\n  // 生成卡片标题颜色\r\n  static getTitleColor(backgroundColor) {\r\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor\r\n    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000\r\n\r\n    if (brightness > 150) {\r\n      return '#1a1a1a' // 深色标题\r\n    } else if (brightness > 100) {\r\n      return brightness > 128 ? '#2c3e50' : '#ecf0f1'\r\n    } else {\r\n      return '#ffffff' // 白色标题\r\n    }\r\n  }\r\n\r\n  // 生成卡片内容文字颜色\r\n  static getContentTextColor(backgroundColor) {\r\n    const bgColor = typeof backgroundColor === 'string' ? this.hexToRgb(backgroundColor) : backgroundColor\r\n    const brightness = (bgColor.r * 299 + bgColor.g * 587 + bgColor.b * 114) / 1000\r\n\r\n    if (brightness > 150) {\r\n      return '#34495e' // 深灰色内容\r\n    } else if (brightness > 100) {\r\n      return brightness > 128 ? '#34495e' : '#bdc3c7'\r\n    } else {\r\n      return '#ecf0f1' // 浅色内容\r\n    }\r\n  }\r\n}\r\n\r\nexport default ColorExtractor"], "mappings": ";;AAAA,MAAMA,cAAc,CAAC;EACnB,aAAaC,aAAaA,CAACC,QAAQ,EAAEC,cAAc,GAAG,EAAE,EAAE;IACxD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC9B,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,WAAW,GAAG,WAAW;MAE7BF,GAAG,CAACG,MAAM,GAAG,MAAM;QACjB,IAAI;UACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;UAEnCJ,MAAM,CAACK,KAAK,GAAGT,GAAG,CAACS,KAAK;UACxBL,MAAM,CAACM,MAAM,GAAGV,GAAG,CAACU,MAAM;UAC1BH,GAAG,CAACI,SAAS,CAACX,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UAExB,MAAMY,SAAS,GAAGL,GAAG,CAACM,YAAY,CAAC,CAAC,EAAE,CAAC,EAAET,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;UACrE,MAAMI,MAAM,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;UAE5CI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,MAAM,CAAC;UAC9Bf,OAAO,CAACe,MAAM,CAAC;QACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BnB,OAAO,CAAC,IAAI,CAACoB,mBAAmB,CAACtB,cAAc,CAAC,CAAC;QACnD;MACF,CAAC;MAEDG,GAAG,CAACoB,OAAO,GAAIF,KAAK,IAAK;QACvBF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B;QACAnB,OAAO,CAAC,IAAI,CAACoB,mBAAmB,CAACtB,cAAc,CAAC,CAAC;MACnD,CAAC;MAEDG,GAAG,CAACqB,GAAG,GAAGzB,QAAQ;IACpB,CAAC,CAAC;EACJ;EAEA,OAAOmB,aAAaA,CAACH,SAAS,EAAE;IAC9B,MAAMU,IAAI,GAAGV,SAAS,CAACU,IAAI;IAC3B,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,IAAI,EAAE,EAAE;MACxC,MAAME,CAAC,GAAGJ,IAAI,CAACE,CAAC,CAAC;MACjB,MAAMG,CAAC,GAAGL,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC;MACrB,MAAMI,CAAC,GAAGN,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC;MACrB,MAAMK,CAAC,GAAGP,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC;MAErB,IAAIK,CAAC,GAAG,GAAG,EAAE;MAEb,MAAMC,KAAK,GAAG,IAAI,CAACC,aAAa,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MACzC,MAAMI,GAAG,GAAG,GAAGF,KAAK,CAACJ,CAAC,IAAII,KAAK,CAACH,CAAC,IAAIG,KAAK,CAACF,CAAC,EAAE;MAE9CL,QAAQ,CAACS,GAAG,CAAC,GAAG,CAACT,QAAQ,CAACS,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C;IAEA,MAAMC,YAAY,GAAGC,MAAM,CAACC,OAAO,CAACZ,QAAQ,CAAC,CAC1Ca,IAAI,CAAC,CAAC,GAAEP,CAAC,CAAC,EAAE,GAAED,CAAC,CAAC,KAAKA,CAAC,GAAGC,CAAC,CAAC,CAC3BQ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZC,GAAG,CAAC,CAAC,CAACN,GAAG,CAAC,KAAK;MACd,MAAM,CAACN,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGI,GAAG,CAACO,KAAK,CAAC,GAAG,CAAC,CAACD,GAAG,CAACE,MAAM,CAAC;MAC5C,OAAO;QAAEd,CAAC;QAAEC,CAAC;QAAEC;MAAE,CAAC;IACpB,CAAC,CAAC;IAEJ,OAAO,IAAI,CAACa,4BAA4B,CAACR,YAAY,CAAC;EACxD;EAEA,OAAOQ,4BAA4BA,CAAC3B,MAAM,EAAE;IAC1C,IAAIA,MAAM,CAACW,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,CAACiB,6BAA6B,CAAC,CAAC;IAEpE,MAAMC,YAAY,GAAG7B,MAAM,CAAC,CAAC,CAAC;IAC9B,MAAM8B,cAAc,GAAG9B,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC;;IAE7C;IACA,MAAM+B,oBAAoB,GAAG,CAACF,YAAY,CAACjB,CAAC,GAAG,GAAG,GAAGiB,YAAY,CAAChB,CAAC,GAAG,GAAG,GAAGgB,YAAY,CAACf,CAAC,GAAG,GAAG,IAAI,IAAI;;IAExG;IACA,MAAMkB,SAAS,GAAG,IAAI,CAACC,wBAAwB,CAACJ,YAAY,CAAC;;IAE7D;IACA,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACN,YAAY,CAAC;;IAE5D;IACA,MAAMO,UAAU,GAAG,IAAI,CAACC,cAAc,CAACR,YAAY,CAAC;;IAEpD;IACA,MAAMS,YAAY,GAAG,IAAI,CAACC,oBAAoB,CAACV,YAAY,CAAC;;IAE5D;IACA,MAAMW,UAAU,GAAG,IAAI,CAACC,aAAa,CAACZ,YAAY,CAAC;;IAEnD;IACA,MAAMa,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACd,YAAY,CAAC;IAE/D,OAAO;MACLe,OAAO,EAAE,IAAI,CAACC,QAAQ,CAAChB,YAAY,CAAC;MACpCiB,SAAS,EAAE,IAAI,CAACD,QAAQ,CAACf,cAAc,CAAC;MACxCiB,eAAe,EAAEb,cAAc;MAC/Bc,WAAW,EAAEZ,UAAU;MACvBa,IAAI,EAAEjB,SAAS;MACf;MACAkB,cAAc,EAAEV,UAAU;MAC1BW,gBAAgB,EAAET,gBAAgB;MAClC;MACAU,gBAAgB,EAAEd,YAAY,CAACe,UAAU;MACzCC,WAAW,EAAEhB,YAAY,CAACtB,KAAK;MAC/BuC,YAAY,EAAEjB,YAAY,CAACkB,MAAM;MACjCC,qBAAqB,EAAEnB,YAAY,CAACoB,eAAe;MACnDC,gBAAgB,EAAErB,YAAY,CAACsB,UAAU;MACzC;MACA7B,oBAAoB,EAAEA,oBAAoB;MAC1C8B,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAACjC,YAAY,EAAEG,SAAS,KAAK,SAAS,GAAG;QAAEpB,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC,GAAG;QAAEF,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChI,CAAC;EACH;EAEA,OAAOqB,kBAAkBA,CAACnB,KAAK,EAAE;IAC/B,MAAM+C,UAAU,GAAG,CAAC/C,KAAK,CAACJ,CAAC,GAAG,GAAG,GAAGI,KAAK,CAACH,CAAC,GAAG,GAAG,GAAGG,KAAK,CAACF,CAAC,GAAG,GAAG,IAAI,IAAI;;IAEzE;IACA,IAAIiD,UAAU,GAAG,GAAG,EAAE;MACpB;MACA,OAAO,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACJ,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACH,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,SAAS;IAC/G,CAAC,MAAM,IAAIiD,UAAU,GAAG,GAAG,EAAE;MAC3B;MACA,OAAO,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACJ,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACH,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,SAAS;IAC/G,CAAC,MAAM,IAAIiD,UAAU,GAAG,EAAE,EAAE;MAC1B;MACA,OAAO,QAAQC,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACJ,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACH,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,SAAS;IACrH,CAAC,MAAM;MACL;MACA,OAAO,QAAQkD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACJ,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACH,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACF,CAAC,GAAG,EAAE,CAAC,SAAS;IACrH;EACF;EAEA,OAAOuB,cAAcA,CAACrB,KAAK,EAAE;IAC3B,MAAM+C,UAAU,GAAG,CAAC/C,KAAK,CAACJ,CAAC,GAAG,GAAG,GAAGI,KAAK,CAACH,CAAC,GAAG,GAAG,GAAGG,KAAK,CAACF,CAAC,GAAG,GAAG,IAAI,IAAI;;IAEzE;IACA,IAAIiD,UAAU,GAAG,GAAG,EAAE;MACpB;MACA,OAAO,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACJ,CAAC,GAAG,GAAG,CAAC,KAAKoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACH,CAAC,GAAG,GAAG,CAAC,KAAKmD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjD,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,QAAQ;IACjH,CAAC,MAAM,IAAIiD,UAAU,GAAG,GAAG,EAAE;MAC3B;MACA,OAAO,QAAQ/C,KAAK,CAACJ,CAAC,KAAKI,KAAK,CAACH,CAAC,KAAKG,KAAK,CAACF,CAAC,QAAQ;IACxD,CAAC,MAAM;MACL;MACA,OAAO,QAAQkD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACJ,CAAC,GAAG,GAAG,CAAC,KAAKoD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACH,CAAC,GAAG,GAAG,CAAC,KAAKmD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAElD,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,QAAQ;IACvH;EACF;;EAEA;EACA,OAAOT,mBAAmBA,CAAC8D,MAAM,GAAG,EAAE,EAAE;IACtC,MAAMC,WAAW,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;;IAExC;IACA,MAAMC,aAAa,GAAG;MACpB;MACAC,IAAI,EAAE;QAAE3D,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAAE;MACjC;MACA0D,KAAK,EAAE;QAAE5D,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAAE;MAClC;MACA2D,MAAM,EAAE;QAAE7D,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAG,CAAC;MAAE;MACnC;MACA4D,MAAM,EAAE;QAAE9D,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAI,CAAC;MAAE;MACnC;MACA6D,GAAG,EAAE;QAAE/D,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAG,CAAC;MAAE;MAC/B;MACA8D,IAAI,EAAE;QAAEhE,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAAE;MACjC;MACA+D,MAAM,EAAE;QAAEjE,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAG,CAAC,CAAC;IACpC,CAAC;IAED,IAAIe,YAAY,GAAGyC,aAAa,CAACC,IAAI,EAAC;;IAEtC;IACA,IAAIH,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,MAAM,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MAChKjD,YAAY,GAAGyC,aAAa,CAACG,MAAM;IACrC,CAAC,MAAM,IAAIL,WAAW,CAACU,QAAQ,CAAC,OAAO,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,SAAS,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACzKjD,YAAY,GAAGyC,aAAa,CAACI,MAAM;IACrC,CAAC,MAAM,IAAIN,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,OAAO,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACxOjD,YAAY,GAAGyC,aAAa,CAACE,KAAK;IACpC,CAAC,MAAM,IAAIJ,WAAW,CAACU,QAAQ,CAAC,OAAO,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,KAAK,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,MAAM,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,OAAO,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACpMjD,YAAY,GAAGyC,aAAa,CAACM,IAAI;IACnC,CAAC,MAAM,IAAIR,WAAW,CAACU,QAAQ,CAAC,WAAW,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,KAAK,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACzKjD,YAAY,GAAGyC,aAAa,CAACK,GAAG;IAClC,CAAC,MAAM,IAAIP,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,OAAO,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACxKjD,YAAY,GAAGyC,aAAa,CAACO,MAAM;IACrC,CAAC,MAAM,IAAIT,WAAW,CAACU,QAAQ,CAAC,OAAO,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,QAAQ,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,UAAU,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,IAAIV,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACxMjD,YAAY,GAAGyC,aAAa,CAACC,IAAI;IACnC;IAEA,MAAMjC,YAAY,GAAG,IAAI,CAACC,oBAAoB,CAACV,YAAY,CAAC;IAC5D,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACN,YAAY,CAAC;IAC5D,MAAMW,UAAU,GAAG,IAAI,CAACC,aAAa,CAACZ,YAAY,CAAC;IACnD,MAAMa,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACd,YAAY,CAAC;IAC/D,MAAME,oBAAoB,GAAG,CAACF,YAAY,CAACjB,CAAC,GAAG,GAAG,GAAGiB,YAAY,CAAChB,CAAC,GAAG,GAAG,GAAGgB,YAAY,CAACf,CAAC,GAAG,GAAG,IAAI,IAAI;IAExG,OAAO;MACL8B,OAAO,EAAE,IAAI,CAACC,QAAQ,CAAChB,YAAY,CAAC;MACpCiB,SAAS,EAAE,IAAI,CAACD,QAAQ,CAACyB,aAAa,CAACM,IAAI,CAAC;MAAE;MAC9C7B,eAAe,EAAEb,cAAc;MAC/Bc,WAAW,EAAE,IAAI,CAACX,cAAc,CAACR,YAAY,CAAC;MAC9CoB,IAAI,EAAE,IAAI,CAAChB,wBAAwB,CAACJ,YAAY,CAAC;MACjD;MACAqB,cAAc,EAAEV,UAAU;MAC1BW,gBAAgB,EAAET,gBAAgB;MAClC;MACAU,gBAAgB,EAAEd,YAAY,CAACe,UAAU;MACzCC,WAAW,EAAEhB,YAAY,CAACtB,KAAK;MAC/BuC,YAAY,EAAEjB,YAAY,CAACkB,MAAM;MACjCC,qBAAqB,EAAEnB,YAAY,CAACoB,eAAe;MACnDC,gBAAgB,EAAErB,YAAY,CAACsB,UAAU;MACzC;MACA7B,oBAAoB,EAAEA,oBAAoB;MAC1C8B,aAAa,EAAE,IAAI,CAACC,gBAAgB,CAACjC,YAAY,EAAES,YAAY,CAACtB,KAAK,KAAK,SAAS,GAAG;QAAEJ,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC,GAAG;QAAEF,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,CAAC;MACxI;MACAiE,YAAY,EAAE,IAAI;MAClBC,WAAW,EAAEb;IACf,CAAC;EACH;EAEA,OAAOvC,6BAA6BA,CAAA,EAAG;IACrC;IACA,OAAO,IAAI,CAACvB,mBAAmB,CAAC,uCAAuC,CAAC;EAC1E;EAEA,OAAOY,aAAaA,CAACL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC5B;IACA,MAAMmE,MAAM,GAAG,EAAE;IACjB,OAAO;MACLrE,CAAC,EAAEoD,IAAI,CAACkB,KAAK,CAACtE,CAAC,GAAGqE,MAAM,CAAC,GAAGA,MAAM;MAClCpE,CAAC,EAAEmD,IAAI,CAACkB,KAAK,CAACrE,CAAC,GAAGoE,MAAM,CAAC,GAAGA,MAAM;MAClCnE,CAAC,EAAEkD,IAAI,CAACkB,KAAK,CAACpE,CAAC,GAAGmE,MAAM,CAAC,GAAGA;IAC9B,CAAC;EACH;EAEA,OAAOpC,QAAQA,CAAC7B,KAAK,EAAE;IACrB,MAAMmE,KAAK,GAAIC,CAAC,IAAK;MACnB,MAAMC,GAAG,GAAGD,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC;MAC1B,OAAOD,GAAG,CAAC1E,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG0E,GAAG,GAAGA,GAAG;IAC3C,CAAC;IACD,OAAO,IAAIF,KAAK,CAACnE,KAAK,CAACJ,CAAC,CAAC,GAAGuE,KAAK,CAACnE,KAAK,CAACH,CAAC,CAAC,GAAGsE,KAAK,CAACnE,KAAK,CAACF,CAAC,CAAC,EAAE;EAC/D;EAEA,OAAOyE,gBAAgBA,CAACvE,KAAK,EAAE;IAC7B;IACA,MAAM+C,UAAU,GAAG,CAAC/C,KAAK,CAACJ,CAAC,GAAG,GAAG,GAAGI,KAAK,CAACH,CAAC,GAAG,GAAG,GAAGG,KAAK,CAACF,CAAC,GAAG,GAAG,IAAI,IAAI;IACzE,OAAOiD,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;EACjD;;EAEA;EACA,OAAOyB,QAAQA,CAACH,GAAG,EAAE;IACnB,MAAMI,MAAM,GAAG,2CAA2C,CAACC,IAAI,CAACL,GAAG,CAAC;IACpE,OAAOI,MAAM,GAAG;MACd7E,CAAC,EAAE+E,QAAQ,CAACF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC1B5E,CAAC,EAAE8E,QAAQ,CAACF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC1B3E,CAAC,EAAE6E,QAAQ,CAACF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;IAC3B,CAAC,GAAG,IAAI;EACV;;EAEA;EACA,OAAO3B,gBAAgBA,CAAC8B,MAAM,EAAEC,MAAM,EAAE;IACtC,MAAMC,YAAY,GAAI9E,KAAK,IAAK;MAC9B,MAAM+E,GAAG,GAAG,OAAO/E,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACwE,QAAQ,CAACxE,KAAK,CAAC,GAAGA,KAAK;MACpE,MAAM,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACiF,GAAG,CAACnF,CAAC,EAAEmF,GAAG,CAAClF,CAAC,EAAEkF,GAAG,CAACjF,CAAC,CAAC,CAACU,GAAG,CAAC4D,CAAC,IAAI;QAC/CA,CAAC,GAAGA,CAAC,GAAG,GAAG;QACX,OAAOA,CAAC,IAAI,OAAO,GAAGA,CAAC,GAAG,KAAK,GAAGpB,IAAI,CAACgC,GAAG,CAAC,CAACZ,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;MACtE,CAAC,CAAC;MACF,OAAO,MAAM,GAAGxE,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGC,CAAC;IAC7C,CAAC;IAED,MAAMmF,IAAI,GAAGH,YAAY,CAACF,MAAM,CAAC;IACjC,MAAMM,IAAI,GAAGJ,YAAY,CAACD,MAAM,CAAC;IACjC,MAAMM,SAAS,GAAGnC,IAAI,CAACC,GAAG,CAACgC,IAAI,EAAEC,IAAI,CAAC;IACtC,MAAME,OAAO,GAAGpC,IAAI,CAACE,GAAG,CAAC+B,IAAI,EAAEC,IAAI,CAAC;IAEpC,OAAO,CAACC,SAAS,GAAG,IAAI,KAAKC,OAAO,GAAG,IAAI,CAAC;EAC9C;;EAEA;EACA,OAAOnE,wBAAwBA,CAACoE,eAAe,EAAE;IAC/C,MAAMC,OAAO,GAAG,OAAOD,eAAe,KAAK,QAAQ,GAAG,IAAI,CAACb,QAAQ,CAACa,eAAe,CAAC,GAAGA,eAAe;;IAEtG;IACA,MAAME,aAAa,GAAG,IAAI,CAACzC,gBAAgB,CAACwC,OAAO,EAAE;MAAE1F,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC,CAAC;IAChF,MAAM0F,aAAa,GAAG,IAAI,CAAC1C,gBAAgB,CAACwC,OAAO,EAAE;MAAE1F,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;;IAE1E;IACA,IAAIyF,aAAa,IAAI,GAAG,EAAE;MACxB,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIC,aAAa,IAAI,GAAG,EAAE;MAC/B,OAAO,SAAS;IAClB,CAAC,MAAM;MACL;MACA,OAAOD,aAAa,GAAGC,aAAa,GAAG,SAAS,GAAG,SAAS;IAC9D;EACF;;EAEA;EACA,OAAOjE,oBAAoBA,CAAC8D,eAAe,EAAE;IAC3C,MAAMC,OAAO,GAAG,OAAOD,eAAe,KAAK,QAAQ,GAAG,IAAI,CAACb,QAAQ,CAACa,eAAe,CAAC,GAAGA,eAAe;IACtG,MAAMtC,UAAU,GAAG,CAACuC,OAAO,CAAC1F,CAAC,GAAG,GAAG,GAAG0F,OAAO,CAACzF,CAAC,GAAG,GAAG,GAAGyF,OAAO,CAACxF,CAAC,GAAG,GAAG,IAAI,IAAI;;IAE/E;IACA,IAAI2F,QAAQ,EAAEzE,SAAS,EAAE0E,WAAW,EAAEC,OAAO;IAE7C,IAAI5C,UAAU,GAAG,GAAG,EAAE;MACpB;MACA0C,QAAQ,GAAG,QAAQzC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEqC,OAAO,CAAC1F,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEqC,OAAO,CAACzF,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEqC,OAAO,CAACxF,CAAC,GAAG,EAAE,CAAC,SAAS;MACvHkB,SAAS,GAAG,SAAS;MACrB0E,WAAW,GAAG,0BAA0B;MACxCC,OAAO,GAAG,QAAQ3C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEqC,OAAO,CAAC1F,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEqC,OAAO,CAACzF,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEqC,OAAO,CAACxF,CAAC,GAAG,EAAE,CAAC,SAAS;IACxH,CAAC,MAAM,IAAIiD,UAAU,GAAG,GAAG,EAAE;MAC3B;MACA0C,QAAQ,GAAG,QAAQH,OAAO,CAAC1F,CAAC,KAAK0F,OAAO,CAACzF,CAAC,KAAKyF,OAAO,CAACxF,CAAC,QAAQ;MAChEkB,SAAS,GAAG+B,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;MACpD2C,WAAW,GAAG1E,SAAS,KAAK,SAAS,GAAG,0BAA0B,GAAG,oBAAoB;MACzF2E,OAAO,GAAG,QAAQL,OAAO,CAAC1F,CAAC,KAAK0F,OAAO,CAACzF,CAAC,KAAKyF,OAAO,CAACxF,CAAC,QAAQ;IACjE,CAAC,MAAM;MACL;MACA2F,QAAQ,GAAG,QAAQzC,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEoC,OAAO,CAAC1F,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEoC,OAAO,CAACzF,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEoC,OAAO,CAACxF,CAAC,GAAG,EAAE,CAAC,SAAS;MAC7HkB,SAAS,GAAG,SAAS;MACrB0E,WAAW,GAAG,oBAAoB;MAClCC,OAAO,GAAG,QAAQ3C,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEoC,OAAO,CAAC1F,CAAC,GAAG,EAAE,CAAC,KAAKoD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEoC,OAAO,CAACzF,CAAC,GAAG,EAAE,CAAC,KAAKmD,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEoC,OAAO,CAACxF,CAAC,GAAG,EAAE,CAAC,SAAS;IAC9H;IAEA,OAAO;MACLuC,UAAU,EAAEoD,QAAQ;MACpBzF,KAAK,EAAEgB,SAAS;MAChBwB,MAAM,EAAEkD,WAAW;MACnBhD,eAAe,EAAEiD,OAAO;MACxB/C,UAAU,EAAE5B,SAAS,KAAK,SAAS,GAC/B,8BAA8B,GAC9B;IACN,CAAC;EACH;;EAEA;EACA,OAAOS,aAAaA,CAAC4D,eAAe,EAAE;IACpC,MAAMC,OAAO,GAAG,OAAOD,eAAe,KAAK,QAAQ,GAAG,IAAI,CAACb,QAAQ,CAACa,eAAe,CAAC,GAAGA,eAAe;IACtG,MAAMtC,UAAU,GAAG,CAACuC,OAAO,CAAC1F,CAAC,GAAG,GAAG,GAAG0F,OAAO,CAACzF,CAAC,GAAG,GAAG,GAAGyF,OAAO,CAACxF,CAAC,GAAG,GAAG,IAAI,IAAI;IAE/E,IAAIiD,UAAU,GAAG,GAAG,EAAE;MACpB,OAAO,SAAS,EAAC;IACnB,CAAC,MAAM,IAAIA,UAAU,GAAG,GAAG,EAAE;MAC3B,OAAOA,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;IACjD,CAAC,MAAM;MACL,OAAO,SAAS,EAAC;IACnB;EACF;;EAEA;EACA,OAAOpB,mBAAmBA,CAAC0D,eAAe,EAAE;IAC1C,MAAMC,OAAO,GAAG,OAAOD,eAAe,KAAK,QAAQ,GAAG,IAAI,CAACb,QAAQ,CAACa,eAAe,CAAC,GAAGA,eAAe;IACtG,MAAMtC,UAAU,GAAG,CAACuC,OAAO,CAAC1F,CAAC,GAAG,GAAG,GAAG0F,OAAO,CAACzF,CAAC,GAAG,GAAG,GAAGyF,OAAO,CAACxF,CAAC,GAAG,GAAG,IAAI,IAAI;IAE/E,IAAIiD,UAAU,GAAG,GAAG,EAAE;MACpB,OAAO,SAAS,EAAC;IACnB,CAAC,MAAM,IAAIA,UAAU,GAAG,GAAG,EAAE;MAC3B,OAAOA,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;IACjD,CAAC,MAAM;MACL,OAAO,SAAS,EAAC;IACnB;EACF;AACF;AAEA,eAAenF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}