{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n// 情感化壁纸提示词生成器\n// 专为车载HMI场景设计的动态提示词生成服务\nimport LlmService from '@/services/LlmService';\nexport default class EmotionalPromptGenerator {\n  constructor() {\n    this.llmService = new LlmService();\n    this.cache = new Map();\n    this.maxCacheSize = 50;\n  }\n\n  /**\r\n   * 生成情感化的壁纸提示词\r\n   * @param {Object} sceneInfo - 场景信息\r\n   * @param {Object} context - 用户上下文信息\r\n   * @returns {Promise<string>} 生成的提示词\r\n   */\n  async generateEmotionalPrompt(sceneInfo, context = {}) {\n    const cacheKey = this.getCacheKey(sceneInfo, context);\n\n    // 检查缓存\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n    try {\n      // 构建情感化提示词生成请求\n      const emotionalPrompt = this.buildEmotionalPrompt(sceneInfo, context);\n\n      // 调用LLM生成提示词\n      const generatedPrompt = await this.llmService.generateResponse(emotionalPrompt);\n\n      // 缓存结果\n      this.cacheResult(cacheKey, generatedPrompt);\n      console.log('🎨 情感化提示词生成成功:', generatedPrompt);\n      return generatedPrompt;\n    } catch (error) {\n      console.error('情感化提示词生成失败:', error);\n      // 返回增强的默认提示词\n      return this.getFallbackPrompt(sceneInfo, context);\n    }\n  }\n\n  /**\r\n   * 构建情感化提示词生成请求\r\n   */\n  buildEmotionalPrompt(sceneInfo, context) {\n    const {\n      id: sceneId,\n      name: sceneName,\n      description: sceneDescription,\n      theme: sceneTheme\n    } = sceneInfo;\n    const {\n      timeOfDay,\n      weather,\n      mood,\n      userRole,\n      passengers,\n      destination,\n      isWeekend,\n      drivingDuration\n    } = context;\n    return `# 角色\n你是一个专业的车载HMI壁纸提示词设计师，专门为汽车智能座舱系统生成动漫卡通风格的温馨壁纸描述提示词。\n\n## 任务\n根据提供的场景信息和用户上下文，生成一个能够体现用户真实感受和心理状态的动漫卡通风格壁纸描述提示词。\n\n## 场景信息\n- 场景ID: ${sceneId}\n- 场景名称: ${sceneName}\n- 场景描述: ${sceneDescription}\n- 主题风格: ${sceneTheme}\n\n## 用户上下文\n- 时间: ${timeOfDay || '未知'}\n- 天气: ${weather || '晴朗'}\n- 用户情绪: ${mood || '平静'}\n- 用户角色: ${userRole || '车主'}\n- 同行人员: ${passengers || '独自'}\n- 目的地: ${destination || '未知'}\n- 是否周末: ${isWeekend ? '是' : '否'}\n- 驾驶时长: ${drivingDuration || '刚开始'}\n\n## 生成要求\n1. **动漫卡通风格**: 必须使用动漫、卡通、插画风格，避免写实风格\n2. **温馨氛围**: 营造温馨、舒适、治愈的视觉氛围\n3. **避免人物**: 不要生成任何人物形象，专注于场景和环境\n4. **情感共鸣**: 从车主的真实感受出发，体现当前场景下的心理状态\n5. **场景贴合**: 紧密结合车载环境和驾驶场景\n6. **视觉描述**: 提供具体、生动的视觉元素描述\n7. **氛围营造**: 通过光影、色彩、构图营造相应的氛围\n\n## 艺术风格要求\n- 🎨 **风格**: 动漫卡通、插画风格、手绘风格\n- 🌈 **色彩**: 温暖柔和的色调，避免过于强烈的对比\n- 🏠 **场景**: 可爱的小房子、温馨的车厢、自然风光等\n- 🚗 **车辆**: 卡通化的汽车设计，圆润可爱的造型\n- 🌸 **元素**: 花朵、云朵、星星、心形等温馨装饰元素\n\n## 严格禁止\n- ❌ 写实风格、照片级真实感\n- ❌ 任何人物形象（包括卡通人物）\n- ❌ 高楼大厦、现代都市建筑\n- ❌ 机械化的模板描述\n- ❌ 过于抽象或概念化的表达\n\n## 输出格式\n直接输出壁纸描述提示词，不需要额外说明。提示词应该：\n- 专注于动漫卡通风格的场景描述\n- 体现温馨治愈的情感氛围\n- 适合作为文生图模型的输入\n- 长度控制在100-200字之间\n\n请根据以上信息，生成一个动漫卡通风格的温馨壁纸描述提示词：`;\n  }\n\n  /**\r\n   * 获取场景特定的情感增强词\r\n   */\n  getSceneEmotionalEnhancements(sceneId) {\n    const enhancements = {\n      morningCommuteFamily: {\n        emotions: ['温馨', '关爱', '期待', '家庭温暖'],\n        keywords: ['晨光', '童趣', '亲子时光', '温暖守护', '卡通车厢'],\n        atmosphere: '动漫风格的温馨车厢，柔和的晨光透过车窗，可爱的玩具和书包散落其间'\n      },\n      morningCommuteFocus: {\n        emotions: ['专注', '效率', '清醒', '目标导向'],\n        keywords: ['晨光', '清新', '专注力', '简约', '卡通道路'],\n        atmosphere: '动漫风格的简约驾驶环境，清新的晨光，可爱的道路标示和简约仪表盘'\n      },\n      eveningCommute: {\n        emotions: ['放松', '满足', '期待', '归家心切'],\n        keywords: ['夕阳', '归途', '温暖', '疲劳缓解', '卡通小屋'],\n        atmosphere: '动漫风格的黄昏归途，温暖的夕阳，道路两旁可爱的卡通小屋和温暖的灯光'\n      },\n      waitingMode: {\n        emotions: ['悠闲', '放松', '享受', '宁静'],\n        keywords: ['休憩', '舒适', '轻松', '冥想', '卡通云朵'],\n        atmosphere: '动漫风格的悠闲休息场景，舒适的卡通座椅，飘浮的云朵和轻松的氛围'\n      },\n      rainyNight: {\n        emotions: ['宁静', '沉思', '安全', '温暖'],\n        keywords: ['雨滴', '夜色', '温暖灯光', '安全感', '卡通雨伞'],\n        atmosphere: '动漫风格的雨夜车厢，车窗上滑落的卡通雨滴，温暖的车内灯光和舒适的环境'\n      },\n      familyTrip: {\n        emotions: ['愉悦', '期待', '亲密', '冒险'],\n        keywords: ['阳光', '自然', '欢笑', '自由', '卡通风景'],\n        atmosphere: '动漫风格的愉快出游，阳光明媚的自然风光，卡通化的风景和可爱的车辆'\n      },\n      longDistance: {\n        emotions: ['专注', '坚韧', '自由', '思考'],\n        keywords: ['公路', '远方', '自由感', '坚持', '卡通地图'],\n        atmosphere: '动漫风格的长途驾驶，开阔的卡通道路，可爱的地图元素和自由的氛围'\n      },\n      romanticMode: {\n        emotions: ['浪漫', '亲密', '温馨', '私密'],\n        keywords: ['星光', '温暖', '浪漫氛围', '温柔', '卡通星星'],\n        atmosphere: '动漫风格的浪漫车厢，满天卡通星星，温暖的光影和温馨的氛围'\n      },\n      chargingMode: {\n        emotions: ['耐心', '期待', '科技感', '环保'],\n        keywords: ['充电', '科技', '未来感', '环保', '卡通闪电'],\n        atmosphere: '动漫风格的科技充电环境，可爱的充电元素，未来感的卡通设计'\n      }\n    };\n    return enhancements[sceneId] || {\n      emotions: ['舒适', '安全', '愉悦'],\n      keywords: ['驾驶', '舒适', '安全', '卡通元素'],\n      atmosphere: '动漫风格的舒适驾驶环境，可爱的卡通元素和温馨的氛围'\n    };\n  }\n\n  /**\r\n   * 获取降级提示词\r\n   */\n  getFallbackPrompt(sceneInfo, context) {\n    const enhancements = this.getSceneEmotionalEnhancements(sceneInfo.id);\n    const timeInfo = context.timeOfDay || 'daytime';\n    const weatherInfo = context.weather || 'clear';\n    const basePrompt = `${enhancements.atmosphere}`;\n    const emotionalWords = enhancements.emotions.join('、');\n    const visualKeywords = enhancements.keywords.join('、');\n    return `${basePrompt}, 体现${emotionalWords}的情感氛围, 包含${visualKeywords}等元素, 动漫卡通风格, 插画风格, 手绘风格, 温馨治愈, 柔和色彩, 可爱设计, 无人物, 纯场景, ${timeInfo}时光, ${weatherInfo}天气, (动漫风格:1.3), (卡通插画:1.2), (温馨治愈:1.1)`;\n  }\n\n  /**\r\n   * 生成缓存键\r\n   */\n  getCacheKey(sceneInfo, context) {\n    const contextStr = JSON.stringify({\n      sceneId: sceneInfo.id,\n      timeOfDay: context.timeOfDay,\n      weather: context.weather,\n      mood: context.mood,\n      isWeekend: context.isWeekend\n    });\n    return `prompt_${Buffer.from(contextStr).toString('base64').slice(0, 32)}`;\n  }\n\n  /**\r\n   * 缓存结果\r\n   */\n  cacheResult(key, result) {\n    if (this.cache.size >= this.maxCacheSize) {\n      // 删除最旧的缓存项\n      const firstKey = this.cache.keys().next().value;\n      this.cache.delete(firstKey);\n    }\n    this.cache.set(key, result);\n  }\n\n  /**\r\n   * 清除缓存\r\n   */\n  clearCache() {\n    this.cache.clear();\n  }\n\n  /**\r\n   * 获取缓存统计\r\n   */\n  getCacheStats() {\n    return {\n      size: this.cache.size,\n      maxSize: this.maxCacheSize,\n      hitRate: 'N/A' // 可以添加命中率统计\n    };\n  }\n\n  /**\r\n   * 根据时间获取情感化描述\r\n   */\n  getTimeEmotionalContext(hour) {\n    if (hour >= 5 && hour < 8) {\n      return {\n        timeOfDay: '清晨',\n        mood: '清新宁静',\n        lighting: '柔和的晨光',\n        atmosphere: '宁静祥和'\n      };\n    } else if (hour >= 8 && hour < 12) {\n      return {\n        timeOfDay: '上午',\n        mood: '充满活力',\n        lighting: '明亮的日光',\n        atmosphere: '活跃向上'\n      };\n    } else if (hour >= 12 && hour < 14) {\n      return {\n        timeOfDay: '中午',\n        mood: '略带疲惫',\n        lighting: '强烈的阳光',\n        atmosphere: '热情充沛'\n      };\n    } else if (hour >= 14 && hour < 17) {\n      return {\n        timeOfDay: '下午',\n        mood: '专注沉稳',\n        lighting: '倾斜的阳光',\n        atmosphere: '沉稳专注'\n      };\n    } else if (hour >= 17 && hour < 19) {\n      return {\n        timeOfDay: '傍晚',\n        mood: '期待放松',\n        lighting: '温暖的夕阳',\n        atmosphere: '温暖归家'\n      };\n    } else if (hour >= 19 && hour < 22) {\n      return {\n        timeOfDay: '夜晚',\n        mood: '宁静温馨',\n        lighting: '温暖的灯光',\n        atmosphere: '温馨舒适'\n      };\n    } else {\n      return {\n        timeOfDay: '深夜',\n        mood: '宁静安详',\n        lighting: '柔和的月光',\n        atmosphere: '宁静神秘'\n      };\n    }\n  }\n\n  /**\r\n   * 根据天气获取情感化描述\r\n   */\n  getWeatherEmotionalContext(weather) {\n    const weatherMap = {\n      'sunny': {\n        description: '晴朗',\n        mood: '愉悦开朗',\n        lighting: '明媚的阳光',\n        atmosphere: '明亮开朗'\n      },\n      'cloudy': {\n        description: '多云',\n        mood: '平静舒缓',\n        lighting: '柔和的光线',\n        atmosphere: '柔和舒适'\n      },\n      'rainy': {\n        description: '下雨',\n        mood: '宁静沉思',\n        lighting: '雨滴的折射光',\n        atmosphere: '清新宁静'\n      },\n      'night': {\n        description: '夜晚',\n        mood: '神秘安静',\n        lighting: '温暖的灯光',\n        atmosphere: '宁静神秘'\n      }\n    };\n    return weatherMap[weather] || weatherMap.sunny;\n  }\n\n  /**\r\n   * 批量生成提示词\r\n   */\n  async generateMultiplePrompts(scenes, context) {\n    const results = [];\n    for (const scene of scenes) {\n      try {\n        const prompt = await this.generateEmotionalPrompt(scene, context);\n        results.push({\n          sceneId: scene.id,\n          prompt,\n          success: true\n        });\n      } catch (error) {\n        console.error(`生成场景 ${scene.id} 提示词失败:`, error);\n        results.push({\n          sceneId: scene.id,\n          prompt: this.getFallbackPrompt(scene, context),\n          success: false,\n          error: error.message\n        });\n      }\n    }\n    return results;\n  }\n}", "map": {"version": 3, "names": ["LlmService", "EmotionalPromptGenerator", "constructor", "llmService", "cache", "Map", "maxCacheSize", "generateEmotionalPrompt", "sceneInfo", "context", "cache<PERSON>ey", "get<PERSON><PERSON><PERSON><PERSON>", "has", "get", "emotionalPrompt", "buildEmotionalPrompt", "generatedPrompt", "generateResponse", "cacheResult", "console", "log", "error", "getFallbackPrompt", "id", "sceneId", "name", "scene<PERSON><PERSON>", "description", "sceneDescription", "theme", "sceneTheme", "timeOfDay", "weather", "mood", "userRole", "passengers", "destination", "isWeekend", "drivingDuration", "getSceneEmotionalEnhancements", "enhancements", "morningCommuteFamily", "emotions", "keywords", "atmosphere", "morningCommuteFocus", "eveningCommute", "waitingMode", "rainyNight", "familyTrip", "longDistance", "<PERSON><PERSON><PERSON>", "chargingMode", "timeInfo", "weatherInfo", "basePrompt", "<PERSON><PERSON>ords", "join", "visualKeywords", "contextStr", "JSON", "stringify", "<PERSON><PERSON><PERSON>", "from", "toString", "slice", "key", "result", "size", "firstKey", "keys", "next", "value", "delete", "set", "clearCache", "clear", "getCacheStats", "maxSize", "hitRate", "getTimeEmotionalContext", "hour", "lighting", "getWeatherEmotionalContext", "weatherMap", "sunny", "generateMultiplePrompts", "scenes", "results", "scene", "prompt", "push", "success", "message"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/services/EmotionalPromptGenerator.js"], "sourcesContent": ["// 情感化壁纸提示词生成器\r\n// 专为车载HMI场景设计的动态提示词生成服务\r\nimport LlmService from '@/services/LlmService'\r\n\r\nexport default class EmotionalPromptGenerator {\r\n  constructor() {\r\n    this.llmService = new LlmService()\r\n    this.cache = new Map()\r\n    this.maxCacheSize = 50\r\n  }\r\n\r\n  /**\r\n   * 生成情感化的壁纸提示词\r\n   * @param {Object} sceneInfo - 场景信息\r\n   * @param {Object} context - 用户上下文信息\r\n   * @returns {Promise<string>} 生成的提示词\r\n   */\r\n  async generateEmotionalPrompt(sceneInfo, context = {}) {\r\n    const cacheKey = this.getCacheKey(sceneInfo, context)\r\n    \r\n    // 检查缓存\r\n    if (this.cache.has(cacheKey)) {\r\n      return this.cache.get(cacheKey)\r\n    }\r\n\r\n    try {\r\n      // 构建情感化提示词生成请求\r\n      const emotionalPrompt = this.buildEmotionalPrompt(sceneInfo, context)\r\n      \r\n      // 调用LLM生成提示词\r\n      const generatedPrompt = await this.llmService.generateResponse(emotionalPrompt)\r\n      \r\n      // 缓存结果\r\n      this.cacheResult(cacheKey, generatedPrompt)\r\n      \r\n      console.log('🎨 情感化提示词生成成功:', generatedPrompt)\r\n      return generatedPrompt\r\n      \r\n    } catch (error) {\r\n      console.error('情感化提示词生成失败:', error)\r\n      // 返回增强的默认提示词\r\n      return this.getFallbackPrompt(sceneInfo, context)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 构建情感化提示词生成请求\r\n   */\r\n  buildEmotionalPrompt(sceneInfo, context) {\r\n    const { \r\n      id: sceneId, \r\n      name: sceneName, \r\n      description: sceneDescription,\r\n      theme: sceneTheme \r\n    } = sceneInfo\r\n\r\n    const {\r\n      timeOfDay,\r\n      weather,\r\n      mood,\r\n      userRole,\r\n      passengers,\r\n      destination,\r\n      isWeekend,\r\n      drivingDuration\r\n    } = context\r\n\r\n    return `# 角色\r\n你是一个专业的车载HMI壁纸提示词设计师，专门为汽车智能座舱系统生成动漫卡通风格的温馨壁纸描述提示词。\r\n\r\n## 任务\r\n根据提供的场景信息和用户上下文，生成一个能够体现用户真实感受和心理状态的动漫卡通风格壁纸描述提示词。\r\n\r\n## 场景信息\r\n- 场景ID: ${sceneId}\r\n- 场景名称: ${sceneName}\r\n- 场景描述: ${sceneDescription}\r\n- 主题风格: ${sceneTheme}\r\n\r\n## 用户上下文\r\n- 时间: ${timeOfDay || '未知'}\r\n- 天气: ${weather || '晴朗'}\r\n- 用户情绪: ${mood || '平静'}\r\n- 用户角色: ${userRole || '车主'}\r\n- 同行人员: ${passengers || '独自'}\r\n- 目的地: ${destination || '未知'}\r\n- 是否周末: ${isWeekend ? '是' : '否'}\r\n- 驾驶时长: ${drivingDuration || '刚开始'}\r\n\r\n## 生成要求\r\n1. **动漫卡通风格**: 必须使用动漫、卡通、插画风格，避免写实风格\r\n2. **温馨氛围**: 营造温馨、舒适、治愈的视觉氛围\r\n3. **避免人物**: 不要生成任何人物形象，专注于场景和环境\r\n4. **情感共鸣**: 从车主的真实感受出发，体现当前场景下的心理状态\r\n5. **场景贴合**: 紧密结合车载环境和驾驶场景\r\n6. **视觉描述**: 提供具体、生动的视觉元素描述\r\n7. **氛围营造**: 通过光影、色彩、构图营造相应的氛围\r\n\r\n## 艺术风格要求\r\n- 🎨 **风格**: 动漫卡通、插画风格、手绘风格\r\n- 🌈 **色彩**: 温暖柔和的色调，避免过于强烈的对比\r\n- 🏠 **场景**: 可爱的小房子、温馨的车厢、自然风光等\r\n- 🚗 **车辆**: 卡通化的汽车设计，圆润可爱的造型\r\n- 🌸 **元素**: 花朵、云朵、星星、心形等温馨装饰元素\r\n\r\n## 严格禁止\r\n- ❌ 写实风格、照片级真实感\r\n- ❌ 任何人物形象（包括卡通人物）\r\n- ❌ 高楼大厦、现代都市建筑\r\n- ❌ 机械化的模板描述\r\n- ❌ 过于抽象或概念化的表达\r\n\r\n## 输出格式\r\n直接输出壁纸描述提示词，不需要额外说明。提示词应该：\r\n- 专注于动漫卡通风格的场景描述\r\n- 体现温馨治愈的情感氛围\r\n- 适合作为文生图模型的输入\r\n- 长度控制在100-200字之间\r\n\r\n请根据以上信息，生成一个动漫卡通风格的温馨壁纸描述提示词：`\r\n  }\r\n\r\n  /**\r\n   * 获取场景特定的情感增强词\r\n   */\r\n  getSceneEmotionalEnhancements(sceneId) {\r\n    const enhancements = {\r\n      morningCommuteFamily: {\r\n        emotions: ['温馨', '关爱', '期待', '家庭温暖'],\r\n        keywords: ['晨光', '童趣', '亲子时光', '温暖守护', '卡通车厢'],\r\n        atmosphere: '动漫风格的温馨车厢，柔和的晨光透过车窗，可爱的玩具和书包散落其间'\r\n      },\r\n      morningCommuteFocus: {\r\n        emotions: ['专注', '效率', '清醒', '目标导向'],\r\n        keywords: ['晨光', '清新', '专注力', '简约', '卡通道路'],\r\n        atmosphere: '动漫风格的简约驾驶环境，清新的晨光，可爱的道路标示和简约仪表盘'\r\n      },\r\n      eveningCommute: {\r\n        emotions: ['放松', '满足', '期待', '归家心切'],\r\n        keywords: ['夕阳', '归途', '温暖', '疲劳缓解', '卡通小屋'],\r\n        atmosphere: '动漫风格的黄昏归途，温暖的夕阳，道路两旁可爱的卡通小屋和温暖的灯光'\r\n      },\r\n      waitingMode: {\r\n        emotions: ['悠闲', '放松', '享受', '宁静'],\r\n        keywords: ['休憩', '舒适', '轻松', '冥想', '卡通云朵'],\r\n        atmosphere: '动漫风格的悠闲休息场景，舒适的卡通座椅，飘浮的云朵和轻松的氛围'\r\n      },\r\n      rainyNight: {\r\n        emotions: ['宁静', '沉思', '安全', '温暖'],\r\n        keywords: ['雨滴', '夜色', '温暖灯光', '安全感', '卡通雨伞'],\r\n        atmosphere: '动漫风格的雨夜车厢，车窗上滑落的卡通雨滴，温暖的车内灯光和舒适的环境'\r\n      },\r\n      familyTrip: {\r\n        emotions: ['愉悦', '期待', '亲密', '冒险'],\r\n        keywords: ['阳光', '自然', '欢笑', '自由', '卡通风景'],\r\n        atmosphere: '动漫风格的愉快出游，阳光明媚的自然风光，卡通化的风景和可爱的车辆'\r\n      },\r\n      longDistance: {\r\n        emotions: ['专注', '坚韧', '自由', '思考'],\r\n        keywords: ['公路', '远方', '自由感', '坚持', '卡通地图'],\r\n        atmosphere: '动漫风格的长途驾驶，开阔的卡通道路，可爱的地图元素和自由的氛围'\r\n      },\r\n      romanticMode: {\r\n        emotions: ['浪漫', '亲密', '温馨', '私密'],\r\n        keywords: ['星光', '温暖', '浪漫氛围', '温柔', '卡通星星'],\r\n        atmosphere: '动漫风格的浪漫车厢，满天卡通星星，温暖的光影和温馨的氛围'\r\n      },\r\n      chargingMode: {\r\n        emotions: ['耐心', '期待', '科技感', '环保'],\r\n        keywords: ['充电', '科技', '未来感', '环保', '卡通闪电'],\r\n        atmosphere: '动漫风格的科技充电环境，可爱的充电元素，未来感的卡通设计'\r\n      }\r\n    }\r\n\r\n    return enhancements[sceneId] || {\r\n      emotions: ['舒适', '安全', '愉悦'],\r\n      keywords: ['驾驶', '舒适', '安全', '卡通元素'],\r\n      atmosphere: '动漫风格的舒适驾驶环境，可爱的卡通元素和温馨的氛围'\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取降级提示词\r\n   */\r\n  getFallbackPrompt(sceneInfo, context) {\r\n    const enhancements = this.getSceneEmotionalEnhancements(sceneInfo.id)\r\n    const timeInfo = context.timeOfDay || 'daytime'\r\n    const weatherInfo = context.weather || 'clear'\r\n    \r\n    const basePrompt = `${enhancements.atmosphere}`\r\n    const emotionalWords = enhancements.emotions.join('、')\r\n    const visualKeywords = enhancements.keywords.join('、')\r\n    \r\n    return `${basePrompt}, 体现${emotionalWords}的情感氛围, 包含${visualKeywords}等元素, 动漫卡通风格, 插画风格, 手绘风格, 温馨治愈, 柔和色彩, 可爱设计, 无人物, 纯场景, ${timeInfo}时光, ${weatherInfo}天气, (动漫风格:1.3), (卡通插画:1.2), (温馨治愈:1.1)`\r\n  }\r\n\r\n  /**\r\n   * 生成缓存键\r\n   */\r\n  getCacheKey(sceneInfo, context) {\r\n    const contextStr = JSON.stringify({\r\n      sceneId: sceneInfo.id,\r\n      timeOfDay: context.timeOfDay,\r\n      weather: context.weather,\r\n      mood: context.mood,\r\n      isWeekend: context.isWeekend\r\n    })\r\n    return `prompt_${Buffer.from(contextStr).toString('base64').slice(0, 32)}`\r\n  }\r\n\r\n  /**\r\n   * 缓存结果\r\n   */\r\n  cacheResult(key, result) {\r\n    if (this.cache.size >= this.maxCacheSize) {\r\n      // 删除最旧的缓存项\r\n      const firstKey = this.cache.keys().next().value\r\n      this.cache.delete(firstKey)\r\n    }\r\n    this.cache.set(key, result)\r\n  }\r\n\r\n  /**\r\n   * 清除缓存\r\n   */\r\n  clearCache() {\r\n    this.cache.clear()\r\n  }\r\n\r\n  /**\r\n   * 获取缓存统计\r\n   */\r\n  getCacheStats() {\r\n    return {\r\n      size: this.cache.size,\r\n      maxSize: this.maxCacheSize,\r\n      hitRate: 'N/A' // 可以添加命中率统计\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 根据时间获取情感化描述\r\n   */\r\n  getTimeEmotionalContext(hour) {\r\n    if (hour >= 5 && hour < 8) {\r\n      return {\r\n        timeOfDay: '清晨',\r\n        mood: '清新宁静',\r\n        lighting: '柔和的晨光',\r\n        atmosphere: '宁静祥和'\r\n      }\r\n    } else if (hour >= 8 && hour < 12) {\r\n      return {\r\n        timeOfDay: '上午',\r\n        mood: '充满活力',\r\n        lighting: '明亮的日光',\r\n        atmosphere: '活跃向上'\r\n      }\r\n    } else if (hour >= 12 && hour < 14) {\r\n      return {\r\n        timeOfDay: '中午',\r\n        mood: '略带疲惫',\r\n        lighting: '强烈的阳光',\r\n        atmosphere: '热情充沛'\r\n      }\r\n    } else if (hour >= 14 && hour < 17) {\r\n      return {\r\n        timeOfDay: '下午',\r\n        mood: '专注沉稳',\r\n        lighting: '倾斜的阳光',\r\n        atmosphere: '沉稳专注'\r\n      }\r\n    } else if (hour >= 17 && hour < 19) {\r\n      return {\r\n        timeOfDay: '傍晚',\r\n        mood: '期待放松',\r\n        lighting: '温暖的夕阳',\r\n        atmosphere: '温暖归家'\r\n      }\r\n    } else if (hour >= 19 && hour < 22) {\r\n      return {\r\n        timeOfDay: '夜晚',\r\n        mood: '宁静温馨',\r\n        lighting: '温暖的灯光',\r\n        atmosphere: '温馨舒适'\r\n      }\r\n    } else {\r\n      return {\r\n        timeOfDay: '深夜',\r\n        mood: '宁静安详',\r\n        lighting: '柔和的月光',\r\n        atmosphere: '宁静神秘'\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 根据天气获取情感化描述\r\n   */\r\n  getWeatherEmotionalContext(weather) {\r\n    const weatherMap = {\r\n      'sunny': {\r\n        description: '晴朗',\r\n        mood: '愉悦开朗',\r\n        lighting: '明媚的阳光',\r\n        atmosphere: '明亮开朗'\r\n      },\r\n      'cloudy': {\r\n        description: '多云',\r\n        mood: '平静舒缓',\r\n        lighting: '柔和的光线',\r\n        atmosphere: '柔和舒适'\r\n      },\r\n      'rainy': {\r\n        description: '下雨',\r\n        mood: '宁静沉思',\r\n        lighting: '雨滴的折射光',\r\n        atmosphere: '清新宁静'\r\n      },\r\n      'night': {\r\n        description: '夜晚',\r\n        mood: '神秘安静',\r\n        lighting: '温暖的灯光',\r\n        atmosphere: '宁静神秘'\r\n      }\r\n    }\r\n\r\n    return weatherMap[weather] || weatherMap.sunny\r\n  }\r\n\r\n  /**\r\n   * 批量生成提示词\r\n   */\r\n  async generateMultiplePrompts(scenes, context) {\r\n    const results = []\r\n    \r\n    for (const scene of scenes) {\r\n      try {\r\n        const prompt = await this.generateEmotionalPrompt(scene, context)\r\n        results.push({\r\n          sceneId: scene.id,\r\n          prompt,\r\n          success: true\r\n        })\r\n      } catch (error) {\r\n        console.error(`生成场景 ${scene.id} 提示词失败:`, error)\r\n        results.push({\r\n          sceneId: scene.id,\r\n          prompt: this.getFallbackPrompt(scene, context),\r\n          success: false,\r\n          error: error.message\r\n        })\r\n      }\r\n    }\r\n    \r\n    return results\r\n  }\r\n}"], "mappings": ";AAAA;AACA;AACA,OAAOA,UAAU,MAAM,uBAAuB;AAE9C,eAAe,MAAMC,wBAAwB,CAAC;EAC5CC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,UAAU,GAAG,IAAIH,UAAU,CAAC,CAAC;IAClC,IAAI,CAACI,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,YAAY,GAAG,EAAE;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,uBAAuBA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACrD,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACH,SAAS,EAAEC,OAAO,CAAC;;IAErD;IACA,IAAI,IAAI,CAACL,KAAK,CAACQ,GAAG,CAACF,QAAQ,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACN,KAAK,CAACS,GAAG,CAACH,QAAQ,CAAC;IACjC;IAEA,IAAI;MACF;MACA,MAAMI,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAACP,SAAS,EAAEC,OAAO,CAAC;;MAErE;MACA,MAAMO,eAAe,GAAG,MAAM,IAAI,CAACb,UAAU,CAACc,gBAAgB,CAACH,eAAe,CAAC;;MAE/E;MACA,IAAI,CAACI,WAAW,CAACR,QAAQ,EAAEM,eAAe,CAAC;MAE3CG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,eAAe,CAAC;MAC9C,OAAOA,eAAe;IAExB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC;MACA,OAAO,IAAI,CAACC,iBAAiB,CAACd,SAAS,EAAEC,OAAO,CAAC;IACnD;EACF;;EAEA;AACF;AACA;EACEM,oBAAoBA,CAACP,SAAS,EAAEC,OAAO,EAAE;IACvC,MAAM;MACJc,EAAE,EAAEC,OAAO;MACXC,IAAI,EAAEC,SAAS;MACfC,WAAW,EAAEC,gBAAgB;MAC7BC,KAAK,EAAEC;IACT,CAAC,GAAGtB,SAAS;IAEb,MAAM;MACJuB,SAAS;MACTC,OAAO;MACPC,IAAI;MACJC,QAAQ;MACRC,UAAU;MACVC,WAAW;MACXC,SAAS;MACTC;IACF,CAAC,GAAG7B,OAAO;IAEX,OAAO;AACX;AACA;AACA;AACA;AACA;AACA;AACA,UAAUe,OAAO;AACjB,UAAUE,SAAS;AACnB,UAAUE,gBAAgB;AAC1B,UAAUE,UAAU;AACpB;AACA;AACA,QAAQC,SAAS,IAAI,IAAI;AACzB,QAAQC,OAAO,IAAI,IAAI;AACvB,UAAUC,IAAI,IAAI,IAAI;AACtB,UAAUC,QAAQ,IAAI,IAAI;AAC1B,UAAUC,UAAU,IAAI,IAAI;AAC5B,SAASC,WAAW,IAAI,IAAI;AAC5B,UAAUC,SAAS,GAAG,GAAG,GAAG,GAAG;AAC/B,UAAUC,eAAe,IAAI,KAAK;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;EAC5B;;EAEA;AACF;AACA;EACEC,6BAA6BA,CAACf,OAAO,EAAE;IACrC,MAAMgB,YAAY,GAAG;MACnBC,oBAAoB,EAAE;QACpBC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;QACpCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;QAC9CC,UAAU,EAAE;MACd,CAAC;MACDC,mBAAmB,EAAE;QACnBH,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;QACpCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;QAC3CC,UAAU,EAAE;MACd,CAAC;MACDE,cAAc,EAAE;QACdJ,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;QACpCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;QAC5CC,UAAU,EAAE;MACd,CAAC;MACDG,WAAW,EAAE;QACXL,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAClCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;QAC1CC,UAAU,EAAE;MACd,CAAC;MACDI,UAAU,EAAE;QACVN,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAClCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;QAC7CC,UAAU,EAAE;MACd,CAAC;MACDK,UAAU,EAAE;QACVP,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAClCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;QAC1CC,UAAU,EAAE;MACd,CAAC;MACDM,YAAY,EAAE;QACZR,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAClCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;QAC3CC,UAAU,EAAE;MACd,CAAC;MACDO,YAAY,EAAE;QACZT,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAClCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC;QAC5CC,UAAU,EAAE;MACd,CAAC;MACDQ,YAAY,EAAE;QACZV,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;QACnCC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;QAC3CC,UAAU,EAAE;MACd;IACF,CAAC;IAED,OAAOJ,YAAY,CAAChB,OAAO,CAAC,IAAI;MAC9BkB,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC5BC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;MACpCC,UAAU,EAAE;IACd,CAAC;EACH;;EAEA;AACF;AACA;EACEtB,iBAAiBA,CAACd,SAAS,EAAEC,OAAO,EAAE;IACpC,MAAM+B,YAAY,GAAG,IAAI,CAACD,6BAA6B,CAAC/B,SAAS,CAACe,EAAE,CAAC;IACrE,MAAM8B,QAAQ,GAAG5C,OAAO,CAACsB,SAAS,IAAI,SAAS;IAC/C,MAAMuB,WAAW,GAAG7C,OAAO,CAACuB,OAAO,IAAI,OAAO;IAE9C,MAAMuB,UAAU,GAAG,GAAGf,YAAY,CAACI,UAAU,EAAE;IAC/C,MAAMY,cAAc,GAAGhB,YAAY,CAACE,QAAQ,CAACe,IAAI,CAAC,GAAG,CAAC;IACtD,MAAMC,cAAc,GAAGlB,YAAY,CAACG,QAAQ,CAACc,IAAI,CAAC,GAAG,CAAC;IAEtD,OAAO,GAAGF,UAAU,OAAOC,cAAc,YAAYE,cAAc,wDAAwDL,QAAQ,OAAOC,WAAW,wCAAwC;EAC/L;;EAEA;AACF;AACA;EACE3C,WAAWA,CAACH,SAAS,EAAEC,OAAO,EAAE;IAC9B,MAAMkD,UAAU,GAAGC,IAAI,CAACC,SAAS,CAAC;MAChCrC,OAAO,EAAEhB,SAAS,CAACe,EAAE;MACrBQ,SAAS,EAAEtB,OAAO,CAACsB,SAAS;MAC5BC,OAAO,EAAEvB,OAAO,CAACuB,OAAO;MACxBC,IAAI,EAAExB,OAAO,CAACwB,IAAI;MAClBI,SAAS,EAAE5B,OAAO,CAAC4B;IACrB,CAAC,CAAC;IACF,OAAO,UAAUyB,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,QAAQ,CAAC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;EAC5E;;EAEA;AACF;AACA;EACE/C,WAAWA,CAACgD,GAAG,EAAEC,MAAM,EAAE;IACvB,IAAI,IAAI,CAAC/D,KAAK,CAACgE,IAAI,IAAI,IAAI,CAAC9D,YAAY,EAAE;MACxC;MACA,MAAM+D,QAAQ,GAAG,IAAI,CAACjE,KAAK,CAACkE,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK;MAC/C,IAAI,CAACpE,KAAK,CAACqE,MAAM,CAACJ,QAAQ,CAAC;IAC7B;IACA,IAAI,CAACjE,KAAK,CAACsE,GAAG,CAACR,GAAG,EAAEC,MAAM,CAAC;EAC7B;;EAEA;AACF;AACA;EACEQ,UAAUA,CAAA,EAAG;IACX,IAAI,CAACvE,KAAK,CAACwE,KAAK,CAAC,CAAC;EACpB;;EAEA;AACF;AACA;EACEC,aAAaA,CAAA,EAAG;IACd,OAAO;MACLT,IAAI,EAAE,IAAI,CAAChE,KAAK,CAACgE,IAAI;MACrBU,OAAO,EAAE,IAAI,CAACxE,YAAY;MAC1ByE,OAAO,EAAE,KAAK,CAAC;IACjB,CAAC;EACH;;EAEA;AACF;AACA;EACEC,uBAAuBA,CAACC,IAAI,EAAE;IAC5B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE;MACzB,OAAO;QACLlD,SAAS,EAAE,IAAI;QACfE,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;IACH,CAAC,MAAM,IAAIqC,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE;MACjC,OAAO;QACLlD,SAAS,EAAE,IAAI;QACfE,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;IACH,CAAC,MAAM,IAAIqC,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE;MAClC,OAAO;QACLlD,SAAS,EAAE,IAAI;QACfE,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;IACH,CAAC,MAAM,IAAIqC,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE;MAClC,OAAO;QACLlD,SAAS,EAAE,IAAI;QACfE,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;IACH,CAAC,MAAM,IAAIqC,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE;MAClC,OAAO;QACLlD,SAAS,EAAE,IAAI;QACfE,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;IACH,CAAC,MAAM,IAAIqC,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE;MAClC,OAAO;QACLlD,SAAS,EAAE,IAAI;QACfE,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLb,SAAS,EAAE,IAAI;QACfE,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACEuC,0BAA0BA,CAACnD,OAAO,EAAE;IAClC,MAAMoD,UAAU,GAAG;MACjB,OAAO,EAAE;QACPzD,WAAW,EAAE,IAAI;QACjBM,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;MACD,QAAQ,EAAE;QACRjB,WAAW,EAAE,IAAI;QACjBM,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd,CAAC;MACD,OAAO,EAAE;QACPjB,WAAW,EAAE,IAAI;QACjBM,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,QAAQ;QAClBtC,UAAU,EAAE;MACd,CAAC;MACD,OAAO,EAAE;QACPjB,WAAW,EAAE,IAAI;QACjBM,IAAI,EAAE,MAAM;QACZiD,QAAQ,EAAE,OAAO;QACjBtC,UAAU,EAAE;MACd;IACF,CAAC;IAED,OAAOwC,UAAU,CAACpD,OAAO,CAAC,IAAIoD,UAAU,CAACC,KAAK;EAChD;;EAEA;AACF;AACA;EACE,MAAMC,uBAAuBA,CAACC,MAAM,EAAE9E,OAAO,EAAE;IAC7C,MAAM+E,OAAO,GAAG,EAAE;IAElB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MAC1B,IAAI;QACF,MAAMG,MAAM,GAAG,MAAM,IAAI,CAACnF,uBAAuB,CAACkF,KAAK,EAAEhF,OAAO,CAAC;QACjE+E,OAAO,CAACG,IAAI,CAAC;UACXnE,OAAO,EAAEiE,KAAK,CAAClE,EAAE;UACjBmE,MAAM;UACNE,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOvE,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,QAAQoE,KAAK,CAAClE,EAAE,SAAS,EAAEF,KAAK,CAAC;QAC/CmE,OAAO,CAACG,IAAI,CAAC;UACXnE,OAAO,EAAEiE,KAAK,CAAClE,EAAE;UACjBmE,MAAM,EAAE,IAAI,CAACpE,iBAAiB,CAACmE,KAAK,EAAEhF,OAAO,CAAC;UAC9CmF,OAAO,EAAE,KAAK;UACdvE,KAAK,EAAEA,KAAK,CAACwE;QACf,CAAC,CAAC;MACJ;IACF;IAEA,OAAOL,OAAO;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}