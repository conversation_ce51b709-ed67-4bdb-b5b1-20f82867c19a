{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"voice-interaction-manager\"\n};\nconst _hoisted_2 = [\"title\"];\nconst _hoisted_3 = {\n  key: 0,\n  class: \"recognition-result\"\n};\nconst _hoisted_4 = {\n  class: \"result-text\"\n};\nconst _hoisted_5 = {\n  class: \"result-actions\"\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"status-message\"\n};\nconst _hoisted_7 = {\n  key: 2,\n  class: \"text-input-fallback\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 语音输入按钮 \"), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleVoiceInteraction && $setup.toggleVoiceInteraction(...args)),\n    class: _normalizeClass(['voice-btn', {\n      listening: $setup.isListening,\n      processing: $setup.isProcessing,\n      speaking: $setup.isSpeaking\n    }])\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass($setup.getVoiceButtonIcon)\n  }, null, 2 /* CLASS */)], 2 /* CLASS */), _createCommentVNode(\" 文本输入切换按钮 \"), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $setup.toggleTextInput && $setup.toggleTextInput(...args)),\n    class: \"text-toggle-btn\",\n    title: $setup.showTextInput ? '隐藏文本输入' : '显示文本输入'\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass($setup.showTextInput ? 'fas fa-keyboard' : 'fas fa-edit')\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_2), _createCommentVNode(\" 识别结果显示 \"), $setup.recognitionResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.recognitionResult), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.acceptResult && $setup.acceptResult(...args)),\n    class: \"action-btn accept\"\n  }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n    class: \"fas fa-check\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = (...args) => $setup.rejectResult && $setup.rejectResult(...args)),\n    class: \"action-btn reject\"\n  }, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 处理状态显示 \"), $setup.statusMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_cache[9] || (_cache[9] = _createElementVNode(\"i\", {\n    class: \"fas fa-info-circle\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($setup.statusMessage), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 文本输入备用方案 \"), $setup.showTextInput ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.textInput = $event),\n    onKeyup: _cache[5] || (_cache[5] = _withKeys((...args) => $setup.submitTextInput && $setup.submitTextInput(...args), [\"enter\"])),\n    placeholder: \"或直接输入描述...\",\n    class: \"text-input-field\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.textInput]]), _createElementVNode(\"button\", {\n    onClick: _cache[6] || (_cache[6] = (...args) => $setup.submitTextInput && $setup.submitTextInput(...args)),\n    class: \"submit-btn\"\n  }, _cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n    class: \"fas fa-paper-plane\"\n  }, null, -1 /* CACHED */)]))])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "onClick", "_cache", "args", "$setup", "toggleVoiceInteraction", "_normalizeClass", "isListening", "isProcessing", "isSpeaking", "getVoiceButtonIcon", "toggleTextInput", "title", "showTextInput", "recognitionResult", "_hoisted_3", "_hoisted_4", "_toDisplayString", "_hoisted_5", "acceptResult", "rejectResult", "statusMessage", "_hoisted_6", "_hoisted_7", "textInput", "$event", "onKeyup", "_with<PERSON><PERSON><PERSON>", "submitTextInput", "placeholder"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue"], "sourcesContent": ["<template>\n  <div class=\"voice-interaction-manager\">\n    <!-- 语音输入按钮 -->\n    <button\n      @click=\"toggleVoiceInteraction\"\n      :class=\"['voice-btn', {\n        listening: isListening,\n        processing: isProcessing,\n        speaking: isSpeaking\n      }]\"\n    >\n      <i :class=\"getVoiceButtonIcon\"></i>\n    </button>\n\n    <!-- 文本输入切换按钮 -->\n    <button\n      @click=\"toggleTextInput\"\n      class=\"text-toggle-btn\"\n      :title=\"showTextInput ? '隐藏文本输入' : '显示文本输入'\"\n    >\n      <i :class=\"showTextInput ? 'fas fa-keyboard' : 'fas fa-edit'\"></i>\n    </button>\n\n    <!-- 识别结果显示 -->\n    <div v-if=\"recognitionResult\" class=\"recognition-result\">\n      <div class=\"result-text\">{{ recognitionResult }}</div>\n      <div class=\"result-actions\">\n        <button @click=\"acceptResult\" class=\"action-btn accept\">\n          <i class=\"fas fa-check\"></i>\n        </button>\n        <button @click=\"rejectResult\" class=\"action-btn reject\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- 处理状态显示 -->\n    <div v-if=\"statusMessage\" class=\"status-message\">\n      <i class=\"fas fa-info-circle\"></i>\n      {{ statusMessage }}\n    </div>\n\n    <!-- 文本输入备用方案 -->\n    <div v-if=\"showTextInput\" class=\"text-input-fallback\">\n      <input\n        v-model=\"textInput\"\n        @keyup.enter=\"submitTextInput\"\n        placeholder=\"或直接输入描述...\"\n        class=\"text-input-field\"\n      />\n      <button @click=\"submitTextInput\" class=\"submit-btn\">\n        <i class=\"fas fa-paper-plane\"></i>\n      </button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport AsrService from '@/services/AsrService'\nimport TtsService from '@/services/TtsService'\nimport LlmService from '@/services/LlmService'\n\nexport default {\n  name: 'VoiceInteractionManager',\n  emits: ['wallpaper-prompt-ready', 'scene-switch-requested'],\n\n  setup(props, { emit }) {\n    const asrService = new AsrService()\n    const ttsService = new TtsService()\n    const llmService = new LlmService()\n\n    const isListening = ref(false)\n    const isProcessing = ref(false)\n    const isSpeaking = ref(false)\n    const recognitionResult = ref('')\n    const textInput = ref('')\n    const statusMessage = ref('')\n    const showTextInput = ref(false) // 默认隐藏文本输入框\n\n    const voiceState = computed(() => {\n      if (isSpeaking.value) return 'speaking'\n      if (isProcessing.value) return 'processing'\n      if (isListening.value) return 'listening'\n      return 'idle'\n    })\n\n    const getVoiceButtonIcon = computed(() => {\n      switch (voiceState.value) {\n        case 'speaking':\n          return 'fas fa-volume-up'\n        case 'processing':\n          return 'fas fa-spinner fa-spin'\n        case 'listening':\n          return 'fas fa-stop'\n        default:\n          return 'fas fa-microphone'\n      }\n    })\n\n    const toggleVoiceInteraction = async () => {\n      if (isListening.value) {\n        stopVoiceInteraction()\n      } else {\n        startVoiceInteraction()\n      }\n    }\n\n    const startVoiceInteraction = async () => {\n      if (isListening.value || isProcessing.value) return\n\n      try {\n        isListening.value = true\n        statusMessage.value = '正在聆听...'\n        \n        // 使用ASR服务进行语音识别\n        const result = await asrService.startRecognition()\n        recognitionResult.value = result\n        \n        // 自动接受结果并处理\n        await processVoiceResult(result)\n        \n      } catch (error) {\n        console.error('语音识别失败:', error)\n        statusMessage.value = '语音识别失败，请重试'\n        \n        // 语音反馈\n        await ttsService.speak('语音识别失败，请重试或使用文本输入')\n      } finally {\n        isListening.value = false\n        statusMessage.value = ''\n      }\n    }\n\n    const stopVoiceInteraction = () => {\n      if (asrService) {\n        asrService.stopRecognition()\n      }\n      if (ttsService) {\n        ttsService.stop()\n      }\n      isListening.value = false\n      statusMessage.value = ''\n    }\n\n    const processVoiceResult = async (userInput) => {\n      isProcessing.value = true\n      statusMessage.value = '正在处理...'\n\n      try {\n        // 首先尝试识别场景切换指令\n        statusMessage.value = '正在理解您的需求...'\n        const sceneResult = await llmService.detectSceneFromVoice(userInput)\n        \n        if (sceneResult.sceneId && sceneResult.confidence > 0.6) {\n          // 场景切换逻辑\n          statusMessage.value = `正在切换到${getSceneName(sceneResult.sceneId)}...`\n          await ttsService.speak(`正在为您切换到${getSceneName(sceneResult.sceneId)}`)\n          \n          // 发送场景切换请求\n          emit('scene-switch-requested', {\n            sceneId: sceneResult.sceneId,\n            confidence: sceneResult.confidence,\n            reason: sceneResult.reason\n          })\n          \n          // 完成反馈\n          setTimeout(async () => {\n            await ttsService.speak('场景切换完成')\n            statusMessage.value = ''\n          }, 2000)\n          \n          return\n        }\n        \n        // 如果不是场景切换，则按原来的逻辑处理壁纸生成\n        const enhancedPrompt = await llmService.generateResponse(userInput)\n        \n        // 语音反馈处理结果\n        statusMessage.value = '正在生成壁纸...'\n        await ttsService.speak('正在为您生成壁纸，请稍候')\n        \n        // 发送最终提示词给主题管理器\n        emit('wallpaper-prompt-ready', enhancedPrompt)\n        \n        // 完成反馈\n        setTimeout(async () => {\n          await ttsService.speak('壁纸生成完成')\n          statusMessage.value = ''\n        }, 3000)\n        \n      } catch (error) {\n        console.error('处理语音输入失败:', error)\n        statusMessage.value = '处理失败，请重试'\n        \n        await ttsService.speak('处理失败，请重试')\n        \n        // 降级方案：直接使用原始输入\n        emit('wallpaper-prompt-ready', userInput)\n      } finally {\n        isProcessing.value = false\n        recognitionResult.value = ''\n      }\n    }\n\n    const acceptResult = async () => {\n      if (recognitionResult.value.trim()) {\n        await processVoiceResult(recognitionResult.value)\n      }\n    }\n\n    const rejectResult = () => {\n      recognitionResult.value = ''\n      statusMessage.value = ''\n      ttsService.speak('请重新描述')\n    }\n\n    const submitTextInput = async () => {\n      if (textInput.value.trim()) {\n        await processVoiceResult(textInput.value)\n        textInput.value = ''\n      }\n    }\n\n    const toggleTextInput = () => {\n      showTextInput.value = !showTextInput.value\n    }\n\n    // 获取场景的中文名称\n    const getSceneName = (sceneId) => {\n      const sceneNames = {\n        morningCommuteFamily: '家庭出行模式',\n        morningCommuteFocus: '专注通勤模式',\n        eveningCommute: '下班通勤模式',\n        waitingMode: '等待休息模式',\n        rainyNight: '雨夜模式',\n        familyTrip: '家庭出游模式',\n        longDistance: '长途驾驶模式',\n        guestMode: '访客模式',\n        petMode: '宠物模式',\n        carWashMode: '洗车模式',\n        romanticMode: '浪漫模式',\n        chargingMode: '充电模式',\n        fatigueDetection: '疲劳检测模式',\n        userSwitch: '用户切换模式',\n        parkingMode: '泊车模式',\n        emergencyMode: '紧急模式'\n      }\n      return sceneNames[sceneId] || sceneId\n    }\n\n    // 初始化TTS语音\n    onMounted(() => {\n      // 等待语音列表加载\n      if (ttsService.isSupported) {\n        setTimeout(() => {\n          ttsService.getVoices()\n        }, 100)\n      }\n    })\n\n    return {\n      isListening,\n      isProcessing,\n      isSpeaking,\n      recognitionResult,\n      textInput,\n      statusMessage,\n      showTextInput,\n      voiceState,\n      getVoiceButtonIcon,\n      toggleVoiceInteraction,\n      toggleTextInput,\n      acceptResult,\n      rejectResult,\n      submitTextInput,\n      getSceneName\n    }\n  }\n}\n</script>\n\n<style scoped>\n.voice-interaction-manager {\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 10px;\n  max-width: 420px;\n}\n\n.voice-btn {\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  font-size: 24px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(12px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.voice-btn:hover {\n  transform: scale(1.1);\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.voice-btn.listening {\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\n  animation: pulse 1.5s infinite;\n}\n\n.voice-btn.processing {\n  background: linear-gradient(45deg, #4834d4, #686de0);\n  animation: pulse 2s infinite;\n}\n\n.voice-btn.speaking {\n  background: linear-gradient(45deg, #00d2d3, #54a0ff);\n  animation: wave 2s infinite;\n}\n\n.text-toggle-btn {\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.2);\n  border: none;\n  color: white;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  transition: all 0.3s ease;\n  font-size: 16px;\n}\n\n.text-toggle-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.1);\n}\n\n.recognition-result {\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 15px;\n  backdrop-filter: blur(12px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.result-text {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 10px;\n  line-height: 1.4;\n}\n\n.result-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  border: none;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.action-btn.accept {\n  background: #2ed573;\n  color: white;\n}\n\n.action-btn.reject {\n  background: #ff4757;\n  color: white;\n}\n\n.action-btn:hover {\n  transform: scale(1.1);\n}\n\n.status-message {\n  padding: 12px 15px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 10px;\n  font-size: 13px;\n  color: #333;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.text-input-fallback {\n  display: flex;\n  gap: 10px;\n}\n\n.text-input-field {\n  flex: 1;\n  padding: 12px 18px;\n  border: none;\n  border-radius: 25px;\n  background: rgba(255, 255, 255, 0.9);\n  font-size: 14px;\n  outline: none;\n  backdrop-filter: blur(10px);\n  color: #333;\n}\n\n.submit-btn {\n  width: 44px;\n  height: 44px;\n  border-radius: 50%;\n  border: none;\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  transition: all 0.3s ease;\n}\n\n.submit-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: scale(1.1);\n}\n\n@keyframes pulse {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n}\n\n@keyframes wave {\n  0%, 100% { transform: scale(1); }\n  25% { transform: scale(1.02); }\n  75% { transform: scale(1.02); }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAA2B;;;;EAuBNA,KAAK,EAAC;;;EAC7BA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAgB;;;EAWHA,KAAK,EAAC;;;;EAMNA,KAAK,EAAC;;;uBA1ClCC,mBAAA,CAqDM,OArDNC,UAqDM,GApDJC,mBAAA,YAAe,EACfC,mBAAA,CASS;IARNC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAC,sBAAA,IAAAD,MAAA,CAAAC,sBAAA,IAAAF,IAAA,CAAsB;IAC7BP,KAAK,EAAAU,eAAA;iBAAqCF,MAAA,CAAAG,WAAW;kBAAsBH,MAAA,CAAAI,YAAY;gBAAoBJ,MAAA,CAAAK;;MAM5GT,mBAAA,CAAmC;IAA/BJ,KAAK,EAAAU,eAAA,CAAEF,MAAA,CAAAM,kBAAkB;4CAG/BX,mBAAA,cAAiB,EACjBC,mBAAA,CAMS;IALNC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAO,eAAA,IAAAP,MAAA,CAAAO,eAAA,IAAAR,IAAA,CAAe;IACvBP,KAAK,EAAC,iBAAiB;IACtBgB,KAAK,EAAER,MAAA,CAAAS,aAAa;MAErBb,mBAAA,CAAkE;IAA9DJ,KAAK,EAAAU,eAAA,CAAEF,MAAA,CAAAS,aAAa;wDAG1Bd,mBAAA,YAAe,EACJK,MAAA,CAAAU,iBAAiB,I,cAA5BjB,mBAAA,CAUM,OAVNkB,UAUM,GATJf,mBAAA,CAAsD,OAAtDgB,UAAsD,EAAAC,gBAAA,CAA1Bb,MAAA,CAAAU,iBAAiB,kBAC7Cd,mBAAA,CAOM,OAPNkB,UAOM,GANJlB,mBAAA,CAES;IAFAC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAe,YAAA,IAAAf,MAAA,CAAAe,YAAA,IAAAhB,IAAA,CAAY;IAAEP,KAAK,EAAC;gCAClCI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,0B,IAEzBI,mBAAA,CAES;IAFAC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAgB,YAAA,IAAAhB,MAAA,CAAAgB,YAAA,IAAAjB,IAAA,CAAY;IAAEP,KAAK,EAAC;gCAClCI,mBAAA,CAA4B;IAAzBJ,KAAK,EAAC;EAAc,0B,6CAK7BG,mBAAA,YAAe,EACJK,MAAA,CAAAiB,aAAa,I,cAAxBxB,mBAAA,CAGM,OAHNyB,UAGM,G,0BAFJtB,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,4B,iBAAK,GAClC,GAAAqB,gBAAA,CAAGb,MAAA,CAAAiB,aAAa,iB,wCAGlBtB,mBAAA,cAAiB,EACNK,MAAA,CAAAS,aAAa,I,cAAxBhB,mBAAA,CAUM,OAVN0B,UAUM,G,gBATJvB,mBAAA,CAKE;+DAJSI,MAAA,CAAAoB,SAAS,GAAAC,MAAA;IACjBC,OAAK,EAAAxB,MAAA,QAAAA,MAAA,MAAAyB,SAAA,KAAAxB,IAAA,KAAQC,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAwB,eAAA,IAAAzB,IAAA,CAAe;IAC7B0B,WAAW,EAAC,YAAY;IACxBjC,KAAK,EAAC;iEAHGQ,MAAA,CAAAoB,SAAS,E,GAKpBxB,mBAAA,CAES;IAFAC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,MAAA,CAAAwB,eAAA,IAAAxB,MAAA,CAAAwB,eAAA,IAAAzB,IAAA,CAAe;IAAEP,KAAK,EAAC;kCACrCI,mBAAA,CAAkC;IAA/BJ,KAAK,EAAC;EAAoB,0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}