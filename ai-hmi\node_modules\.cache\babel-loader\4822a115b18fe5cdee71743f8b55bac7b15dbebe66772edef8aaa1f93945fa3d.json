{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"immersive-interface\"\n};\nconst _hoisted_2 = {\n  class: \"conversation-panel\"\n};\nconst _hoisted_3 = {\n  class: \"preset-buttons\"\n};\nconst _hoisted_4 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 左侧对话区域 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"welcome-message\"\n  }, [_createElementVNode(\"p\", null, \"你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.presetScenes, preset => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: preset.id,\n      onClick: $event => $setup.generateWallpaper(preset.prompt),\n      class: \"preset-btn\"\n    }, _toDisplayString(preset.name), 9 /* TEXT, PROPS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))]), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"description\"\n  }, [_createElementVNode(\"p\", null, \"根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍\")], -1 /* CACHED */))]), _createCommentVNode(\" 右侧VPA区域 \"), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"vpa-panel\"\n  }, [_createElementVNode(\"div\", {\n    class: \"vpa-container\"\n  }, [_createElementVNode(\"img\", {\n    src: \"/images/vpa2.gif\",\n    alt: \"VPA智能助手\",\n    class: \"vpa-avatar\"\n  })])], -1 /* CACHED */))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_Fragment", "_renderList", "$setup", "presetScenes", "preset", "key", "id", "onClick", "$event", "generateWallpaper", "prompt", "name", "_hoisted_4", "src", "alt"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue"], "sourcesContent": ["<template>\n  <div class=\"immersive-interface\">\n    <!-- 左侧对话区域 -->\n    <div class=\"conversation-panel\">\n      <div class=\"welcome-message\">\n        <p>你好，我是小智，欢迎来到AI-HMI，我是智能桌面助手，可以根据场景定制专属桌面，这是对我说：</p>\n      </div>\n      \n      <div class=\"preset-buttons\">\n        <button \n          v-for=\"preset in presetScenes\" \n          :key=\"preset.id\"\n          @click=\"generateWallpaper(preset.prompt)\"\n          class=\"preset-btn\"\n        >\n          {{ preset.name }}\n        </button>\n      </div>\n      \n      <div class=\"description\">\n        <p>根据你的使用习惯，后续会全面智能化桌面，或者也可以在AI-LAB设置你的更多介绍</p>\n      </div>\n    </div>\n\n    <!-- 右侧VPA区域 -->\n    <div class=\"vpa-panel\">\n      <div class=\"vpa-container\">\n        <img src=\"/images/vpa2.gif\" alt=\"VPA智能助手\" class=\"vpa-avatar\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed } from 'vue'\nimport EmotionalPromptGenerator from '@/services/EmotionalPromptGenerator'\nimport SceneContextManager from '@/services/SceneContextManager'\n\nexport default {\n  name: 'ImmersiveWallpaperInterface',\n  emits: ['wallpaper-prompt-ready'],\n  \n  setup(props, { emit }) {\n    const emotionalPromptGenerator = new EmotionalPromptGenerator()\n    const contextManager = new SceneContextManager()\n    \n    const presetScenes = ref([\n      {\n        id: 'commute',\n        name: '生成通勤桌面',\n        prompt: '动漫卡通风格的通勤场景，可爱的卡通车厢，柔和的晨光，温馨的通勤氛围'\n      },\n      {\n        id: 'navigation',\n        name: '导航3D效果',\n        prompt: '动漫卡通风格的导航界面，可爱的地图元素，卡通化的道路标识，温馨的导航体验'\n      },\n      {\n        id: 'solitude',\n        name: '帮我规划一个独处的桌面',\n        prompt: '动漫卡通风格的独处空间，舒适的小屋，柔和的灯光，温馨治愈的独处氛围'\n      },\n      {\n        id: 'spring',\n        name: '生成春游桌面',\n        prompt: '动漫卡通风格的春游场景，可爱的花草树木，明媚的春光，温馨的春日氛围'\n      },\n      {\n        id: 'weekend',\n        name: '帮我规划一个周末一日游',\n        prompt: '动漫卡通风格的周末出游，可爱的风景元素，轻松的氛围，温馨的周末时光'\n      }\n    ])\n\n    const generateWallpaper = async (prompt) => {\n      console.log('生成壁纸:', prompt)\n      \n      try {\n        // 获取当前上下文\n        const currentContext = contextManager.getPromptGenerationContext()\n        console.log('📋 当前上下文:', currentContext)\n        \n        // 创建场景信息对象\n        const sceneInfo = {\n          id: 'immersive',\n          name: '沉浸式模式',\n          description: prompt,\n          theme: 'immersive'\n        }\n        \n        // 生成情感化提示词\n        const emotionalPrompt = await emotionalPromptGenerator.generateEmotionalPrompt(\n          sceneInfo,\n          currentContext\n        )\n        \n        console.log('🎭 情感化提示词生成成功:', emotionalPrompt)\n        \n        // 发送完整的事件数据\n        emit('wallpaper-prompt-ready', {\n          prompt: emotionalPrompt,\n          originalPrompt: prompt,\n          scene: sceneInfo,\n          context: currentContext\n        })\n        \n      } catch (error) {\n        console.error('情感化提示词生成失败，使用原始提示词:', error)\n        \n        // 降级到原始提示词\n        emit('wallpaper-prompt-ready', {\n          prompt,\n          originalPrompt: prompt,\n          scene: {\n            id: 'immersive',\n            name: '沉浸式模式',\n            description: prompt,\n            theme: 'immersive'\n          },\n          context: contextManager.getPromptGenerationContext()\n        })\n      }\n    }\n\n    return {\n      presetScenes,\n      generateWallpaper,\n      // 暴露上下文信息用于调试\n      contextInfo: computed(() => contextManager.getStatistics())\n    }\n  }\n}\n</script>\n\n<style scoped>\n.immersive-interface {\n  width: 100%;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 40px;\n  box-sizing: border-box;\n}\n\n.conversation-panel {\n  flex: 1;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.welcome-message {\n  margin-bottom: 25px;\n}\n\n.welcome-message p {\n  color: white;\n  font-size: 16px;\n  line-height: 1.6;\n  margin: 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.preset-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-bottom: 25px;\n}\n\n.preset-btn {\n  padding: 12px 20px;\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  border-radius: 25px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  text-align: left;\n}\n\n.preset-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n\n.description {\n  margin-top: 20px;\n}\n\n.description p {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 12px;\n  line-height: 1.5;\n  margin: 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.vpa-panel {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  max-width: 500px;\n}\n\n.vpa-container {\n  width: 300px;\n  height: 300px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(20px);\n  border-radius: 50%;\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.vpa-avatar {\n  width: 200px;\n  height: 200px;\n  object-fit: contain;\n  border-radius: 50%;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .immersive-interface {\n    flex-direction: column;\n    padding: 20px;\n    gap: 30px;\n  }\n  \n  .conversation-panel {\n    max-width: 100%;\n  }\n  \n  .vpa-container {\n    width: 200px;\n    height: 200px;\n  }\n  \n  .vpa-avatar {\n    width: 150px;\n    height: 150px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAoB;;EAKxBA,KAAK,EAAC;AAAgB;;;uBAP/BC,mBAAA,CA6BM,OA7BNC,UA6BM,GA5BJC,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNC,UAmBM,G,0BAlBJD,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAAsD,WAAnD,iDAA+C,E,qBAGpDA,mBAAA,CASM,OATNE,UASM,I,kBARJL,mBAAA,CAOSM,SAAA,QAAAC,WAAA,CANUC,MAAA,CAAAC,YAAY,EAAtBC,MAAM;yBADfV,mBAAA,CAOS;MALNW,GAAG,EAAED,MAAM,CAACE,EAAE;MACdC,OAAK,EAAAC,MAAA,IAAEN,MAAA,CAAAO,iBAAiB,CAACL,MAAM,CAACM,MAAM;MACvCjB,KAAK,EAAC;wBAEHW,MAAM,CAACO,IAAI,wBAAAC,UAAA;8DAIlBf,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAA+C,WAA5C,0CAAwC,E,uBAI/CD,mBAAA,aAAgB,E,0BAChBC,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAe,IACxBI,mBAAA,CAA+D;IAA1DgB,GAAG,EAAC,kBAAkB;IAACC,GAAG,EAAC,SAAS;IAACrB,KAAK,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}