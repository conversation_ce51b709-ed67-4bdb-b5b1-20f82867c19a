[{"D:\\code\\pythonWork\\theme\\ai-hmi\\src\\main.js": "1", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue": "2", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue": "3", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue": "4", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue": "5", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue": "6", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue": "7", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue": "8", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue": "9", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue": "10", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue": "11", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\SceneManager.js": "12", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js": "13", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\SceneContextManager.js": "14", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js": "15", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js": "16", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\LlmService.js": "17", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\AsrService.js": "18", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js": "19", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js": "20", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\BaseCard.vue": "21", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassCard.vue": "22", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\TtsService.js": "23", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\vpa.js": "24", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\index.js": "25", "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\layout.js": "26"}, {"size": 195, "mtime": 1754273464770, "results": "27", "hashOfConfig": "28"}, {"size": 5387, "mtime": 1753957630545, "results": "29", "hashOfConfig": "28"}, {"size": 12769, "mtime": 1753924229010, "results": "30", "hashOfConfig": "28"}, {"size": 40894, "mtime": 1754273464767, "results": "31", "hashOfConfig": "28"}, {"size": 20551, "mtime": 1754008893561, "results": "32", "hashOfConfig": "28"}, {"size": 6347, "mtime": 1753952377657, "results": "33", "hashOfConfig": "28"}, {"size": 11595, "mtime": 1753927909625, "results": "34", "hashOfConfig": "28"}, {"size": 16128, "mtime": 1754273464768, "results": "35", "hashOfConfig": "28"}, {"size": 14226, "mtime": 1754273464768, "results": "36", "hashOfConfig": "28"}, {"size": 11618, "mtime": 1754275564343, "results": "37", "hashOfConfig": "28"}, {"size": 76365, "mtime": 1754017652373, "results": "38", "hashOfConfig": "28"}, {"size": 14619, "mtime": 1754015192668, "results": "39", "hashOfConfig": "28"}, {"size": 12102, "mtime": 1753924229023, "results": "40", "hashOfConfig": "28"}, {"size": 15166, "mtime": 1753950309312, "results": "41", "hashOfConfig": "28"}, {"size": 15105, "mtime": 1753924229023, "results": "42", "hashOfConfig": "28"}, {"size": 12006, "mtime": 1753952352571, "results": "43", "hashOfConfig": "28"}, {"size": 11085, "mtime": 1754017652374, "results": "44", "hashOfConfig": "28"}, {"size": 1617, "mtime": 1753850270876, "results": "45", "hashOfConfig": "28"}, {"size": 9102, "mtime": 1754008893564, "results": "46", "hashOfConfig": "28"}, {"size": 8519, "mtime": 1754008893564, "results": "47", "hashOfConfig": "28"}, {"size": 8359, "mtime": 1754273464767, "results": "48", "hashOfConfig": "28"}, {"size": 6069, "mtime": 1753924229010, "results": "49", "hashOfConfig": "28"}, {"size": 4327, "mtime": 1753857066008, "results": "50", "hashOfConfig": "28"}, {"size": 6880, "mtime": 1754273464771, "results": "51", "hashOfConfig": "28"}, {"size": 320, "mtime": 1754273464770, "results": "52", "hashOfConfig": "28"}, {"size": 5303, "mtime": 1754273464770, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, "17wmy<PERSON>", {"filePath": "57", "messages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "66", "messages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "98", "messages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "59"}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "56"}, "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\main.js", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\App.vue", [], [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\TestSceneGeneration.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\SceneManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\DynamicWallpaperManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\ImmersiveWallpaperInterface.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\VoiceInteractionManager.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\DefaultCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\SceneManager.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\AIColorAnalyzer.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\SceneContextManager.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\utils\\ColorExtractor.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\EmotionalPromptGenerator.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\LlmService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\AsrService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\ImageGenerationService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\DynamicWallpaperService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\BaseCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassCard.vue", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\services\\TtsService.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\vpa.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\index.js", [], "D:\\code\\pythonWork\\theme\\ai-hmi\\src\\store\\modules\\layout.js", []]