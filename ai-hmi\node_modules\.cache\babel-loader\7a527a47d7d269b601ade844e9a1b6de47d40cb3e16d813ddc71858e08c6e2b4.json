{"ast": null, "code": "import { defineStore } from 'pinia';\nexport const useVpaStore = defineStore('vpa', {\n  state: () => ({\n    // VPA当前状态\n    currentMode: 'companion',\n    // companion(陪伴模式) | interaction(交互模式) | hidden(隐藏)\n\n    // VPA可用模式\n    availableModes: {\n      companion: {\n        name: '陪伴模式',\n        description: 'VPA以小窗形式陪伴用户',\n        component: 'VPAAvatarWidget',\n        defaultSize: 'vpaWidget',\n        // 2x2\n        animation: 'idle',\n        position: {\n          x: 15,\n          y: 7\n        }\n      },\n      interaction: {\n        name: '交互模式',\n        description: 'VPA展开为交互面板',\n        component: 'VPAInteractionPanel',\n        defaultSize: 'vpaInteractionLarge',\n        // 8x9\n        animation: 'talking',\n        position: {\n          x: 9,\n          y: 1\n        }\n      },\n      hidden: {\n        name: '隐藏模式',\n        description: 'VPA完全隐藏',\n        component: null,\n        defaultSize: null,\n        animation: null,\n        position: null\n      }\n    },\n    // VPA动画状态\n    animationState: 'idle',\n    // idle | talking | listening | thinking | greeting | sleeping\n\n    // VPA可用动画\n    availableAnimations: {\n      idle: {\n        name: '待机',\n        resource: 'vpa2.gif',\n        duration: 3000,\n        loop: true\n      },\n      talking: {\n        name: '说话',\n        resource: 'vpn1.gif',\n        duration: 2000,\n        loop: true\n      },\n      listening: {\n        name: '聆听',\n        resource: 'vpa2.gif',\n        duration: 1500,\n        loop: true\n      },\n      thinking: {\n        name: '思考',\n        resource: 'vpa2.gif',\n        duration: 2500,\n        loop: true\n      },\n      greeting: {\n        name: '问候',\n        resource: 'vpn1.gif',\n        duration: 1000,\n        loop: false\n      },\n      sleeping: {\n        name: '休眠',\n        resource: 'vpa2.gif',\n        duration: 5000,\n        loop: true\n      }\n    },\n    // VPA个性化设置\n    personality: {\n      name: 'AI助手',\n      voice: 'female',\n      language: 'zh-CN',\n      responseStyle: 'friendly',\n      // friendly | professional | casual\n      emotionalLevel: 'medium' // low | medium | high\n    },\n    // VPA交互历史\n    interactionHistory: [],\n    // VPA当前对话状态\n    conversationState: {\n      isActive: false,\n      currentTopic: null,\n      contextLevel: 0,\n      // 0-5, 上下文理解深度\n      lastInteraction: null\n    },\n    // VPA能力状态\n    capabilities: {\n      voiceRecognition: true,\n      textToSpeech: true,\n      emotionRecognition: false,\n      gestureRecognition: false,\n      contextAwareness: true\n    },\n    // VPA显示配置\n    displayConfig: {\n      showName: true,\n      showStatus: true,\n      showAnimation: true,\n      transparency: 0.95,\n      blurEffect: true\n    }\n  }),\n  getters: {\n    // 获取当前VPA模式配置\n    currentModeConfig: state => {\n      return state.availableModes[state.currentMode];\n    },\n    // 获取当前动画配置\n    currentAnimationConfig: state => {\n      return state.availableAnimations[state.animationState];\n    },\n    // 检查VPA是否可见\n    isVisible: state => {\n      return state.currentMode !== 'hidden';\n    },\n    // 检查VPA是否在交互中\n    isInteracting: state => {\n      return state.conversationState.isActive;\n    },\n    // 获取VPA组件信息\n    vpaComponentInfo: state => {\n      const modeConfig = state.availableModes[state.currentMode];\n      const animationConfig = state.availableAnimations[state.animationState];\n      return {\n        component: modeConfig?.component,\n        size: modeConfig?.defaultSize,\n        position: modeConfig?.position,\n        animation: animationConfig,\n        displayConfig: state.displayConfig\n      };\n    }\n  },\n  actions: {\n    // 切换VPA模式\n    switchMode(mode) {\n      if (this.availableModes[mode]) {\n        const previousMode = this.currentMode;\n        this.currentMode = mode;\n\n        // 根据模式切换动画\n        if (mode === 'interaction') {\n          this.setAnimation('talking');\n        } else if (mode === 'companion') {\n          this.setAnimation('idle');\n        }\n        console.log(`VPA模式已切换: ${previousMode} -> ${mode}`);\n\n        // 记录交互历史\n        this.addInteractionHistory({\n          type: 'mode_switch',\n          from: previousMode,\n          to: mode,\n          timestamp: new Date()\n        });\n      }\n    },\n    // 设置VPA动画\n    setAnimation(animationName) {\n      if (this.availableAnimations[animationName]) {\n        this.animationState = animationName;\n        console.log(`VPA动画已切换到: ${animationName}`);\n      }\n    },\n    // 开始对话\n    startConversation(topic = null) {\n      this.conversationState.isActive = true;\n      this.conversationState.currentTopic = topic;\n      this.conversationState.lastInteraction = new Date();\n\n      // 切换到交互模式\n      if (this.currentMode === 'companion') {\n        this.switchMode('interaction');\n      }\n      this.setAnimation('listening');\n    },\n    // 结束对话\n    endConversation() {\n      this.conversationState.isActive = false;\n      this.conversationState.currentTopic = null;\n\n      // 切换回陪伴模式\n      if (this.currentMode === 'interaction') {\n        this.switchMode('companion');\n      }\n      this.setAnimation('idle');\n    },\n    // 添加交互历史\n    addInteractionHistory(interaction) {\n      this.interactionHistory.unshift(interaction);\n\n      // 保持历史记录在合理范围内\n      if (this.interactionHistory.length > 100) {\n        this.interactionHistory = this.interactionHistory.slice(0, 100);\n      }\n    },\n    // 更新个性化设置\n    updatePersonality(settings) {\n      this.personality = {\n        ...this.personality,\n        ...settings\n      };\n    },\n    // 更新显示配置\n    updateDisplayConfig(config) {\n      this.displayConfig = {\n        ...this.displayConfig,\n        ...config\n      };\n    },\n    // VPA问候\n    greet() {\n      this.setAnimation('greeting');\n\n      // 2秒后回到待机状态\n      setTimeout(() => {\n        this.setAnimation('idle');\n      }, 2000);\n      this.addInteractionHistory({\n        type: 'greeting',\n        timestamp: new Date()\n      });\n    },\n    // VPA休眠\n    sleep() {\n      this.setAnimation('sleeping');\n      this.conversationState.isActive = false;\n    },\n    // VPA唤醒\n    wakeUp() {\n      this.setAnimation('greeting');\n      setTimeout(() => {\n        this.setAnimation('idle');\n      }, 1000);\n    }\n  }\n});", "map": {"version": 3, "names": ["defineStore", "useVpaStore", "state", "currentMode", "availableModes", "companion", "name", "description", "component", "defaultSize", "animation", "position", "x", "y", "interaction", "hidden", "animationState", "availableAnimations", "idle", "resource", "duration", "loop", "talking", "listening", "thinking", "greeting", "sleeping", "personality", "voice", "language", "responseStyle", "emotionalLevel", "interactionHistory", "conversationState", "isActive", "currentTopic", "contextLevel", "lastInteraction", "capabilities", "voiceRecognition", "textToSpeech", "emotionRecognition", "gestureRecognition", "contextAwareness", "displayConfig", "showName", "showStatus", "showAnimation", "transparency", "blurEffect", "getters", "currentModeConfig", "currentAnimationConfig", "isVisible", "isInteracting", "vpaComponentInfo", "modeConfig", "animationConfig", "size", "actions", "switchMode", "mode", "previousMode", "setAnimation", "console", "log", "addInteractionHistory", "type", "from", "to", "timestamp", "Date", "animationName", "startConversation", "topic", "endConversation", "unshift", "length", "slice", "updatePersonality", "settings", "updateDisplayConfig", "config", "greet", "setTimeout", "sleep", "wakeUp"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/store/modules/vpa.js"], "sourcesContent": ["import { defineStore } from 'pinia'\r\n\r\nexport const useVpaStore = defineStore('vpa', {\r\n  state: () => ({\r\n    // VPA当前状态\r\n    currentMode: 'companion', // companion(陪伴模式) | interaction(交互模式) | hidden(隐藏)\r\n    \r\n    // VPA可用模式\r\n    availableModes: {\r\n      companion: {\r\n        name: '陪伴模式',\r\n        description: 'VPA以小窗形式陪伴用户',\r\n        component: 'VPAAvatarWidget',\r\n        defaultSize: 'vpaWidget', // 2x2\r\n        animation: 'idle',\r\n        position: { x: 15, y: 7 }\r\n      },\r\n      interaction: {\r\n        name: '交互模式',\r\n        description: 'VPA展开为交互面板',\r\n        component: 'VPAInteractionPanel',\r\n        defaultSize: 'vpaInteractionLarge', // 8x9\r\n        animation: 'talking',\r\n        position: { x: 9, y: 1 }\r\n      },\r\n      hidden: {\r\n        name: '隐藏模式',\r\n        description: 'VPA完全隐藏',\r\n        component: null,\r\n        defaultSize: null,\r\n        animation: null,\r\n        position: null\r\n      }\r\n    },\r\n    \r\n    // VPA动画状态\r\n    animationState: 'idle', // idle | talking | listening | thinking | greeting | sleeping\r\n    \r\n    // VPA可用动画\r\n    availableAnimations: {\r\n      idle: {\r\n        name: '待机',\r\n        resource: 'vpa2.gif',\r\n        duration: 3000,\r\n        loop: true\r\n      },\r\n      talking: {\r\n        name: '说话',\r\n        resource: 'vpn1.gif',\r\n        duration: 2000,\r\n        loop: true\r\n      },\r\n      listening: {\r\n        name: '聆听',\r\n        resource: 'vpa2.gif',\r\n        duration: 1500,\r\n        loop: true\r\n      },\r\n      thinking: {\r\n        name: '思考',\r\n        resource: 'vpa2.gif',\r\n        duration: 2500,\r\n        loop: true\r\n      },\r\n      greeting: {\r\n        name: '问候',\r\n        resource: 'vpn1.gif',\r\n        duration: 1000,\r\n        loop: false\r\n      },\r\n      sleeping: {\r\n        name: '休眠',\r\n        resource: 'vpa2.gif',\r\n        duration: 5000,\r\n        loop: true\r\n      }\r\n    },\r\n    \r\n    // VPA个性化设置\r\n    personality: {\r\n      name: 'AI助手',\r\n      voice: 'female',\r\n      language: 'zh-CN',\r\n      responseStyle: 'friendly', // friendly | professional | casual\r\n      emotionalLevel: 'medium' // low | medium | high\r\n    },\r\n    \r\n    // VPA交互历史\r\n    interactionHistory: [],\r\n    \r\n    // VPA当前对话状态\r\n    conversationState: {\r\n      isActive: false,\r\n      currentTopic: null,\r\n      contextLevel: 0, // 0-5, 上下文理解深度\r\n      lastInteraction: null\r\n    },\r\n    \r\n    // VPA能力状态\r\n    capabilities: {\r\n      voiceRecognition: true,\r\n      textToSpeech: true,\r\n      emotionRecognition: false,\r\n      gestureRecognition: false,\r\n      contextAwareness: true\r\n    },\r\n    \r\n    // VPA显示配置\r\n    displayConfig: {\r\n      showName: true,\r\n      showStatus: true,\r\n      showAnimation: true,\r\n      transparency: 0.95,\r\n      blurEffect: true\r\n    }\r\n  }),\r\n  \r\n  getters: {\r\n    // 获取当前VPA模式配置\r\n    currentModeConfig: (state) => {\r\n      return state.availableModes[state.currentMode]\r\n    },\r\n    \r\n    // 获取当前动画配置\r\n    currentAnimationConfig: (state) => {\r\n      return state.availableAnimations[state.animationState]\r\n    },\r\n    \r\n    // 检查VPA是否可见\r\n    isVisible: (state) => {\r\n      return state.currentMode !== 'hidden'\r\n    },\r\n    \r\n    // 检查VPA是否在交互中\r\n    isInteracting: (state) => {\r\n      return state.conversationState.isActive\r\n    },\r\n    \r\n    // 获取VPA组件信息\r\n    vpaComponentInfo: (state) => {\r\n      const modeConfig = state.availableModes[state.currentMode]\r\n      const animationConfig = state.availableAnimations[state.animationState]\r\n      \r\n      return {\r\n        component: modeConfig?.component,\r\n        size: modeConfig?.defaultSize,\r\n        position: modeConfig?.position,\r\n        animation: animationConfig,\r\n        displayConfig: state.displayConfig\r\n      }\r\n    }\r\n  },\r\n  \r\n  actions: {\r\n    // 切换VPA模式\r\n    switchMode(mode) {\r\n      if (this.availableModes[mode]) {\r\n        const previousMode = this.currentMode\r\n        this.currentMode = mode\r\n        \r\n        // 根据模式切换动画\r\n        if (mode === 'interaction') {\r\n          this.setAnimation('talking')\r\n        } else if (mode === 'companion') {\r\n          this.setAnimation('idle')\r\n        }\r\n        \r\n        console.log(`VPA模式已切换: ${previousMode} -> ${mode}`)\r\n        \r\n        // 记录交互历史\r\n        this.addInteractionHistory({\r\n          type: 'mode_switch',\r\n          from: previousMode,\r\n          to: mode,\r\n          timestamp: new Date()\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 设置VPA动画\r\n    setAnimation(animationName) {\r\n      if (this.availableAnimations[animationName]) {\r\n        this.animationState = animationName\r\n        console.log(`VPA动画已切换到: ${animationName}`)\r\n      }\r\n    },\r\n    \r\n    // 开始对话\r\n    startConversation(topic = null) {\r\n      this.conversationState.isActive = true\r\n      this.conversationState.currentTopic = topic\r\n      this.conversationState.lastInteraction = new Date()\r\n      \r\n      // 切换到交互模式\r\n      if (this.currentMode === 'companion') {\r\n        this.switchMode('interaction')\r\n      }\r\n      \r\n      this.setAnimation('listening')\r\n    },\r\n    \r\n    // 结束对话\r\n    endConversation() {\r\n      this.conversationState.isActive = false\r\n      this.conversationState.currentTopic = null\r\n      \r\n      // 切换回陪伴模式\r\n      if (this.currentMode === 'interaction') {\r\n        this.switchMode('companion')\r\n      }\r\n      \r\n      this.setAnimation('idle')\r\n    },\r\n    \r\n    // 添加交互历史\r\n    addInteractionHistory(interaction) {\r\n      this.interactionHistory.unshift(interaction)\r\n      \r\n      // 保持历史记录在合理范围内\r\n      if (this.interactionHistory.length > 100) {\r\n        this.interactionHistory = this.interactionHistory.slice(0, 100)\r\n      }\r\n    },\r\n    \r\n    // 更新个性化设置\r\n    updatePersonality(settings) {\r\n      this.personality = { ...this.personality, ...settings }\r\n    },\r\n    \r\n    // 更新显示配置\r\n    updateDisplayConfig(config) {\r\n      this.displayConfig = { ...this.displayConfig, ...config }\r\n    },\r\n    \r\n    // VPA问候\r\n    greet() {\r\n      this.setAnimation('greeting')\r\n      \r\n      // 2秒后回到待机状态\r\n      setTimeout(() => {\r\n        this.setAnimation('idle')\r\n      }, 2000)\r\n      \r\n      this.addInteractionHistory({\r\n        type: 'greeting',\r\n        timestamp: new Date()\r\n      })\r\n    },\r\n    \r\n    // VPA休眠\r\n    sleep() {\r\n      this.setAnimation('sleeping')\r\n      this.conversationState.isActive = false\r\n    },\r\n    \r\n    // VPA唤醒\r\n    wakeUp() {\r\n      this.setAnimation('greeting')\r\n      \r\n      setTimeout(() => {\r\n        this.setAnimation('idle')\r\n      }, 1000)\r\n    }\r\n  }\r\n})\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AAEnC,OAAO,MAAMC,WAAW,GAAGD,WAAW,CAAC,KAAK,EAAE;EAC5CE,KAAK,EAAEA,CAAA,MAAO;IACZ;IACAC,WAAW,EAAE,WAAW;IAAE;;IAE1B;IACAC,cAAc,EAAE;MACdC,SAAS,EAAE;QACTC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,cAAc;QAC3BC,SAAS,EAAE,iBAAiB;QAC5BC,WAAW,EAAE,WAAW;QAAE;QAC1BC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;MAC1B,CAAC;MACDC,WAAW,EAAE;QACXR,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,YAAY;QACzBC,SAAS,EAAE,qBAAqB;QAChCC,WAAW,EAAE,qBAAqB;QAAE;QACpCC,SAAS,EAAE,SAAS;QACpBC,QAAQ,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE;MACzB,CAAC;MACDE,MAAM,EAAE;QACNT,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ;IACF,CAAC;IAED;IACAK,cAAc,EAAE,MAAM;IAAE;;IAExB;IACAC,mBAAmB,EAAE;MACnBC,IAAI,EAAE;QACJZ,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;QACPhB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDE,SAAS,EAAE;QACTjB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDG,QAAQ,EAAE;QACRlB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDI,QAAQ,EAAE;QACRnB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR,CAAC;MACDK,QAAQ,EAAE;QACRpB,IAAI,EAAE,IAAI;QACVa,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;MACR;IACF,CAAC;IAED;IACAM,WAAW,EAAE;MACXrB,IAAI,EAAE,MAAM;MACZsB,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE,OAAO;MACjBC,aAAa,EAAE,UAAU;MAAE;MAC3BC,cAAc,EAAE,QAAQ,CAAC;IAC3B,CAAC;IAED;IACAC,kBAAkB,EAAE,EAAE;IAEtB;IACAC,iBAAiB,EAAE;MACjBC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,CAAC;MAAE;MACjBC,eAAe,EAAE;IACnB,CAAC;IAED;IACAC,YAAY,EAAE;MACZC,gBAAgB,EAAE,IAAI;MACtBC,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE,KAAK;MACzBC,kBAAkB,EAAE,KAAK;MACzBC,gBAAgB,EAAE;IACpB,CAAC;IAED;IACAC,aAAa,EAAE;MACbC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;EAEFC,OAAO,EAAE;IACP;IACAC,iBAAiB,EAAGjD,KAAK,IAAK;MAC5B,OAAOA,KAAK,CAACE,cAAc,CAACF,KAAK,CAACC,WAAW,CAAC;IAChD,CAAC;IAED;IACAiD,sBAAsB,EAAGlD,KAAK,IAAK;MACjC,OAAOA,KAAK,CAACe,mBAAmB,CAACf,KAAK,CAACc,cAAc,CAAC;IACxD,CAAC;IAED;IACAqC,SAAS,EAAGnD,KAAK,IAAK;MACpB,OAAOA,KAAK,CAACC,WAAW,KAAK,QAAQ;IACvC,CAAC;IAED;IACAmD,aAAa,EAAGpD,KAAK,IAAK;MACxB,OAAOA,KAAK,CAAC+B,iBAAiB,CAACC,QAAQ;IACzC,CAAC;IAED;IACAqB,gBAAgB,EAAGrD,KAAK,IAAK;MAC3B,MAAMsD,UAAU,GAAGtD,KAAK,CAACE,cAAc,CAACF,KAAK,CAACC,WAAW,CAAC;MAC1D,MAAMsD,eAAe,GAAGvD,KAAK,CAACe,mBAAmB,CAACf,KAAK,CAACc,cAAc,CAAC;MAEvE,OAAO;QACLR,SAAS,EAAEgD,UAAU,EAAEhD,SAAS;QAChCkD,IAAI,EAAEF,UAAU,EAAE/C,WAAW;QAC7BE,QAAQ,EAAE6C,UAAU,EAAE7C,QAAQ;QAC9BD,SAAS,EAAE+C,eAAe;QAC1Bb,aAAa,EAAE1C,KAAK,CAAC0C;MACvB,CAAC;IACH;EACF,CAAC;EAEDe,OAAO,EAAE;IACP;IACAC,UAAUA,CAACC,IAAI,EAAE;MACf,IAAI,IAAI,CAACzD,cAAc,CAACyD,IAAI,CAAC,EAAE;QAC7B,MAAMC,YAAY,GAAG,IAAI,CAAC3D,WAAW;QACrC,IAAI,CAACA,WAAW,GAAG0D,IAAI;;QAEvB;QACA,IAAIA,IAAI,KAAK,aAAa,EAAE;UAC1B,IAAI,CAACE,YAAY,CAAC,SAAS,CAAC;QAC9B,CAAC,MAAM,IAAIF,IAAI,KAAK,WAAW,EAAE;UAC/B,IAAI,CAACE,YAAY,CAAC,MAAM,CAAC;QAC3B;QAEAC,OAAO,CAACC,GAAG,CAAC,aAAaH,YAAY,OAAOD,IAAI,EAAE,CAAC;;QAEnD;QACA,IAAI,CAACK,qBAAqB,CAAC;UACzBC,IAAI,EAAE,aAAa;UACnBC,IAAI,EAAEN,YAAY;UAClBO,EAAE,EAAER,IAAI;UACRS,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACAR,YAAYA,CAACS,aAAa,EAAE;MAC1B,IAAI,IAAI,CAACvD,mBAAmB,CAACuD,aAAa,CAAC,EAAE;QAC3C,IAAI,CAACxD,cAAc,GAAGwD,aAAa;QACnCR,OAAO,CAACC,GAAG,CAAC,cAAcO,aAAa,EAAE,CAAC;MAC5C;IACF,CAAC;IAED;IACAC,iBAAiBA,CAACC,KAAK,GAAG,IAAI,EAAE;MAC9B,IAAI,CAACzC,iBAAiB,CAACC,QAAQ,GAAG,IAAI;MACtC,IAAI,CAACD,iBAAiB,CAACE,YAAY,GAAGuC,KAAK;MAC3C,IAAI,CAACzC,iBAAiB,CAACI,eAAe,GAAG,IAAIkC,IAAI,CAAC,CAAC;;MAEnD;MACA,IAAI,IAAI,CAACpE,WAAW,KAAK,WAAW,EAAE;QACpC,IAAI,CAACyD,UAAU,CAAC,aAAa,CAAC;MAChC;MAEA,IAAI,CAACG,YAAY,CAAC,WAAW,CAAC;IAChC,CAAC;IAED;IACAY,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC1C,iBAAiB,CAACC,QAAQ,GAAG,KAAK;MACvC,IAAI,CAACD,iBAAiB,CAACE,YAAY,GAAG,IAAI;;MAE1C;MACA,IAAI,IAAI,CAAChC,WAAW,KAAK,aAAa,EAAE;QACtC,IAAI,CAACyD,UAAU,CAAC,WAAW,CAAC;MAC9B;MAEA,IAAI,CAACG,YAAY,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED;IACAG,qBAAqBA,CAACpD,WAAW,EAAE;MACjC,IAAI,CAACkB,kBAAkB,CAAC4C,OAAO,CAAC9D,WAAW,CAAC;;MAE5C;MACA,IAAI,IAAI,CAACkB,kBAAkB,CAAC6C,MAAM,GAAG,GAAG,EAAE;QACxC,IAAI,CAAC7C,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC8C,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;MACjE;IACF,CAAC;IAED;IACAC,iBAAiBA,CAACC,QAAQ,EAAE;MAC1B,IAAI,CAACrD,WAAW,GAAG;QAAE,GAAG,IAAI,CAACA,WAAW;QAAE,GAAGqD;MAAS,CAAC;IACzD,CAAC;IAED;IACAC,mBAAmBA,CAACC,MAAM,EAAE;MAC1B,IAAI,CAACtC,aAAa,GAAG;QAAE,GAAG,IAAI,CAACA,aAAa;QAAE,GAAGsC;MAAO,CAAC;IAC3D,CAAC;IAED;IACAC,KAAKA,CAAA,EAAG;MACN,IAAI,CAACpB,YAAY,CAAC,UAAU,CAAC;;MAE7B;MACAqB,UAAU,CAAC,MAAM;QACf,IAAI,CAACrB,YAAY,CAAC,MAAM,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;MAER,IAAI,CAACG,qBAAqB,CAAC;QACzBC,IAAI,EAAE,UAAU;QAChBG,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;IAED;IACAc,KAAKA,CAAA,EAAG;MACN,IAAI,CAACtB,YAAY,CAAC,UAAU,CAAC;MAC7B,IAAI,CAAC9B,iBAAiB,CAACC,QAAQ,GAAG,KAAK;IACzC,CAAC;IAED;IACAoD,MAAMA,CAAA,EAAG;MACP,IAAI,CAACvB,YAAY,CAAC,UAAU,CAAC;MAE7BqB,UAAU,CAAC,MAAM;QACf,IAAI,CAACrB,YAAY,CAAC,MAAM,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}