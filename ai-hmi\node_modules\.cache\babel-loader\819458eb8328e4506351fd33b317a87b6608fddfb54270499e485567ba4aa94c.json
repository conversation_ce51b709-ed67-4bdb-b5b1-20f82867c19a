{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"education-content\"\n};\nconst _hoisted_2 = {\n  class: \"current-lesson\"\n};\nconst _hoisted_3 = {\n  class: \"lesson-header\"\n};\nconst _hoisted_4 = {\n  class: \"lesson-icon\"\n};\nconst _hoisted_5 = {\n  class: \"lesson-info\"\n};\nconst _hoisted_6 = {\n  class: \"lesson-title\"\n};\nconst _hoisted_7 = {\n  class: \"lesson-description\"\n};\nconst _hoisted_8 = {\n  class: \"lesson-progress\"\n};\nconst _hoisted_9 = {\n  class: \"progress-bar\"\n};\nconst _hoisted_10 = {\n  class: \"progress-text\"\n};\nconst _hoisted_11 = {\n  class: \"learning-modes\"\n};\nconst _hoisted_12 = [\"onClick\"];\nconst _hoisted_13 = {\n  class: \"interaction-area\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  class: \"story-mode\"\n};\nconst _hoisted_15 = {\n  class: \"story-content\"\n};\nconst _hoisted_16 = {\n  class: \"story-controls\"\n};\nconst _hoisted_17 = {\n  key: 1,\n  class: \"quiz-mode\"\n};\nconst _hoisted_18 = {\n  class: \"quiz-question\"\n};\nconst _hoisted_19 = {\n  class: \"quiz-options\"\n};\nconst _hoisted_20 = [\"onClick\"];\nconst _hoisted_21 = {\n  key: 2,\n  class: \"game-mode\"\n};\nconst _hoisted_22 = {\n  class: \"game-content\"\n};\nconst _hoisted_23 = {\n  class: \"number-game\"\n};\nconst _hoisted_24 = {\n  class: \"game-display\"\n};\nconst _hoisted_25 = {\n  class: \"game-controls\"\n};\nconst _hoisted_26 = {\n  class: \"card-footer-content\"\n};\nconst _hoisted_27 = {\n  class: \"achievement-info\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_BaseCard = _resolveComponent(\"BaseCard\");\n  return _openBlock(), _createBlock(_component_BaseCard, {\n    \"card-type\": \"kidEducation\",\n    size: \"large\",\n    position: $props.position,\n    theme: $props.theme,\n    \"theme-colors\": $props.themeColors,\n    title: '儿童教育',\n    icon: 'fas fa-graduation-cap',\n    clickable: true,\n    \"show-footer\": true,\n    onClick: $setup.handleCardClick,\n    class: \"kid-education-card\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_cache[10] || (_cache[10] = _createElementVNode(\"i\", {\n      class: \"fas fa-star\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, \"今日获得 \" + _toDisplayString($setup.todayStars) + \" 颗星\", 1 /* TEXT */)]), _createElementVNode(\"button\", {\n      onClick: _cache[6] || (_cache[6] = (...args) => $setup.openFullEducation && $setup.openFullEducation(...args)),\n      class: \"expand-btn\"\n    }, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n      class: \"fas fa-expand\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"展开\", -1 /* CACHED */)]))])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 当前学习内容 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.currentLesson.icon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"h4\", _hoisted_6, _toDisplayString($setup.currentLesson.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_7, _toDisplayString($setup.currentLesson.description), 1 /* TEXT */)])]), _createCommentVNode(\" 学习进度 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: `${$setup.currentLesson.progress}%`\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"span\", _hoisted_10, _toDisplayString($setup.currentLesson.progress) + \"% 完成\", 1 /* TEXT */)])]), _createCommentVNode(\" 学习模式选择 \"), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.learningModes, mode => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: mode.id,\n        onClick: $event => $setup.selectLearningMode(mode),\n        class: _normalizeClass(['mode-btn', {\n          active: $setup.selectedMode === mode.id\n        }])\n      }, [_createElementVNode(\"i\", {\n        class: _normalizeClass(mode.icon)\n      }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(mode.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_12);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 互动区域 \"), _createElementVNode(\"div\", _hoisted_13, [$setup.selectedMode === 'story' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"h5\", null, _toDisplayString($setup.currentStory.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString($setup.currentStory.content), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", {\n      onClick: _cache[0] || (_cache[0] = (...args) => $setup.previousStory && $setup.previousStory(...args)),\n      class: \"story-btn\"\n    }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-backward\"\n    }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n      onClick: _cache[1] || (_cache[1] = (...args) => $setup.toggleStoryPlay && $setup.toggleStoryPlay(...args)),\n      class: \"story-btn play-btn\"\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass($setup.isPlaying ? 'fas fa-pause' : 'fas fa-play')\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"button\", {\n      onClick: _cache[2] || (_cache[2] = (...args) => $setup.nextStory && $setup.nextStory(...args)),\n      class: \"story-btn\"\n    }, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n      class: \"fas fa-step-forward\"\n    }, null, -1 /* CACHED */)]))])])) : $setup.selectedMode === 'quiz' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"h5\", null, _toDisplayString($setup.currentQuiz.question), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentQuiz.options, (option, index) => {\n      return _openBlock(), _createElementBlock(\"button\", {\n        key: index,\n        onClick: $event => $setup.selectQuizOption(index),\n        class: _normalizeClass(['quiz-option', {\n          selected: $setup.selectedOption === index,\n          correct: $setup.showAnswer && index === $setup.currentQuiz.correct,\n          wrong: $setup.showAnswer && $setup.selectedOption === index && index !== $setup.currentQuiz.correct\n        }])\n      }, _toDisplayString(option), 11 /* TEXT, CLASS, PROPS */, _hoisted_20);\n    }), 128 /* KEYED_FRAGMENT */))])])])) : $setup.selectedMode === 'game' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[9] || (_cache[9] = _createElementVNode(\"h5\", null, \"数字游戏\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString($setup.gameNumber), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"button\", {\n      onClick: _cache[3] || (_cache[3] = $event => $setup.gameAction('add')),\n      class: \"game-btn\"\n    }, \"+1\"), _createElementVNode(\"button\", {\n      onClick: _cache[4] || (_cache[4] = $event => $setup.gameAction('subtract')),\n      class: \"game-btn\"\n    }, \"-1\"), _createElementVNode(\"button\", {\n      onClick: _cache[5] || (_cache[5] = $event => $setup.gameAction('reset')),\n      class: \"game-btn\"\n    }, \"重置\")])])])])) : _createCommentVNode(\"v-if\", true)])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"position\", \"theme\", \"theme-colors\", \"onClick\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_BaseCard", "size", "position", "$props", "theme", "themeColors", "title", "icon", "clickable", "onClick", "$setup", "handleCardClick", "footer", "_withCtx", "_createElementVNode", "_hoisted_26", "_hoisted_27", "_toDisplayString", "todayStars", "_cache", "args", "openFullEducation", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_normalizeClass", "<PERSON><PERSON><PERSON><PERSON>", "_hoisted_5", "_hoisted_6", "_hoisted_7", "description", "_hoisted_8", "_hoisted_9", "style", "_normalizeStyle", "width", "progress", "_hoisted_10", "_hoisted_11", "_createElementBlock", "_Fragment", "_renderList", "learningModes", "mode", "key", "id", "$event", "selectLearningMode", "active", "selectedMode", "name", "_hoisted_13", "_hoisted_14", "_hoisted_15", "currentStory", "content", "_hoisted_16", "previousStory", "toggleStoryPlay", "isPlaying", "nextStory", "_hoisted_17", "_hoisted_18", "currentQuiz", "question", "_hoisted_19", "options", "option", "index", "selectQuizOption", "selectedOption", "showAnswer", "correct", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "gameNumber", "_hoisted_25", "gameAction"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue"], "sourcesContent": ["<template>\r\n  <BaseCard\r\n    card-type=\"kidEducation\"\r\n    size=\"large\"\r\n    :position=\"position\"\r\n    :theme=\"theme\"\r\n    :theme-colors=\"themeColors\"\r\n    :title=\"'儿童教育'\"\r\n    :icon=\"'fas fa-graduation-cap'\"\r\n    :clickable=\"true\"\r\n    :show-footer=\"true\"\r\n    @click=\"handleCardClick\"\r\n    class=\"kid-education-card\"\r\n  >\r\n    <div class=\"education-content\">\r\n      <!-- 当前学习内容 -->\r\n      <div class=\"current-lesson\">\r\n        <div class=\"lesson-header\">\r\n          <div class=\"lesson-icon\">\r\n            <i :class=\"currentLesson.icon\"></i>\r\n          </div>\r\n          <div class=\"lesson-info\">\r\n            <h4 class=\"lesson-title\">{{ currentLesson.title }}</h4>\r\n            <p class=\"lesson-description\">{{ currentLesson.description }}</p>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 学习进度 -->\r\n        <div class=\"lesson-progress\">\r\n          <div class=\"progress-bar\">\r\n            <div \r\n              class=\"progress-fill\" \r\n              :style=\"{ width: `${currentLesson.progress}%` }\"\r\n            ></div>\r\n          </div>\r\n          <span class=\"progress-text\">{{ currentLesson.progress }}% 完成</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 学习模式选择 -->\r\n      <div class=\"learning-modes\">\r\n        <button \r\n          v-for=\"mode in learningModes\" \r\n          :key=\"mode.id\"\r\n          @click=\"selectLearningMode(mode)\"\r\n          :class=\"['mode-btn', { active: selectedMode === mode.id }]\"\r\n        >\r\n          <i :class=\"mode.icon\"></i>\r\n          <span>{{ mode.name }}</span>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 互动区域 -->\r\n      <div class=\"interaction-area\">\r\n        <div v-if=\"selectedMode === 'story'\" class=\"story-mode\">\r\n          <div class=\"story-content\">\r\n            <h5>{{ currentStory.title }}</h5>\r\n            <p>{{ currentStory.content }}</p>\r\n          </div>\r\n          <div class=\"story-controls\">\r\n            <button @click=\"previousStory\" class=\"story-btn\">\r\n              <i class=\"fas fa-step-backward\"></i>\r\n            </button>\r\n            <button @click=\"toggleStoryPlay\" class=\"story-btn play-btn\">\r\n              <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\r\n            </button>\r\n            <button @click=\"nextStory\" class=\"story-btn\">\r\n              <i class=\"fas fa-step-forward\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-else-if=\"selectedMode === 'quiz'\" class=\"quiz-mode\">\r\n          <div class=\"quiz-question\">\r\n            <h5>{{ currentQuiz.question }}</h5>\r\n            <div class=\"quiz-options\">\r\n              <button \r\n                v-for=\"(option, index) in currentQuiz.options\" \r\n                :key=\"index\"\r\n                @click=\"selectQuizOption(index)\"\r\n                :class=\"['quiz-option', { \r\n                  selected: selectedOption === index,\r\n                  correct: showAnswer && index === currentQuiz.correct,\r\n                  wrong: showAnswer && selectedOption === index && index !== currentQuiz.correct\r\n                }]\"\r\n              >\r\n                {{ option }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-else-if=\"selectedMode === 'game'\" class=\"game-mode\">\r\n          <div class=\"game-content\">\r\n            <h5>数字游戏</h5>\r\n            <div class=\"number-game\">\r\n              <div class=\"game-display\">{{ gameNumber }}</div>\r\n              <div class=\"game-controls\">\r\n                <button @click=\"gameAction('add')\" class=\"game-btn\">+1</button>\r\n                <button @click=\"gameAction('subtract')\" class=\"game-btn\">-1</button>\r\n                <button @click=\"gameAction('reset')\" class=\"game-btn\">重置</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <div class=\"card-footer-content\">\r\n        <div class=\"achievement-info\">\r\n          <i class=\"fas fa-star\"></i>\r\n          <span>今日获得 {{ todayStars }} 颗星</span>\r\n        </div>\r\n        <button @click=\"openFullEducation\" class=\"expand-btn\">\r\n          <i class=\"fas fa-expand\"></i>\r\n          <span>展开</span>\r\n        </button>\r\n      </div>\r\n    </template>\r\n  </BaseCard>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport BaseCard from '../BaseCard.vue'\r\n\r\nexport default {\r\n  name: 'KidEducationCard',\r\n  components: {\r\n    BaseCard\r\n  },\r\n  props: {\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 2 })\r\n    },\r\n    theme: {\r\n      type: String,\r\n      default: 'glass'\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({\r\n        primary: '#ff6b6b',\r\n        secondary: '#4ecdc4',\r\n        background: 'rgba(255, 107, 107, 0.1)',\r\n        text: '#ffffff'\r\n      })\r\n    }\r\n  },\r\n  \r\n  emits: ['card-click', 'mode-changed', 'lesson-completed'],\r\n  \r\n  setup(props, { emit }) {\r\n    // 响应式状态\r\n    const selectedMode = ref('story')\r\n    const isPlaying = ref(false)\r\n    const selectedOption = ref(null)\r\n    const showAnswer = ref(false)\r\n    const gameNumber = ref(0)\r\n    const todayStars = ref(3)\r\n    \r\n    // 当前课程数据\r\n    const currentLesson = ref({\r\n      title: '认识动物',\r\n      description: '学习各种动物的名称和特征',\r\n      icon: 'fas fa-paw',\r\n      progress: 65\r\n    })\r\n    \r\n    // 学习模式\r\n    const learningModes = ref([\r\n      { id: 'story', name: '故事', icon: 'fas fa-book' },\r\n      { id: 'quiz', name: '问答', icon: 'fas fa-question-circle' },\r\n      { id: 'game', name: '游戏', icon: 'fas fa-gamepad' }\r\n    ])\r\n    \r\n    // 故事内容\r\n    const stories = ref([\r\n      {\r\n        title: '小兔子的冒险',\r\n        content: '从前有一只小兔子，它住在美丽的森林里...'\r\n      },\r\n      {\r\n        title: '勇敢的小狮子',\r\n        content: '在非洲大草原上，有一只勇敢的小狮子...'\r\n      }\r\n    ])\r\n    \r\n    const currentStoryIndex = ref(0)\r\n    const currentStory = computed(() => stories.value[currentStoryIndex.value])\r\n    \r\n    // 问答内容\r\n    const quizzes = ref([\r\n      {\r\n        question: '小兔子最喜欢吃什么？',\r\n        options: ['胡萝卜', '肉', '鱼', '草'],\r\n        correct: 0\r\n      },\r\n      {\r\n        question: '狮子是什么动物？',\r\n        options: ['食草动物', '食肉动物', '杂食动物', '不知道'],\r\n        correct: 1\r\n      }\r\n    ])\r\n    \r\n    const currentQuizIndex = ref(0)\r\n    const currentQuiz = computed(() => quizzes.value[currentQuizIndex.value])\r\n    \r\n    // 事件处理\r\n    const handleCardClick = () => {\r\n      emit('card-click', 'kidEducation')\r\n    }\r\n    \r\n    const selectLearningMode = (mode) => {\r\n      selectedMode.value = mode.id\r\n      emit('mode-changed', mode.id)\r\n      \r\n      // 重置相关状态\r\n      if (mode.id === 'quiz') {\r\n        selectedOption.value = null\r\n        showAnswer.value = false\r\n      }\r\n    }\r\n    \r\n    const previousStory = () => {\r\n      if (currentStoryIndex.value > 0) {\r\n        currentStoryIndex.value--\r\n      }\r\n    }\r\n    \r\n    const nextStory = () => {\r\n      if (currentStoryIndex.value < stories.value.length - 1) {\r\n        currentStoryIndex.value++\r\n      }\r\n    }\r\n    \r\n    const toggleStoryPlay = () => {\r\n      isPlaying.value = !isPlaying.value\r\n      // 这里可以集成TTS服务来朗读故事\r\n    }\r\n    \r\n    const selectQuizOption = (index) => {\r\n      if (showAnswer.value) return\r\n      \r\n      selectedOption.value = index\r\n      showAnswer.value = true\r\n      \r\n      // 2秒后自动进入下一题\r\n      setTimeout(() => {\r\n        if (currentQuizIndex.value < quizzes.value.length - 1) {\r\n          currentQuizIndex.value++\r\n          selectedOption.value = null\r\n          showAnswer.value = false\r\n        } else {\r\n          // 完成所有问题\r\n          emit('lesson-completed', 'quiz')\r\n        }\r\n      }, 2000)\r\n    }\r\n    \r\n    const gameAction = (action) => {\r\n      switch (action) {\r\n        case 'add':\r\n          gameNumber.value++\r\n          break\r\n        case 'subtract':\r\n          if (gameNumber.value > 0) {\r\n            gameNumber.value--\r\n          }\r\n          break\r\n        case 'reset':\r\n          gameNumber.value = 0\r\n          break\r\n      }\r\n    }\r\n    \r\n    const openFullEducation = () => {\r\n      // 打开完整的教育界面\r\n      console.log('打开完整教育界面')\r\n    }\r\n    \r\n    // 初始化\r\n    onMounted(() => {\r\n      // 可以在这里加载用户的学习进度\r\n      console.log('儿童教育卡片已加载')\r\n    })\r\n    \r\n    return {\r\n      selectedMode,\r\n      isPlaying,\r\n      selectedOption,\r\n      showAnswer,\r\n      gameNumber,\r\n      todayStars,\r\n      currentLesson,\r\n      learningModes,\r\n      currentStory,\r\n      currentQuiz,\r\n      handleCardClick,\r\n      selectLearningMode,\r\n      previousStory,\r\n      nextStory,\r\n      toggleStoryPlay,\r\n      selectQuizOption,\r\n      gameAction,\r\n      openFullEducation\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.kid-education-card {\r\n  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);\r\n}\r\n\r\n.education-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  height: 100%;\r\n}\r\n\r\n/* 当前课程 */\r\n.current-lesson {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n}\r\n\r\n.lesson-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.lesson-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.lesson-info h4 {\r\n  margin: 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 16px;\r\n}\r\n\r\n.lesson-info p {\r\n  margin: 5px 0 0 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n  font-size: 12px;\r\n}\r\n\r\n.lesson-progress {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.progress-bar {\r\n  flex: 1;\r\n  height: 6px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 学习模式 */\r\n.learning-modes {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.mode-btn {\r\n  flex: 1;\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 8px;\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 11px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.mode-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.mode-btn.active {\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  border-color: var(--card-primary-color, #ff6b6b);\r\n}\r\n\r\n.mode-btn i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 互动区域 */\r\n.interaction-area {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 故事模式 */\r\n.story-content h5 {\r\n  margin: 0 0 10px 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 14px;\r\n}\r\n\r\n.story-content p {\r\n  margin: 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.9;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.story-controls {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.story-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  color: white;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.story-btn:hover {\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n}\r\n\r\n.play-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n\r\n/* 问答模式 */\r\n.quiz-question h5 {\r\n  margin: 0 0 15px 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 14px;\r\n}\r\n\r\n.quiz-options {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 8px;\r\n}\r\n\r\n.quiz-option {\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 6px;\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  font-size: 11px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.quiz-option:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.quiz-option.selected {\r\n  background: var(--card-primary-color, #ff6b6b);\r\n}\r\n\r\n.quiz-option.correct {\r\n  background: #2ecc71;\r\n  border-color: #2ecc71;\r\n}\r\n\r\n.quiz-option.wrong {\r\n  background: #e74c3c;\r\n  border-color: #e74c3c;\r\n}\r\n\r\n/* 游戏模式 */\r\n.game-content h5 {\r\n  margin: 0 0 15px 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.number-game {\r\n  text-align: center;\r\n}\r\n\r\n.game-display {\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  color: var(--card-primary-color, #ff6b6b);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.game-controls {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.game-btn {\r\n  padding: 6px 12px;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  border: none;\r\n  border-radius: 6px;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-btn:hover {\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n}\r\n\r\n/* 卡片底部 */\r\n.card-footer-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.achievement-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n}\r\n\r\n.achievement-info i {\r\n  color: #f1c40f;\r\n}\r\n\r\n.expand-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  padding: 6px 12px;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  border: none;\r\n  border-radius: 6px;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.expand-btn:hover {\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n}\r\n</style>\r\n"], "mappings": ";;EAcSA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAc;;EACrBA,KAAK,EAAC;AAAoB;;EAK5BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAMnBA,KAAK,EAAC;AAAe;;EAK1BA,KAAK,EAAC;AAAgB;;;EAatBA,KAAK,EAAC;AAAkB;;;EACUA,KAAK,EAAC;;;EACpCA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAgB;;;EAaYA,KAAK,EAAC;;;EACxCA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAc;;;;EAiBYA,KAAK,EAAC;;;EACxCA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAe;;EAY7BA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkB;;;uBA7GnCC,YAAA,CAuHWC,mBAAA;IAtHT,WAAS,EAAC,cAAc;IACxBC,IAAI,EAAC,OAAO;IACXC,QAAQ,EAAEC,MAAA,CAAAD,QAAQ;IAClBE,KAAK,EAAED,MAAA,CAAAC,KAAK;IACZ,cAAY,EAAED,MAAA,CAAAE,WAAW;IACzBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,uBAAuB;IAC7BC,SAAS,EAAE,IAAI;IACf,aAAW,EAAE,IAAI;IACjBC,OAAK,EAAEC,MAAA,CAAAC,eAAe;IACvBb,KAAK,EAAC;;IAgGKc,MAAM,EAAAC,QAAA,CACf,MASM,CATNC,mBAAA,CASM,OATNC,WASM,GARJD,mBAAA,CAGM,OAHNE,WAGM,G,4BAFJF,mBAAA,CAA2B;MAAxBhB,KAAK,EAAC;IAAa,4BACtBgB,mBAAA,CAAqC,cAA/B,OAAK,GAAAG,gBAAA,CAAGP,MAAA,CAAAQ,UAAU,IAAG,KAAG,gB,GAEhCJ,mBAAA,CAGS;MAHAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAW,iBAAA,IAAAX,MAAA,CAAAW,iBAAA,IAAAD,IAAA,CAAiB;MAAEtB,KAAK,EAAC;oCACvCgB,mBAAA,CAA6B;MAA1BhB,KAAK,EAAC;IAAe,2BACxBgB,mBAAA,CAAe,cAAT,IAAE,mB;sBAtGd,MA4FM,CA5FNA,mBAAA,CA4FM,OA5FNQ,UA4FM,GA3FJC,mBAAA,YAAe,EACfT,mBAAA,CAqBM,OArBNU,UAqBM,GApBJV,mBAAA,CAQM,OARNW,UAQM,GAPJX,mBAAA,CAEM,OAFNY,UAEM,GADJZ,mBAAA,CAAmC;MAA/BhB,KAAK,EAAA6B,eAAA,CAAEjB,MAAA,CAAAkB,aAAa,CAACrB,IAAI;+BAE/BO,mBAAA,CAGM,OAHNe,UAGM,GAFJf,mBAAA,CAAuD,MAAvDgB,UAAuD,EAAAb,gBAAA,CAA3BP,MAAA,CAAAkB,aAAa,CAACtB,KAAK,kBAC/CQ,mBAAA,CAAiE,KAAjEiB,UAAiE,EAAAd,gBAAA,CAAhCP,MAAA,CAAAkB,aAAa,CAACI,WAAW,iB,KAI9DT,mBAAA,UAAa,EACbT,mBAAA,CAQM,OARNmB,UAQM,GAPJnB,mBAAA,CAKM,OALNoB,UAKM,GAJJpB,mBAAA,CAGO;MAFLhB,KAAK,EAAC,eAAe;MACpBqC,KAAK,EAAAC,eAAA;QAAAC,KAAA,KAAc3B,MAAA,CAAAkB,aAAa,CAACU,QAAQ;MAAA;+BAG9CxB,mBAAA,CAAmE,QAAnEyB,WAAmE,EAAAtB,gBAAA,CAApCP,MAAA,CAAAkB,aAAa,CAACU,QAAQ,IAAG,MAAI,gB,KAIhEf,mBAAA,YAAe,EACfT,mBAAA,CAUM,OAVN0B,WAUM,I,kBATJC,mBAAA,CAQSC,SAAA,QAAAC,WAAA,CAPQjC,MAAA,CAAAkC,aAAa,EAArBC,IAAI;2BADbJ,mBAAA,CAQS;QANNK,GAAG,EAAED,IAAI,CAACE,EAAE;QACZtC,OAAK,EAAAuC,MAAA,IAAEtC,MAAA,CAAAuC,kBAAkB,CAACJ,IAAI;QAC9B/C,KAAK,EAAA6B,eAAA;UAAAuB,MAAA,EAAyBxC,MAAA,CAAAyC,YAAY,KAAKN,IAAI,CAACE;QAAE;UAEvDjC,mBAAA,CAA0B;QAAtBhB,KAAK,EAAA6B,eAAA,CAAEkB,IAAI,CAACtC,IAAI;+BACpBO,mBAAA,CAA4B,cAAAG,gBAAA,CAAnB4B,IAAI,CAACO,IAAI,iB;sCAItB7B,mBAAA,UAAa,EACbT,mBAAA,CAoDM,OApDNuC,WAoDM,GAnDO3C,MAAA,CAAAyC,YAAY,gB,cAAvBV,mBAAA,CAgBM,OAhBNa,WAgBM,GAfJxC,mBAAA,CAGM,OAHNyC,WAGM,GAFJzC,mBAAA,CAAiC,YAAAG,gBAAA,CAA1BP,MAAA,CAAA8C,YAAY,CAAClD,KAAK,kBACzBQ,mBAAA,CAAiC,WAAAG,gBAAA,CAA3BP,MAAA,CAAA8C,YAAY,CAACC,OAAO,iB,GAE5B3C,mBAAA,CAUM,OAVN4C,WAUM,GATJ5C,mBAAA,CAES;MAFAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAiD,aAAA,IAAAjD,MAAA,CAAAiD,aAAA,IAAAvC,IAAA,CAAa;MAAEtB,KAAK,EAAC;kCACnCgB,mBAAA,CAAoC;MAAjChB,KAAK,EAAC;IAAsB,0B,IAEjCgB,mBAAA,CAES;MAFAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAkD,eAAA,IAAAlD,MAAA,CAAAkD,eAAA,IAAAxC,IAAA,CAAe;MAAEtB,KAAK,EAAC;QACrCgB,mBAAA,CAA2D;MAAvDhB,KAAK,EAAA6B,eAAA,CAAEjB,MAAA,CAAAmD,SAAS;+BAEtB/C,mBAAA,CAES;MAFAL,OAAK,EAAAU,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEV,MAAA,CAAAoD,SAAA,IAAApD,MAAA,CAAAoD,SAAA,IAAA1C,IAAA,CAAS;MAAEtB,KAAK,EAAC;kCAC/BgB,mBAAA,CAAmC;MAAhChB,KAAK,EAAC;IAAqB,0B,UAKpBY,MAAA,CAAAyC,YAAY,e,cAA5BV,mBAAA,CAkBM,OAlBNsB,WAkBM,GAjBJjD,mBAAA,CAgBM,OAhBNkD,WAgBM,GAfJlD,mBAAA,CAAmC,YAAAG,gBAAA,CAA5BP,MAAA,CAAAuD,WAAW,CAACC,QAAQ,kBAC3BpD,mBAAA,CAaM,OAbNqD,WAaM,I,kBAZJ1B,mBAAA,CAWSC,SAAA,QAAAC,WAAA,CAVmBjC,MAAA,CAAAuD,WAAW,CAACG,OAAO,GAArCC,MAAM,EAAEC,KAAK;2BADvB7B,mBAAA,CAWS;QATNK,GAAG,EAAEwB,KAAK;QACV7D,OAAK,EAAAuC,MAAA,IAAEtC,MAAA,CAAA6D,gBAAgB,CAACD,KAAK;QAC7BxE,KAAK,EAAA6B,eAAA;oBAAkDjB,MAAA,CAAA8D,cAAc,KAAKF,KAAK;mBAA8B5D,MAAA,CAAA+D,UAAU,IAAIH,KAAK,KAAK5D,MAAA,CAAAuD,WAAW,CAACS,OAAO;iBAA4BhE,MAAA,CAAA+D,UAAU,IAAI/D,MAAA,CAAA8D,cAAc,KAAKF,KAAK,IAAIA,KAAK,KAAK5D,MAAA,CAAAuD,WAAW,CAACS;;0BAMlPL,MAAM,gCAAAM,WAAA;4CAMDjE,MAAA,CAAAyC,YAAY,e,cAA5BV,mBAAA,CAYM,OAZNmC,WAYM,GAXJ9D,mBAAA,CAUM,OAVN+D,WAUM,G,0BATJ/D,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAOM,OAPNgE,WAOM,GANJhE,mBAAA,CAAgD,OAAhDiE,WAAgD,EAAA9D,gBAAA,CAAnBP,MAAA,CAAAsE,UAAU,kBACvClE,mBAAA,CAIM,OAJNmE,WAIM,GAHJnE,mBAAA,CAA+D;MAAtDL,OAAK,EAAAU,MAAA,QAAAA,MAAA,MAAA6B,MAAA,IAAEtC,MAAA,CAAAwE,UAAU;MAASpF,KAAK,EAAC;OAAW,IAAE,GACtDgB,mBAAA,CAAoE;MAA3DL,OAAK,EAAAU,MAAA,QAAAA,MAAA,MAAA6B,MAAA,IAAEtC,MAAA,CAAAwE,UAAU;MAAcpF,KAAK,EAAC;OAAW,IAAE,GAC3DgB,mBAAA,CAAiE;MAAxDL,OAAK,EAAAU,MAAA,QAAAA,MAAA,MAAA6B,MAAA,IAAEtC,MAAA,CAAAwE,UAAU;MAAWpF,KAAK,EAAC;OAAW,IAAE,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}