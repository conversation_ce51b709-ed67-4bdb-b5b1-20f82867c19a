{"ast": null, "code": "import { createApp } from 'vue';\nimport { createPinia } from 'pinia';\nimport App from './App.vue';\nconst app = createApp(App);\nconst pinia = createPinia();\napp.use(pinia);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "createPinia", "App", "app", "pinia", "use", "mount"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport { createPinia } from 'pinia'\r\nimport App from './App.vue'\r\n\r\nconst app = createApp(App)\r\nconst pinia = createPinia()\r\n\r\napp.use(pinia)\r\napp.mount('#app')\r\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,SAASC,WAAW,QAAQ,OAAO;AACnC,OAAOC,GAAG,MAAM,WAAW;AAE3B,MAAMC,GAAG,GAAGH,SAAS,CAACE,GAAG,CAAC;AAC1B,MAAME,KAAK,GAAGH,WAAW,CAAC,CAAC;AAE3BE,GAAG,CAACE,GAAG,CAACD,KAAK,CAAC;AACdD,GAAG,CAACG,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}