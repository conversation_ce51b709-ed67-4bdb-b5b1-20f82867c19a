{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"avatar-display\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"avatar-animation\"\n};\nconst _hoisted_3 = [\"src\", \"alt\"];\nconst _hoisted_4 = {\n  key: 1,\n  class: \"avatar-fallback\"\n};\nconst _hoisted_5 = {\n  key: 2,\n  class: \"voice-wave\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"interaction-hint\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"quick-actions\"\n};\nconst _hoisted_8 = [\"onClick\", \"title\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"vpa-avatar-widget\", [`size-${$props.size}`, `mode-${$setup.currentMode}`]])\n  }, [_createCommentVNode(\" VPA头像容器 \"), _createElementVNode(\"div\", {\n    class: \"avatar-container\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $setup.handleAvatarClick && $setup.handleAvatarClick(...args))\n  }, [_createCommentVNode(\" 头像显示区域 \"), _createElementVNode(\"div\", _hoisted_1, [$setup.currentAnimationResource ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createCommentVNode(\" 显示GIF动画 \"), _createElementVNode(\"img\", {\n    src: $setup.currentAnimationResource,\n    alt: $setup.currentAnimationState,\n    class: \"avatar-gif\",\n    onError: _cache[0] || (_cache[0] = (...args) => _ctx.handleImageError && _ctx.handleImageError(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_3)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _cache[2] || (_cache[2] = [_createElementVNode(\"i\", {\n    class: \"fas fa-robot avatar-icon\"\n  }, null, -1 /* CACHED */)]))), _createCommentVNode(\" 状态指示器 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"status-indicator\", $setup.currentAnimationState])\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n    class: \"status-dot\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createCommentVNode(\" 语音波形动画 \"), $setup.showVoiceWave ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(4, i => {\n    return _createElementVNode(\"div\", {\n      class: \"wave\",\n      key: i,\n      style: _normalizeStyle({\n        animationDelay: `${i * 0.1}s`\n      })\n    }, null, 4 /* STYLE */);\n  }), 64 /* STABLE_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 交互提示 \"), $setup.showHint ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"span\", null, _toDisplayString($setup.currentHint), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 快速操作按钮 \"), $props.size !== 'small' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.quickActions, action => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: action.id,\n      onClick: $event => $setup.handleQuickAction(action),\n      class: \"action-btn\",\n      title: action.label\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass(action.icon)\n    }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_8);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "size", "$setup", "currentMode", "_createCommentVNode", "_createElementVNode", "onClick", "_cache", "args", "handleAvatarClick", "_hoisted_1", "currentAnimationResource", "_hoisted_2", "src", "alt", "currentAnimationState", "onError", "_ctx", "handleImageError", "_hoisted_4", "showVoiceWave", "_hoisted_5", "_Fragment", "_renderList", "i", "key", "style", "_normalizeStyle", "animationDelay", "showHint", "_hoisted_6", "_toDisplayString", "currentHint", "_hoisted_7", "quickActions", "action", "id", "$event", "handleQuickAction", "title", "label", "icon"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue"], "sourcesContent": ["<template>\r\n  <div class=\"vpa-avatar-widget\" :class=\"[`size-${size}`, `mode-${currentMode}`]\">\r\n    <!-- VPA头像容器 -->\r\n    <div class=\"avatar-container\" @click=\"handleAvatarClick\">\r\n      <!-- 头像显示区域 -->\r\n      <div class=\"avatar-display\">\r\n        <div v-if=\"currentAnimationResource\" class=\"avatar-animation\">\r\n          <!-- 显示GIF动画 -->\r\n          <img\r\n            :src=\"currentAnimationResource\"\r\n            :alt=\"currentAnimationState\"\r\n            class=\"avatar-gif\"\r\n            @error=\"handleImageError\"\r\n          />\r\n        </div>\r\n\r\n        <div v-else class=\"avatar-fallback\">\r\n          <i class=\"fas fa-robot avatar-icon\"></i>\r\n        </div>\r\n        \r\n        <!-- 状态指示器 -->\r\n        <div class=\"status-indicator\" :class=\"currentAnimationState\">\r\n          <div class=\"status-dot\"></div>\r\n        </div>\r\n        \r\n        <!-- 语音波形动画 -->\r\n        <div v-if=\"showVoiceWave\" class=\"voice-wave\">\r\n          <div class=\"wave\" v-for=\"i in 4\" :key=\"i\" :style=\"{ animationDelay: `${i * 0.1}s` }\"></div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 交互提示 -->\r\n      <div v-if=\"showHint\" class=\"interaction-hint\">\r\n        <span>{{ currentHint }}</span>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 快速操作按钮 -->\r\n    <div v-if=\"size !== 'small'\" class=\"quick-actions\">\r\n      <button \r\n        v-for=\"action in quickActions\" \r\n        :key=\"action.id\"\r\n        @click=\"handleQuickAction(action)\"\r\n        class=\"action-btn\"\r\n        :title=\"action.label\"\r\n      >\r\n        <i :class=\"action.icon\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport { useVpaStore } from '@/store/modules/vpa'\r\n\r\nexport default {\r\n  name: 'VPAAvatarWidget',\r\n  props: {\r\n    size: {\r\n      type: String,\r\n      default: 'medium',\r\n      validator: value => ['small', 'medium', 'large'].includes(value)\r\n    },\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 1 })\r\n    },\r\n    theme: {\r\n      type: String,\r\n      default: 'glass'\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({\r\n        primary: '#667eea',\r\n        secondary: '#764ba2',\r\n        background: 'rgba(102, 126, 234, 0.1)',\r\n        text: '#ffffff'\r\n      })\r\n    },\r\n    showQuickActions: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  emits: ['avatar-click', 'mode-changed', 'animation-changed', 'quick-action'],\r\n  \r\n  setup(props, { emit }) {\r\n    const vpaStore = useVpaStore()\r\n    \r\n    // 响应式状态\r\n    const showHint = ref(false)\r\n    const showVoiceWave = ref(false)\r\n    const hintTimer = ref(null)\r\n    \r\n    // 计算属性\r\n    const currentMode = computed(() => vpaStore.currentMode)\r\n    const currentAnimationState = computed(() => vpaStore.currentAnimationState)\r\n    const currentAnimationConfig = computed(() => vpaStore.currentAnimationConfig)\r\n    \r\n    const currentAnimationResource = computed(() => {\r\n      const animationConfig = vpaStore.currentAnimationConfig\r\n      if (animationConfig && animationConfig.resource) {\r\n        try {\r\n          // 直接返回资源路径，让img标签处理\r\n          return `/src/assets/${animationConfig.resource}`\r\n        } catch (error) {\r\n          console.warn('动画资源加载失败:', error)\r\n          return null\r\n        }\r\n      }\r\n      return null\r\n    })\r\n    \r\n    const currentHint = computed(() => {\r\n      const hints = {\r\n        companion: '我在这里陪伴您',\r\n        interaction: '点击与我交流',\r\n        hidden: ''\r\n      }\r\n      return hints[currentMode.value] || '点击与我互动'\r\n    })\r\n    \r\n    const quickActions = computed(() => {\r\n      if (!props.showQuickActions) return []\r\n      \r\n      return [\r\n        {\r\n          id: 'voice',\r\n          label: '语音交互',\r\n          icon: 'fas fa-microphone'\r\n        },\r\n        {\r\n          id: 'settings',\r\n          label: '设置',\r\n          icon: 'fas fa-cog'\r\n        },\r\n        {\r\n          id: 'help',\r\n          label: '帮助',\r\n          icon: 'fas fa-question-circle'\r\n        }\r\n      ]\r\n    })\r\n    \r\n    // 方法\r\n    const handleAvatarClick = () => {\r\n      const clickData = {\r\n        mode: currentMode.value,\r\n        animationState: currentAnimationState.value,\r\n        timestamp: Date.now()\r\n      }\r\n      \r\n      emit('avatar-click', clickData)\r\n      \r\n      // 触发交互动画\r\n      if (currentAnimationState.value === 'idle') {\r\n        vpaStore.setAnimation('greeting')\r\n        setTimeout(() => {\r\n          vpaStore.setAnimation('idle')\r\n        }, 3000)\r\n      }\r\n      \r\n      // 显示语音波形\r\n      showVoiceWave.value = true\r\n      setTimeout(() => {\r\n        showVoiceWave.value = false\r\n      }, 2000)\r\n    }\r\n    \r\n    const handleQuickAction = (action) => {\r\n      emit('quick-action', action)\r\n      \r\n      // 根据操作类型触发相应动画\r\n      switch (action.id) {\r\n        case 'voice':\r\n          vpaStore.setAnimation('listening')\r\n          showVoiceWave.value = true\r\n          break\r\n        case 'settings':\r\n          vpaStore.setAnimation('thinking')\r\n          break\r\n        case 'help':\r\n          vpaStore.setAnimation('talking')\r\n          break\r\n      }\r\n    }\r\n    \r\n    const showInteractionHint = () => {\r\n      showHint.value = true\r\n      hintTimer.value = setTimeout(() => {\r\n        showHint.value = false\r\n      }, 3000)\r\n    }\r\n    \r\n    const hideInteractionHint = () => {\r\n      showHint.value = false\r\n      if (hintTimer.value) {\r\n        clearTimeout(hintTimer.value)\r\n        hintTimer.value = null\r\n      }\r\n    }\r\n    \r\n    // 生命周期\r\n    onMounted(() => {\r\n      console.log('VPA头像组件已加载')\r\n      \r\n      // 定期显示交互提示\r\n      if (props.size !== 'small') {\r\n        setInterval(() => {\r\n          if (currentMode.value === 'companion' && !showHint.value) {\r\n            showInteractionHint()\r\n          }\r\n        }, 30000) // 30秒显示一次提示\r\n      }\r\n    })\r\n    \r\n    onUnmounted(() => {\r\n      hideInteractionHint()\r\n    })\r\n    \r\n    return {\r\n      currentMode,\r\n      currentAnimationState,\r\n      currentAnimationConfig,\r\n      currentAnimationResource,\r\n      currentHint,\r\n      quickActions,\r\n      showHint,\r\n      showVoiceWave,\r\n      handleAvatarClick,\r\n      handleQuickAction,\r\n      showInteractionHint,\r\n      hideInteractionHint\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.vpa-avatar-widget {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 10px;\r\n  padding: 10px;\r\n  border-radius: 15px;\r\n  background: var(--card-background, rgba(255, 255, 255, 0.1));\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.vpa-avatar-widget:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 尺寸变体 */\r\n.size-small {\r\n  width: 80px;\r\n  height: 80px;\r\n  padding: 8px;\r\n}\r\n\r\n.size-medium {\r\n  width: 120px;\r\n  height: 140px;\r\n  padding: 10px;\r\n}\r\n\r\n.size-large {\r\n  width: 160px;\r\n  height: 200px;\r\n  padding: 15px;\r\n}\r\n\r\n/* 头像容器 */\r\n.avatar-container {\r\n  position: relative;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.avatar-container:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.avatar-display {\r\n  position: relative;\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, var(--card-primary-color, #667eea) 0%, var(--card-secondary-color, #764ba2) 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.size-small .avatar-display {\r\n  width: 50px;\r\n  height: 50px;\r\n}\r\n\r\n.size-large .avatar-display {\r\n  width: 80px;\r\n  height: 80px;\r\n}\r\n\r\n.avatar-icon {\r\n  font-size: 40px;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.size-small .avatar-icon {\r\n  font-size: 30px;\r\n}\r\n\r\n.size-large .avatar-icon {\r\n  font-size: 50px;\r\n}\r\n\r\n/* 动画状态 */\r\n.animation-placeholder.idle .avatar-icon {\r\n  animation: breathe 3s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.talking .avatar-icon {\r\n  animation: talking 0.5s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.listening .avatar-icon {\r\n  animation: pulse 1s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.thinking .avatar-icon {\r\n  animation: thinking 2s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.greeting .avatar-icon {\r\n  animation: bounce 0.6s ease-in-out;\r\n}\r\n\r\n/* 状态指示器 */\r\n.status-indicator {\r\n  position: absolute;\r\n  bottom: 5px;\r\n  right: 5px;\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  border: 2px solid white;\r\n}\r\n\r\n.status-dot {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  background: #4CAF50;\r\n  animation: statusPulse 2s ease-in-out infinite;\r\n}\r\n\r\n.status-indicator.talking .status-dot {\r\n  background: #2196F3;\r\n  animation: statusBlink 0.5s ease-in-out infinite;\r\n}\r\n\r\n.status-indicator.listening .status-dot {\r\n  background: #FF9800;\r\n  animation: statusPulse 1s ease-in-out infinite;\r\n}\r\n\r\n.status-indicator.thinking .status-dot {\r\n  background: #9C27B0;\r\n  animation: statusRotate 2s linear infinite;\r\n}\r\n\r\n/* 语音波形 */\r\n.voice-wave {\r\n  position: absolute;\r\n  bottom: -10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 2px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.wave {\r\n  width: 3px;\r\n  height: 8px;\r\n  background: var(--card-primary-color, #667eea);\r\n  border-radius: 2px;\r\n  animation: wave 1s infinite ease-in-out;\r\n}\r\n\r\n/* 交互提示 */\r\n.interaction-hint {\r\n  position: absolute;\r\n  top: -35px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 5px 10px;\r\n  border-radius: 15px;\r\n  font-size: 11px;\r\n  white-space: nowrap;\r\n  opacity: 0;\r\n  animation: hintFadeIn 0.3s ease forwards;\r\n}\r\n\r\n.interaction-hint::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  border: 5px solid transparent;\r\n  border-top-color: rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n/* 快速操作按钮 */\r\n.quick-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.action-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: var(--card-primary-color, #667eea);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 动画定义 */\r\n@keyframes breathe {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n@keyframes talking {\r\n  0%, 100% { transform: scaleY(1); }\r\n  50% { transform: scaleY(1.1); }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%, 100% { transform: rotate(0deg); }\r\n  50% { transform: rotate(5deg); }\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-5px); }\r\n}\r\n\r\n@keyframes statusPulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes statusBlink {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0; }\r\n}\r\n\r\n@keyframes statusRotate {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes hintFadeIn {\r\n  0% { opacity: 0; transform: translateX(-50%) translateY(-5px); }\r\n  100% { opacity: 1; transform: translateX(-50%) translateY(0); }\r\n}\r\n\r\n@keyframes wave {\r\n  0%, 100% { height: 8px; }\r\n  50% { height: 16px; }\r\n}\r\n</style>\r\n"], "mappings": ";;EAKWA,KAAK,EAAC;AAAgB;;;EACYA,KAAK,EAAC;;;;;EAU/BA,KAAK,EAAC;;;;EAUQA,KAAK,EAAC;;;;EAMbA,KAAK,EAAC;;;;EAMAA,KAAK,EAAC;;;;uBArCrCC,mBAAA,CAgDM;IAhDDD,KAAK,EAAAE,eAAA,EAAC,mBAAmB,WAAkBC,MAAA,CAAAC,IAAI,YAAYC,MAAA,CAAAC,WAAW;MACzEC,mBAAA,aAAgB,EAChBC,mBAAA,CAgCM;IAhCDR,KAAK,EAAC,kBAAkB;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAAO,iBAAA,IAAAP,MAAA,CAAAO,iBAAA,IAAAD,IAAA,CAAiB;MACrDJ,mBAAA,YAAe,EACfC,mBAAA,CAwBM,OAxBNK,UAwBM,GAvBOR,MAAA,CAAAS,wBAAwB,I,cAAnCb,mBAAA,CAQM,OARNc,UAQM,GAPJR,mBAAA,aAAgB,EAChBC,mBAAA,CAKE;IAJCQ,GAAG,EAAEX,MAAA,CAAAS,wBAAwB;IAC7BG,GAAG,EAAEZ,MAAA,CAAAa,qBAAqB;IAC3BlB,KAAK,EAAC,YAAY;IACjBmB,OAAK,EAAAT,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAES,IAAA,CAAAC,gBAAA,IAAAD,IAAA,CAAAC,gBAAA,IAAAV,IAAA,CAAgB;2EAI5BV,mBAAA,CAEM,OAFNqB,UAEM,EAAAZ,MAAA,QAAAA,MAAA,OADJF,mBAAA,CAAwC;IAArCR,KAAK,EAAC;EAA0B,0B,KAGrCO,mBAAA,WAAc,EACdC,mBAAA,CAEM;IAFDR,KAAK,EAAAE,eAAA,EAAC,kBAAkB,EAASG,MAAA,CAAAa,qBAAqB;gCACzDV,mBAAA,CAA8B;IAAzBR,KAAK,EAAC;EAAY,0B,mBAGzBO,mBAAA,YAAe,EACJF,MAAA,CAAAkB,aAAa,I,cAAxBtB,mBAAA,CAEM,OAFNuB,UAEM,I,cADJvB,mBAAA,CAA2FwB,SAAA,QAAAC,WAAA,CAA7D,CAAC,EAANC,CAAC;WAA1BnB,mBAAA,CAA2F;MAAtFR,KAAK,EAAC,MAAM;MAAiB4B,GAAG,EAAED,CAAC;MAAGE,KAAK,EAAAC,eAAA;QAAAC,cAAA,KAAuBJ,CAAC;MAAA;;2EAI5EpB,mBAAA,UAAa,EACFF,MAAA,CAAA2B,QAAQ,I,cAAnB/B,mBAAA,CAEM,OAFNgC,UAEM,GADJzB,mBAAA,CAA8B,cAAA0B,gBAAA,CAArB7B,MAAA,CAAA8B,WAAW,iB,0CAIxB5B,mBAAA,YAAe,EACJJ,MAAA,CAAAC,IAAI,gB,cAAfH,mBAAA,CAUM,OAVNmC,UAUM,I,kBATJnC,mBAAA,CAQSwB,SAAA,QAAAC,WAAA,CAPUrB,MAAA,CAAAgC,YAAY,EAAtBC,MAAM;yBADfrC,mBAAA,CAQS;MANN2B,GAAG,EAAEU,MAAM,CAACC,EAAE;MACd9B,OAAK,EAAA+B,MAAA,IAAEnC,MAAA,CAAAoC,iBAAiB,CAACH,MAAM;MAChCtC,KAAK,EAAC,YAAY;MACjB0C,KAAK,EAAEJ,MAAM,CAACK;QAEfnC,mBAAA,CAA4B;MAAxBR,KAAK,EAAAE,eAAA,CAAEoC,MAAM,CAACM,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}