{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderSlot as _renderSlot, withModifiers as _withModifiers, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  key: 0,\n  class: \"card-header\"\n};\nconst _hoisted_2 = {\n  class: \"header-left\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"card-title\"\n};\nconst _hoisted_4 = {\n  class: \"header-right\"\n};\nconst _hoisted_5 = {\n  class: \"default-content\"\n};\nconst _hoisted_6 = {\n  key: 0\n};\nconst _hoisted_7 = {\n  key: 1,\n  class: \"card-footer\"\n};\nconst _hoisted_8 = {\n  class: \"footer-actions\"\n};\nconst _hoisted_9 = {\n  key: 2,\n  class: \"loading-overlay\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass(['base-card', `card-${$props.cardType}`, `size-${$props.size}`, $setup.themeClass]),\n    style: _normalizeStyle($setup.cardStyles),\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.handleClick && $setup.handleClick(...args))\n  }, [_createCommentVNode(\" 卡片头部 \"), $props.showHeader ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$props.icon ? (_openBlock(), _createElementBlock(\"i\", {\n    key: 0,\n    class: _normalizeClass([$props.icon, \"card-icon\"])\n  }, null, 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true), $props.title ? (_openBlock(), _createElementBlock(\"h3\", _hoisted_3, _toDisplayString($props.title), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_4, [_renderSlot(_ctx.$slots, \"header-actions\", {}, () => [$props.closable ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[0] || (_cache[0] = _withModifiers($event => _ctx.$emit('close'), [\"stop\"])),\n    class: \"close-btn\"\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)], true)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 卡片内容 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"card-content\", {\n      'no-header': !$props.showHeader\n    }])\n  }, [_renderSlot(_ctx.$slots, \"default\", {}, () => [_createElementVNode(\"div\", _hoisted_5, [$props.description ? (_openBlock(), _createElementBlock(\"p\", _hoisted_6, _toDisplayString($props.description), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])], true)], 2 /* CLASS */), _createCommentVNode(\" 卡片底部 \"), $props.showFooter ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_renderSlot(_ctx.$slots, \"footer\", {}, () => [_createElementVNode(\"div\", _hoisted_8, [$props.actionable ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[1] || (_cache[1] = _withModifiers((...args) => $setup.handleAction && $setup.handleAction(...args), [\"stop\"])),\n    class: \"action-btn\"\n  }, _toDisplayString($props.actionText || '操作'), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])], true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $props.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n    class: \"loading-spinner\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin\"\n  })], -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)], 6 /* CLASS, STYLE */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "cardType", "size", "$setup", "themeClass", "style", "_normalizeStyle", "cardStyles", "onClick", "_cache", "args", "handleClick", "_createCommentVNode", "showHeader", "_hoisted_1", "_createElementVNode", "_hoisted_2", "icon", "title", "_hoisted_3", "_toDisplayString", "_hoisted_4", "_renderSlot", "_ctx", "$slots", "closable", "_withModifiers", "$event", "$emit", "_hoisted_5", "description", "_hoisted_6", "showFooter", "_hoisted_7", "_hoisted_8", "actionable", "handleAction", "actionText", "loading", "_hoisted_9"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\BaseCard.vue"], "sourcesContent": ["<template>\r\n  <div \r\n    :class=\"['base-card', `card-${cardType}`, `size-${size}`, themeClass]\"\r\n    :style=\"cardStyles\"\r\n    @click=\"handleClick\"\r\n  >\r\n    <!-- 卡片头部 -->\r\n    <div v-if=\"showHeader\" class=\"card-header\">\r\n      <div class=\"header-left\">\r\n        <i v-if=\"icon\" :class=\"icon\" class=\"card-icon\"></i>\r\n        <h3 v-if=\"title\" class=\"card-title\">{{ title }}</h3>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <slot name=\"header-actions\">\r\n          <button v-if=\"closable\" @click.stop=\"$emit('close')\" class=\"close-btn\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </slot>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 卡片内容 -->\r\n    <div class=\"card-content\" :class=\"{ 'no-header': !showHeader }\">\r\n      <slot>\r\n        <div class=\"default-content\">\r\n          <p v-if=\"description\">{{ description }}</p>\r\n        </div>\r\n      </slot>\r\n    </div>\r\n\r\n    <!-- 卡片底部 -->\r\n    <div v-if=\"showFooter\" class=\"card-footer\">\r\n      <slot name=\"footer\">\r\n        <div class=\"footer-actions\">\r\n          <button v-if=\"actionable\" @click.stop=\"handleAction\" class=\"action-btn\">\r\n            {{ actionText || '操作' }}\r\n          </button>\r\n        </div>\r\n      </slot>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading-overlay\">\r\n      <div class=\"loading-spinner\">\r\n        <i class=\"fas fa-spinner fa-spin\"></i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { computed } from 'vue'\r\nimport { useLayoutStore } from '@/store'\r\n\r\nexport default {\r\n  name: 'BaseCard',\r\n  props: {\r\n    // 卡片类型\r\n    cardType: {\r\n      type: String,\r\n      default: 'default'\r\n    },\r\n    \r\n    // 卡片尺寸\r\n    size: {\r\n      type: String,\r\n      default: 'medium',\r\n      validator: (value) => ['small', 'medium', 'large', 'custom'].includes(value)\r\n    },\r\n    \r\n    // 自定义尺寸（当size为custom时使用）\r\n    customSize: {\r\n      type: Object,\r\n      default: () => ({ width: 4, height: 2 })\r\n    },\r\n    \r\n    // 网格位置\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 1 })\r\n    },\r\n    \r\n    // 卡片标题\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    // 卡片描述\r\n    description: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    // 卡片图标\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    // 主题\r\n    theme: {\r\n      type: String,\r\n      default: 'glass',\r\n      validator: (value) => ['glass', 'solid', 'minimal', 'gradient'].includes(value)\r\n    },\r\n    \r\n    // 主题颜色\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    \r\n    // 显示配置\r\n    showHeader: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    \r\n    showFooter: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    // 交互配置\r\n    clickable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    actionable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    actionText: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    closable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    // 状态\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  \r\n  emits: ['click', 'action', 'close'],\r\n  \r\n  setup(props, { emit }) {\r\n    const layoutStore = useLayoutStore()\r\n    \r\n    // 计算卡片样式\r\n    const cardStyles = computed(() => {\r\n      let gridStyle = {}\r\n      \r\n      // 根据尺寸类型获取网格样式\r\n      if (props.size === 'custom') {\r\n        gridStyle = layoutStore.getComponentPosition('custom', props.position)\r\n        // 注册自定义尺寸\r\n        layoutStore.registerComponentSize('custom', props.customSize)\r\n      } else {\r\n        const sizeMap = {\r\n          small: 'cardSmall',\r\n          medium: 'cardMedium', \r\n          large: 'cardLarge'\r\n        }\r\n        gridStyle = layoutStore.getComponentPosition(sizeMap[props.size], props.position)\r\n      }\r\n      \r\n      // 主题颜色样式\r\n      const themeStyle = {\r\n        '--card-primary-color': props.themeColors.primary || '#4a90e2',\r\n        '--card-secondary-color': props.themeColors.secondary || '#7ed321',\r\n        '--card-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',\r\n        '--card-text-color': props.themeColors.text || '#ffffff'\r\n      }\r\n      \r\n      return {\r\n        ...gridStyle,\r\n        ...themeStyle\r\n      }\r\n    })\r\n    \r\n    // 主题类名\r\n    const themeClass = computed(() => {\r\n      return `theme-${props.theme}`\r\n    })\r\n    \r\n    // 处理点击事件\r\n    const handleClick = () => {\r\n      if (props.clickable && !props.disabled) {\r\n        emit('click')\r\n      }\r\n    }\r\n    \r\n    // 处理操作事件\r\n    const handleAction = () => {\r\n      if (props.actionable && !props.disabled) {\r\n        emit('action')\r\n      }\r\n    }\r\n    \r\n    return {\r\n      cardStyles,\r\n      themeClass,\r\n      handleClick,\r\n      handleAction\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.base-card {\r\n  position: relative;\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  cursor: default;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 主题样式 */\r\n.theme-glass {\r\n  background: var(--card-background, rgba(255, 255, 255, 0.1));\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.theme-solid {\r\n  background: var(--card-primary-color, #4a90e2);\r\n  border: none;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.theme-minimal {\r\n  background: transparent;\r\n  border: 1px solid var(--card-primary-color, #4a90e2);\r\n  box-shadow: none;\r\n}\r\n\r\n.theme-gradient {\r\n  background: linear-gradient(135deg, \r\n    var(--card-primary-color, #4a90e2) 0%, \r\n    var(--card-secondary-color, #7ed321) 100%);\r\n  border: none;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 可点击状态 */\r\n.base-card.clickable {\r\n  cursor: pointer;\r\n}\r\n\r\n.base-card.clickable:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 禁用状态 */\r\n.base-card.disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 卡片头部 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.card-icon {\r\n  font-size: 18px;\r\n  color: var(--card-primary-color, #4a90e2);\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: var(--card-text-color, #ffffff);\r\n}\r\n\r\n.close-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: var(--card-text-color, #ffffff);\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 卡片内容 */\r\n.card-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  color: var(--card-text-color, #ffffff);\r\n  overflow: hidden;\r\n}\r\n\r\n.card-content.no-header {\r\n  padding-top: 20px;\r\n}\r\n\r\n.default-content p {\r\n  margin: 0;\r\n  opacity: 0.8;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 卡片底部 */\r\n.card-footer {\r\n  padding: 15px 20px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.footer-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n.action-btn {\r\n  padding: 8px 16px;\r\n  border: 1px solid var(--card-primary-color, #4a90e2);\r\n  background: var(--card-primary-color, #4a90e2);\r\n  color: white;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: var(--card-secondary-color, #7ed321);\r\n  border-color: var(--card-secondary-color, #7ed321);\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.loading-spinner {\r\n  color: var(--card-primary-color, #4a90e2);\r\n  font-size: 24px;\r\n}\r\n</style>\r\n"], "mappings": ";;;EAO2BA,KAAK,EAAC;;;EACtBA,KAAK,EAAC;AAAa;;;EAELA,KAAK,EAAC;;;EAEpBA,KAAK,EAAC;AAAc;;EAYlBA,KAAK,EAAC;AAAiB;;;;;;EAOTA,KAAK,EAAC;;;EAEpBA,KAAK,EAAC;AAAgB;;;EASXA,KAAK,EAAC;;;uBAzC5BC,mBAAA,CA8CM;IA7CHD,KAAK,EAAAE,eAAA,uBAAwBC,MAAA,CAAAC,QAAQ,YAAYD,MAAA,CAAAE,IAAI,IAAIC,MAAA,CAAAC,UAAU;IACnEC,KAAK,EAAAC,eAAA,CAAEH,MAAA,CAAAI,UAAU;IACjBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEP,MAAA,CAAAQ,WAAA,IAAAR,MAAA,CAAAQ,WAAA,IAAAD,IAAA,CAAW;MAEnBE,mBAAA,UAAa,EACFZ,MAAA,CAAAa,UAAU,I,cAArBf,mBAAA,CAYM,OAZNgB,UAYM,GAXJC,mBAAA,CAGM,OAHNC,UAGM,GAFKhB,MAAA,CAAAiB,IAAI,I,cAAbnB,mBAAA,CAAmD;;IAAnCD,KAAK,EAAAE,eAAA,EAAEC,MAAA,CAAAiB,IAAI,EAAQ,WAAW;gEACpCjB,MAAA,CAAAkB,KAAK,I,cAAfpB,mBAAA,CAAoD,MAApDqB,UAAoD,EAAAC,gBAAA,CAAbpB,MAAA,CAAAkB,KAAK,oB,qCAE9CH,mBAAA,CAMM,OANNM,UAMM,GALJC,WAAA,CAIOC,IAAA,CAAAC,MAAA,wBAJP,MAIO,CAHSxB,MAAA,CAAAyB,QAAQ,I,cAAtB3B,mBAAA,CAES;;IAFgBU,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAiB,cAAA,CAAAC,MAAA,IAAOJ,IAAA,CAAAK,KAAK;IAAW/B,KAAK,EAAC;gCACzDkB,mBAAA,CAA4B;IAAzBlB,KAAK,EAAC;EAAc,0B,0FAM/Be,mBAAA,UAAa,EACbG,mBAAA,CAMM;IANDlB,KAAK,EAAAE,eAAA,EAAC,cAAc;MAAA,cAAyBC,MAAA,CAAAa;IAAU;MAC1DS,WAAA,CAIOC,IAAA,CAAAC,MAAA,iBAJP,MAIO,CAHLT,mBAAA,CAEM,OAFNc,UAEM,GADK7B,MAAA,CAAA8B,WAAW,I,cAApBhC,mBAAA,CAA2C,KAAAiC,UAAA,EAAAX,gBAAA,CAAlBpB,MAAA,CAAA8B,WAAW,oB,8DAK1ClB,mBAAA,UAAa,EACFZ,MAAA,CAAAgC,UAAU,I,cAArBlC,mBAAA,CAQM,OARNmC,UAQM,GAPJX,WAAA,CAMOC,IAAA,CAAAC,MAAA,gBANP,MAMO,CALLT,mBAAA,CAIM,OAJNmB,UAIM,GAHUlC,MAAA,CAAAmC,UAAU,I,cAAxBrC,mBAAA,CAES;;IAFkBU,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAiB,cAAA,KAAAhB,IAAA,KAAOP,MAAA,CAAAiC,YAAA,IAAAjC,MAAA,CAAAiC,YAAA,IAAA1B,IAAA,CAAY;IAAEb,KAAK,EAAC;sBACtDG,MAAA,CAAAqC,UAAU,4B,oFAMrBzB,mBAAA,UAAa,EACFZ,MAAA,CAAAsC,OAAO,I,cAAlBxC,mBAAA,CAIM,OAJNyC,UAIM,EAAA9B,MAAA,QAAAA,MAAA,OAHJM,mBAAA,CAEM;IAFDlB,KAAK,EAAC;EAAiB,IAC1BkB,mBAAA,CAAsC;IAAnClB,KAAK,EAAC;EAAwB,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}