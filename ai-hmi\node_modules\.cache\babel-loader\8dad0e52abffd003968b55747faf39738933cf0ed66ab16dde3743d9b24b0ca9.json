{"ast": null, "code": "import { computed } from 'vue';\nexport default {\n  name: 'GlassCard',\n  props: {\n    title: {\n      type: String,\n      required: true\n    },\n    icon: {\n      type: String,\n      default: 'fas fa-cube'\n    },\n    style: {\n      type: Object,\n      default: () => ({})\n    },\n    themeColors: {\n      type: Object,\n      default: null\n    }\n  },\n  setup(props) {\n    const cardStyles = computed(() => {\n      // 使用主题颜色或默认值\n      const colors = props.themeColors || {\n        glassBackground: 'rgba(255, 255, 255, 0.15)',\n        glassBorder: 'rgba(255, 255, 255, 0.2)',\n        text: '#ffffff',\n        cardTitleColor: '#ffffff',\n        cardContentColor: '#ecf0f1',\n        buttonBackground: 'rgba(74, 144, 226, 0.8)',\n        buttonColor: '#FFFFFF',\n        buttonBorder: 'rgba(255, 255, 255, 0.6)',\n        buttonHoverBackground: 'rgba(104, 174, 256, 0.9)',\n        buttonTextShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',\n        backgroundBrightness: 128\n      };\n\n      // 检查是否有AI增强的智能样式\n      const useIntelligentStyles = colors.isAIEnhanced && colors.intelligentCardStyles;\n\n      // 根据背景亮度调整阴影\n      const shadowIntensity = colors.backgroundBrightness > 150 ? 0.2 : 0.1;\n      const textShadowColor = colors.backgroundBrightness > 150 ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)';\n\n      // 如果有AI智能样式，优先使用\n      if (useIntelligentStyles) {\n        const intelligentStyles = colors.intelligentCardStyles;\n        const intelligentTextColors = colors.intelligentTextColors || {};\n        const intelligentButtonStyles = colors.intelligentButtonStyles || {};\n        return {\n          // AI智能样式\n          borderRadius: intelligentStyles.borderRadius || props.style.borderRadius || '16px',\n          backdropFilter: intelligentStyles.backdropFilter || props.style.backdropFilter || 'blur(12px)',\n          background: intelligentStyles.background || props.style.background || colors.glassBackground,\n          border: intelligentStyles.border || props.style.border || `1px solid ${colors.glassBorder}`,\n          boxShadow: intelligentStyles.boxShadow || props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,\n          color: intelligentTextColors.content || colors.cardContentColor || colors.text,\n          // AI增强的CSS变量\n          '--button-bg': intelligentButtonStyles.background || colors.buttonBackground,\n          '--button-color': intelligentButtonStyles.color || colors.buttonColor,\n          '--button-border': intelligentButtonStyles.border || colors.buttonBorder,\n          '--button-hover-bg': intelligentButtonStyles.hoverBackground || colors.buttonHoverBackground,\n          '--button-text-shadow': intelligentButtonStyles.textShadow || colors.buttonTextShadow,\n          '--card-title-color': intelligentTextColors.title || colors.cardTitleColor || colors.text,\n          '--card-content-color': intelligentTextColors.content || colors.cardContentColor || colors.text,\n          '--text-shadow': intelligentTextColors.textShadow || `0 1px 2px ${textShadowColor}`,\n          // AI分析信息\n          '--ai-mood': colors.aiAnalysis?.mood || 'default',\n          '--ai-brightness': colors.aiAnalysis?.brightness || 'medium'\n        };\n      }\n\n      // 传统样式（降级方案）\n      return {\n        borderRadius: props.style.borderRadius || '16px',\n        backdropFilter: props.style.backdropFilter || 'blur(12px)',\n        background: props.style.background || colors.glassBackground,\n        border: props.style.border || `1px solid ${colors.glassBorder}`,\n        boxShadow: props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,\n        color: colors.cardContentColor || colors.text,\n        // 传统CSS变量\n        '--button-bg': colors.buttonBackground,\n        '--button-color': colors.buttonColor,\n        '--button-border': colors.buttonBorder,\n        '--button-hover-bg': colors.buttonHoverBackground,\n        '--button-text-shadow': colors.buttonTextShadow,\n        '--card-title-color': colors.cardTitleColor || colors.text,\n        '--card-content-color': colors.cardContentColor || colors.text,\n        '--text-shadow': `0 1px 2px ${textShadowColor}`\n      };\n    });\n    return {\n      cardStyles\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "name", "props", "title", "type", "String", "required", "icon", "default", "style", "Object", "themeColors", "setup", "cardStyles", "colors", "glassBackground", "glassBorder", "text", "cardTitleColor", "cardContentColor", "buttonBackground", "buttonColor", "buttonBorder", "buttonHoverBackground", "buttonTextShadow", "backgroundBrightness", "useIntelligentStyles", "isAIEnhanced", "intelligentCardStyles", "shadowIntensity", "textShadowColor", "intelligentStyles", "intelligentTextColors", "intelligentButtonStyles", "borderRadius", "<PERSON><PERSON>ilter", "background", "border", "boxShadow", "color", "content", "hoverBackground", "textShadow", "aiAnalysis", "mood", "brightness"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\GlassCard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"glass-card\" :style=\"cardStyles\">\r\n    <div class=\"card-header\">\r\n      <i :class=\"icon\" class=\"card-icon\"></i>\r\n      <h3 class=\"card-title\">{{ title }}</h3>\r\n    </div>\r\n    <div class=\"card-content\">\r\n      <slot></slot>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { computed } from 'vue'\r\n\r\nexport default {\r\n  name: 'GlassC<PERSON>',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    icon: {\r\n      type: String,\r\n      default: 'fas fa-cube'\r\n    },\r\n    style: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n\r\n  setup(props) {\r\n    const cardStyles = computed(() => {\r\n      // 使用主题颜色或默认值\r\n      const colors = props.themeColors || {\r\n        glassBackground: 'rgba(255, 255, 255, 0.15)',\r\n        glassBorder: 'rgba(255, 255, 255, 0.2)',\r\n        text: '#ffffff',\r\n        cardTitleColor: '#ffffff',\r\n        cardContentColor: '#ecf0f1',\r\n        buttonBackground: 'rgba(74, 144, 226, 0.8)',\r\n        buttonColor: '#FFFFFF',\r\n        buttonBorder: 'rgba(255, 255, 255, 0.6)',\r\n        buttonHoverBackground: 'rgba(104, 174, 256, 0.9)',\r\n        buttonTextShadow: '0 1px 2px rgba(0, 0, 0, 0.8)',\r\n        backgroundBrightness: 128\r\n      }\r\n\r\n      // 检查是否有AI增强的智能样式\r\n      const useIntelligentStyles = colors.isAIEnhanced && colors.intelligentCardStyles\r\n\r\n      // 根据背景亮度调整阴影\r\n      const shadowIntensity = colors.backgroundBrightness > 150 ? 0.2 : 0.1\r\n      const textShadowColor = colors.backgroundBrightness > 150 ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)'\r\n\r\n      // 如果有AI智能样式，优先使用\r\n      if (useIntelligentStyles) {\r\n        const intelligentStyles = colors.intelligentCardStyles\r\n        const intelligentTextColors = colors.intelligentTextColors || {}\r\n        const intelligentButtonStyles = colors.intelligentButtonStyles || {}\r\n\r\n        return {\r\n          // AI智能样式\r\n          borderRadius: intelligentStyles.borderRadius || props.style.borderRadius || '16px',\r\n          backdropFilter: intelligentStyles.backdropFilter || props.style.backdropFilter || 'blur(12px)',\r\n          background: intelligentStyles.background || props.style.background || colors.glassBackground,\r\n          border: intelligentStyles.border || props.style.border || `1px solid ${colors.glassBorder}`,\r\n          boxShadow: intelligentStyles.boxShadow || props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,\r\n          color: intelligentTextColors.content || colors.cardContentColor || colors.text,\r\n          // AI增强的CSS变量\r\n          '--button-bg': intelligentButtonStyles.background || colors.buttonBackground,\r\n          '--button-color': intelligentButtonStyles.color || colors.buttonColor,\r\n          '--button-border': intelligentButtonStyles.border || colors.buttonBorder,\r\n          '--button-hover-bg': intelligentButtonStyles.hoverBackground || colors.buttonHoverBackground,\r\n          '--button-text-shadow': intelligentButtonStyles.textShadow || colors.buttonTextShadow,\r\n          '--card-title-color': intelligentTextColors.title || colors.cardTitleColor || colors.text,\r\n          '--card-content-color': intelligentTextColors.content || colors.cardContentColor || colors.text,\r\n          '--text-shadow': intelligentTextColors.textShadow || `0 1px 2px ${textShadowColor}`,\r\n          // AI分析信息\r\n          '--ai-mood': colors.aiAnalysis?.mood || 'default',\r\n          '--ai-brightness': colors.aiAnalysis?.brightness || 'medium'\r\n        }\r\n      }\r\n\r\n      // 传统样式（降级方案）\r\n      return {\r\n        borderRadius: props.style.borderRadius || '16px',\r\n        backdropFilter: props.style.backdropFilter || 'blur(12px)',\r\n        background: props.style.background || colors.glassBackground,\r\n        border: props.style.border || `1px solid ${colors.glassBorder}`,\r\n        boxShadow: props.style.boxShadow || `0 8px 32px rgba(0, 0, 0, ${shadowIntensity})`,\r\n        color: colors.cardContentColor || colors.text,\r\n        // 传统CSS变量\r\n        '--button-bg': colors.buttonBackground,\r\n        '--button-color': colors.buttonColor,\r\n        '--button-border': colors.buttonBorder,\r\n        '--button-hover-bg': colors.buttonHoverBackground,\r\n        '--button-text-shadow': colors.buttonTextShadow,\r\n        '--card-title-color': colors.cardTitleColor || colors.text,\r\n        '--card-content-color': colors.cardContentColor || colors.text,\r\n        '--text-shadow': `0 1px 2px ${textShadowColor}`\r\n      }\r\n    })\r\n\r\n    return {\r\n      cardStyles\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.glass-card {\r\n  padding: 20px;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.glass-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.card-icon {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n  color: var(--card-title-color, #ffffff);\r\n  text-shadow: var(--text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n}\r\n\r\n.card-content {\r\n  flex: 1;\r\n  color: var(--card-content-color, #ecf0f1);\r\n  text-shadow: var(--text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));\r\n}\r\n\r\n/* 玻璃态效果增强 */\r\n.glass-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, \r\n    transparent, \r\n    rgba(255, 255, 255, 0.3), \r\n    transparent\r\n  );\r\n}\r\n\r\n.glass-card::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  bottom: 0;\r\n  width: 1px;\r\n  background: linear-gradient(180deg, \r\n    transparent, \r\n    rgba(255, 255, 255, 0.3), \r\n    transparent\r\n  );\r\n}\r\n</style>"], "mappings": "AAaA,SAASA,QAAO,QAAS,KAAI;AAE7B,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJH,IAAI,EAAEC,MAAM;MACZG,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLL,IAAI,EAAEM,MAAM;MACZF,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IACDG,WAAW,EAAE;MACXP,IAAI,EAAEM,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EAEDI,KAAKA,CAACV,KAAK,EAAE;IACX,MAAMW,UAAS,GAAIb,QAAQ,CAAC,MAAM;MAChC;MACA,MAAMc,MAAK,GAAIZ,KAAK,CAACS,WAAU,IAAK;QAClCI,eAAe,EAAE,2BAA2B;QAC5CC,WAAW,EAAE,0BAA0B;QACvCC,IAAI,EAAE,SAAS;QACfC,cAAc,EAAE,SAAS;QACzBC,gBAAgB,EAAE,SAAS;QAC3BC,gBAAgB,EAAE,yBAAyB;QAC3CC,WAAW,EAAE,SAAS;QACtBC,YAAY,EAAE,0BAA0B;QACxCC,qBAAqB,EAAE,0BAA0B;QACjDC,gBAAgB,EAAE,8BAA8B;QAChDC,oBAAoB,EAAE;MACxB;;MAEA;MACA,MAAMC,oBAAmB,GAAIZ,MAAM,CAACa,YAAW,IAAKb,MAAM,CAACc,qBAAoB;;MAE/E;MACA,MAAMC,eAAc,GAAIf,MAAM,CAACW,oBAAmB,GAAI,GAAE,GAAI,GAAE,GAAI,GAAE;MACpE,MAAMK,eAAc,GAAIhB,MAAM,CAACW,oBAAmB,GAAI,GAAE,GAAI,oBAAmB,GAAI,0BAAyB;;MAE5G;MACA,IAAIC,oBAAoB,EAAE;QACxB,MAAMK,iBAAgB,GAAIjB,MAAM,CAACc,qBAAoB;QACrD,MAAMI,qBAAoB,GAAIlB,MAAM,CAACkB,qBAAoB,IAAK,CAAC;QAC/D,MAAMC,uBAAsB,GAAInB,MAAM,CAACmB,uBAAsB,IAAK,CAAC;QAEnE,OAAO;UACL;UACAC,YAAY,EAAEH,iBAAiB,CAACG,YAAW,IAAKhC,KAAK,CAACO,KAAK,CAACyB,YAAW,IAAK,MAAM;UAClFC,cAAc,EAAEJ,iBAAiB,CAACI,cAAa,IAAKjC,KAAK,CAACO,KAAK,CAAC0B,cAAa,IAAK,YAAY;UAC9FC,UAAU,EAAEL,iBAAiB,CAACK,UAAS,IAAKlC,KAAK,CAACO,KAAK,CAAC2B,UAAS,IAAKtB,MAAM,CAACC,eAAe;UAC5FsB,MAAM,EAAEN,iBAAiB,CAACM,MAAK,IAAKnC,KAAK,CAACO,KAAK,CAAC4B,MAAK,IAAK,aAAavB,MAAM,CAACE,WAAW,EAAE;UAC3FsB,SAAS,EAAEP,iBAAiB,CAACO,SAAQ,IAAKpC,KAAK,CAACO,KAAK,CAAC6B,SAAQ,IAAK,4BAA4BT,eAAe,GAAG;UACjHU,KAAK,EAAEP,qBAAqB,CAACQ,OAAM,IAAK1B,MAAM,CAACK,gBAAe,IAAKL,MAAM,CAACG,IAAI;UAC9E;UACA,aAAa,EAAEgB,uBAAuB,CAACG,UAAS,IAAKtB,MAAM,CAACM,gBAAgB;UAC5E,gBAAgB,EAAEa,uBAAuB,CAACM,KAAI,IAAKzB,MAAM,CAACO,WAAW;UACrE,iBAAiB,EAAEY,uBAAuB,CAACI,MAAK,IAAKvB,MAAM,CAACQ,YAAY;UACxE,mBAAmB,EAAEW,uBAAuB,CAACQ,eAAc,IAAK3B,MAAM,CAACS,qBAAqB;UAC5F,sBAAsB,EAAEU,uBAAuB,CAACS,UAAS,IAAK5B,MAAM,CAACU,gBAAgB;UACrF,oBAAoB,EAAEQ,qBAAqB,CAAC7B,KAAI,IAAKW,MAAM,CAACI,cAAa,IAAKJ,MAAM,CAACG,IAAI;UACzF,sBAAsB,EAAEe,qBAAqB,CAACQ,OAAM,IAAK1B,MAAM,CAACK,gBAAe,IAAKL,MAAM,CAACG,IAAI;UAC/F,eAAe,EAAEe,qBAAqB,CAACU,UAAS,IAAK,aAAaZ,eAAe,EAAE;UACnF;UACA,WAAW,EAAEhB,MAAM,CAAC6B,UAAU,EAAEC,IAAG,IAAK,SAAS;UACjD,iBAAiB,EAAE9B,MAAM,CAAC6B,UAAU,EAAEE,UAAS,IAAK;QACtD;MACF;;MAEA;MACA,OAAO;QACLX,YAAY,EAAEhC,KAAK,CAACO,KAAK,CAACyB,YAAW,IAAK,MAAM;QAChDC,cAAc,EAAEjC,KAAK,CAACO,KAAK,CAAC0B,cAAa,IAAK,YAAY;QAC1DC,UAAU,EAAElC,KAAK,CAACO,KAAK,CAAC2B,UAAS,IAAKtB,MAAM,CAACC,eAAe;QAC5DsB,MAAM,EAAEnC,KAAK,CAACO,KAAK,CAAC4B,MAAK,IAAK,aAAavB,MAAM,CAACE,WAAW,EAAE;QAC/DsB,SAAS,EAAEpC,KAAK,CAACO,KAAK,CAAC6B,SAAQ,IAAK,4BAA4BT,eAAe,GAAG;QAClFU,KAAK,EAAEzB,MAAM,CAACK,gBAAe,IAAKL,MAAM,CAACG,IAAI;QAC7C;QACA,aAAa,EAAEH,MAAM,CAACM,gBAAgB;QACtC,gBAAgB,EAAEN,MAAM,CAACO,WAAW;QACpC,iBAAiB,EAAEP,MAAM,CAACQ,YAAY;QACtC,mBAAmB,EAAER,MAAM,CAACS,qBAAqB;QACjD,sBAAsB,EAAET,MAAM,CAACU,gBAAgB;QAC/C,oBAAoB,EAAEV,MAAM,CAACI,cAAa,IAAKJ,MAAM,CAACG,IAAI;QAC1D,sBAAsB,EAAEH,MAAM,CAACK,gBAAe,IAAKL,MAAM,CAACG,IAAI;QAC9D,eAAe,EAAE,aAAaa,eAAe;MAC/C;IACF,CAAC;IAED,OAAO;MACLjB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}