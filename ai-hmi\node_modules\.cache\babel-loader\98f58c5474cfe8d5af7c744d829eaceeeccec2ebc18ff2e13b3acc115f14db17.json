{"ast": null, "code": "import { computed } from 'vue';\nimport { useLayoutStore } from '@/store';\nexport default {\n  name: 'BaseCard',\n  props: {\n    // 卡片类型\n    cardType: {\n      type: String,\n      default: 'default'\n    },\n    // 卡片尺寸\n    size: {\n      type: String,\n      default: 'medium',\n      validator: value => ['small', 'medium', 'large', 'custom'].includes(value)\n    },\n    // 自定义尺寸（当size为custom时使用）\n    customSize: {\n      type: Object,\n      default: () => ({\n        width: 4,\n        height: 2\n      })\n    },\n    // 网格位置\n    position: {\n      type: Object,\n      default: () => ({\n        x: 1,\n        y: 1\n      })\n    },\n    // 卡片标题\n    title: {\n      type: String,\n      default: ''\n    },\n    // 卡片描述\n    description: {\n      type: String,\n      default: ''\n    },\n    // 卡片图标\n    icon: {\n      type: String,\n      default: ''\n    },\n    // 主题\n    theme: {\n      type: String,\n      default: 'glass',\n      validator: value => ['glass', 'solid', 'minimal', 'gradient'].includes(value)\n    },\n    // 主题颜色\n    themeColors: {\n      type: Object,\n      default: () => ({})\n    },\n    // 显示配置\n    showHeader: {\n      type: Boolean,\n      default: true\n    },\n    showFooter: {\n      type: Boolean,\n      default: false\n    },\n    // 交互配置\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    actionable: {\n      type: Boolean,\n      default: false\n    },\n    actionText: {\n      type: String,\n      default: ''\n    },\n    closable: {\n      type: Boolean,\n      default: false\n    },\n    // 状态\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['click', 'action', 'close'],\n  setup(props, {\n    emit\n  }) {\n    const layoutStore = useLayoutStore();\n\n    // 计算卡片样式\n    const cardStyles = computed(() => {\n      let gridStyle = {};\n\n      // 根据尺寸类型获取网格样式\n      if (props.size === 'custom') {\n        gridStyle = layoutStore.getComponentPosition('custom', props.position);\n        // 注册自定义尺寸\n        layoutStore.registerComponentSize('custom', props.customSize);\n      } else {\n        const sizeMap = {\n          small: 'cardSmall',\n          medium: 'cardMedium',\n          large: 'cardLarge'\n        };\n        gridStyle = layoutStore.getComponentPosition(sizeMap[props.size], props.position);\n      }\n\n      // 主题颜色样式\n      const themeStyle = {\n        '--card-primary-color': props.themeColors.primary || '#4a90e2',\n        '--card-secondary-color': props.themeColors.secondary || '#7ed321',\n        '--card-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',\n        '--card-text-color': props.themeColors.text || '#ffffff'\n      };\n      return {\n        ...gridStyle,\n        ...themeStyle\n      };\n    });\n\n    // 主题类名\n    const themeClass = computed(() => {\n      return `theme-${props.theme}`;\n    });\n\n    // 处理点击事件\n    const handleClick = () => {\n      if (props.clickable && !props.disabled) {\n        emit('click');\n      }\n    };\n\n    // 处理操作事件\n    const handleAction = () => {\n      if (props.actionable && !props.disabled) {\n        emit('action');\n      }\n    };\n    return {\n      cardStyles,\n      themeClass,\n      handleClick,\n      handleAction\n    };\n  }\n};", "map": {"version": 3, "names": ["computed", "useLayoutStore", "name", "props", "cardType", "type", "String", "default", "size", "validator", "value", "includes", "customSize", "Object", "width", "height", "position", "x", "y", "title", "description", "icon", "theme", "themeColors", "showHeader", "Boolean", "showFooter", "clickable", "actionable", "actionText", "closable", "loading", "disabled", "emits", "setup", "emit", "layoutStore", "cardStyles", "gridStyle", "getComponentPosition", "registerComponentSize", "sizeMap", "small", "medium", "large", "themeStyle", "primary", "secondary", "background", "text", "themeClass", "handleClick", "handleAction"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\BaseCard.vue"], "sourcesContent": ["<template>\r\n  <div \r\n    :class=\"['base-card', `card-${cardType}`, `size-${size}`, themeClass]\"\r\n    :style=\"cardStyles\"\r\n    @click=\"handleClick\"\r\n  >\r\n    <!-- 卡片头部 -->\r\n    <div v-if=\"showHeader\" class=\"card-header\">\r\n      <div class=\"header-left\">\r\n        <i v-if=\"icon\" :class=\"icon\" class=\"card-icon\"></i>\r\n        <h3 v-if=\"title\" class=\"card-title\">{{ title }}</h3>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <slot name=\"header-actions\">\r\n          <button v-if=\"closable\" @click.stop=\"$emit('close')\" class=\"close-btn\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </slot>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 卡片内容 -->\r\n    <div class=\"card-content\" :class=\"{ 'no-header': !showHeader }\">\r\n      <slot>\r\n        <div class=\"default-content\">\r\n          <p v-if=\"description\">{{ description }}</p>\r\n        </div>\r\n      </slot>\r\n    </div>\r\n\r\n    <!-- 卡片底部 -->\r\n    <div v-if=\"showFooter\" class=\"card-footer\">\r\n      <slot name=\"footer\">\r\n        <div class=\"footer-actions\">\r\n          <button v-if=\"actionable\" @click.stop=\"handleAction\" class=\"action-btn\">\r\n            {{ actionText || '操作' }}\r\n          </button>\r\n        </div>\r\n      </slot>\r\n    </div>\r\n\r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading-overlay\">\r\n      <div class=\"loading-spinner\">\r\n        <i class=\"fas fa-spinner fa-spin\"></i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { computed } from 'vue'\r\nimport { useLayoutStore } from '@/store'\r\n\r\nexport default {\r\n  name: 'BaseCard',\r\n  props: {\r\n    // 卡片类型\r\n    cardType: {\r\n      type: String,\r\n      default: 'default'\r\n    },\r\n    \r\n    // 卡片尺寸\r\n    size: {\r\n      type: String,\r\n      default: 'medium',\r\n      validator: (value) => ['small', 'medium', 'large', 'custom'].includes(value)\r\n    },\r\n    \r\n    // 自定义尺寸（当size为custom时使用）\r\n    customSize: {\r\n      type: Object,\r\n      default: () => ({ width: 4, height: 2 })\r\n    },\r\n    \r\n    // 网格位置\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 1 })\r\n    },\r\n    \r\n    // 卡片标题\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    // 卡片描述\r\n    description: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    // 卡片图标\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    // 主题\r\n    theme: {\r\n      type: String,\r\n      default: 'glass',\r\n      validator: (value) => ['glass', 'solid', 'minimal', 'gradient'].includes(value)\r\n    },\r\n    \r\n    // 主题颜色\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    \r\n    // 显示配置\r\n    showHeader: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    \r\n    showFooter: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    // 交互配置\r\n    clickable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    actionable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    actionText: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    \r\n    closable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    // 状态\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    \r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  \r\n  emits: ['click', 'action', 'close'],\r\n  \r\n  setup(props, { emit }) {\r\n    const layoutStore = useLayoutStore()\r\n    \r\n    // 计算卡片样式\r\n    const cardStyles = computed(() => {\r\n      let gridStyle = {}\r\n      \r\n      // 根据尺寸类型获取网格样式\r\n      if (props.size === 'custom') {\r\n        gridStyle = layoutStore.getComponentPosition('custom', props.position)\r\n        // 注册自定义尺寸\r\n        layoutStore.registerComponentSize('custom', props.customSize)\r\n      } else {\r\n        const sizeMap = {\r\n          small: 'cardSmall',\r\n          medium: 'cardMedium', \r\n          large: 'cardLarge'\r\n        }\r\n        gridStyle = layoutStore.getComponentPosition(sizeMap[props.size], props.position)\r\n      }\r\n      \r\n      // 主题颜色样式\r\n      const themeStyle = {\r\n        '--card-primary-color': props.themeColors.primary || '#4a90e2',\r\n        '--card-secondary-color': props.themeColors.secondary || '#7ed321',\r\n        '--card-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',\r\n        '--card-text-color': props.themeColors.text || '#ffffff'\r\n      }\r\n      \r\n      return {\r\n        ...gridStyle,\r\n        ...themeStyle\r\n      }\r\n    })\r\n    \r\n    // 主题类名\r\n    const themeClass = computed(() => {\r\n      return `theme-${props.theme}`\r\n    })\r\n    \r\n    // 处理点击事件\r\n    const handleClick = () => {\r\n      if (props.clickable && !props.disabled) {\r\n        emit('click')\r\n      }\r\n    }\r\n    \r\n    // 处理操作事件\r\n    const handleAction = () => {\r\n      if (props.actionable && !props.disabled) {\r\n        emit('action')\r\n      }\r\n    }\r\n    \r\n    return {\r\n      cardStyles,\r\n      themeClass,\r\n      handleClick,\r\n      handleAction\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.base-card {\r\n  position: relative;\r\n  border-radius: 15px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  cursor: default;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 主题样式 */\r\n.theme-glass {\r\n  background: var(--card-background, rgba(255, 255, 255, 0.1));\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.theme-solid {\r\n  background: var(--card-primary-color, #4a90e2);\r\n  border: none;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.theme-minimal {\r\n  background: transparent;\r\n  border: 1px solid var(--card-primary-color, #4a90e2);\r\n  box-shadow: none;\r\n}\r\n\r\n.theme-gradient {\r\n  background: linear-gradient(135deg, \r\n    var(--card-primary-color, #4a90e2) 0%, \r\n    var(--card-secondary-color, #7ed321) 100%);\r\n  border: none;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 可点击状态 */\r\n.base-card.clickable {\r\n  cursor: pointer;\r\n}\r\n\r\n.base-card.clickable:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 禁用状态 */\r\n.base-card.disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 卡片头部 */\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.card-icon {\r\n  font-size: 18px;\r\n  color: var(--card-primary-color, #4a90e2);\r\n}\r\n\r\n.card-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: var(--card-text-color, #ffffff);\r\n}\r\n\r\n.close-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: var(--card-text-color, #ffffff);\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 卡片内容 */\r\n.card-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  color: var(--card-text-color, #ffffff);\r\n  overflow: hidden;\r\n}\r\n\r\n.card-content.no-header {\r\n  padding-top: 20px;\r\n}\r\n\r\n.default-content p {\r\n  margin: 0;\r\n  opacity: 0.8;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 卡片底部 */\r\n.card-footer {\r\n  padding: 15px 20px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.footer-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n.action-btn {\r\n  padding: 8px 16px;\r\n  border: 1px solid var(--card-primary-color, #4a90e2);\r\n  background: var(--card-primary-color, #4a90e2);\r\n  color: white;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: var(--card-secondary-color, #7ed321);\r\n  border-color: var(--card-secondary-color, #7ed321);\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.loading-spinner {\r\n  color: var(--card-primary-color, #4a90e2);\r\n  font-size: 24px;\r\n}\r\n</style>\r\n"], "mappings": "AAmDA,SAASA,QAAO,QAAS,KAAI;AAC7B,SAASC,cAAa,QAAS,SAAQ;AAEvC,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE;IACL;IACAC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAC,IAAI,EAAE;MACJH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBE,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACD,KAAK;IAC7E,CAAC;IAED;IACAE,UAAU,EAAE;MACVP,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO;QAAEO,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;IACzC,CAAC;IAED;IACAC,QAAQ,EAAE;MACRX,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO;QAAEU,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IAED;IACAC,KAAK,EAAE;MACLd,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAa,WAAW,EAAE;MACXf,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAc,IAAI,EAAE;MACJhB,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAED;IACAe,KAAK,EAAE;MACLjB,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,OAAO;MAChBE,SAAS,EAAGC,KAAK,IAAK,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAACC,QAAQ,CAACD,KAAK;IAChF,CAAC;IAED;IACAa,WAAW,EAAE;MACXlB,IAAI,EAAEQ,MAAM;MACZN,OAAO,EAAEA,CAAA,MAAO,CAAC,CAAC;IACpB,CAAC;IAED;IACAiB,UAAU,EAAE;MACVnB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDmB,UAAU,EAAE;MACVrB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAED;IACAoB,SAAS,EAAE;MACTtB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDqB,UAAU,EAAE;MACVvB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDsB,UAAU,EAAE;MACVxB,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IAEDuB,QAAQ,EAAE;MACRzB,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAED;IACAwB,OAAO,EAAE;MACP1B,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX,CAAC;IAEDyB,QAAQ,EAAE;MACR3B,IAAI,EAAEoB,OAAO;MACblB,OAAO,EAAE;IACX;EACF,CAAC;EAED0B,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;EAEnCC,KAAKA,CAAC/B,KAAK,EAAE;IAAEgC;EAAK,CAAC,EAAE;IACrB,MAAMC,WAAU,GAAInC,cAAc,CAAC;;IAEnC;IACA,MAAMoC,UAAS,GAAIrC,QAAQ,CAAC,MAAM;MAChC,IAAIsC,SAAQ,GAAI,CAAC;;MAEjB;MACA,IAAInC,KAAK,CAACK,IAAG,KAAM,QAAQ,EAAE;QAC3B8B,SAAQ,GAAIF,WAAW,CAACG,oBAAoB,CAAC,QAAQ,EAAEpC,KAAK,CAACa,QAAQ;QACrE;QACAoB,WAAW,CAACI,qBAAqB,CAAC,QAAQ,EAAErC,KAAK,CAACS,UAAU;MAC9D,OAAO;QACL,MAAM6B,OAAM,GAAI;UACdC,KAAK,EAAE,WAAW;UAClBC,MAAM,EAAE,YAAY;UACpBC,KAAK,EAAE;QACT;QACAN,SAAQ,GAAIF,WAAW,CAACG,oBAAoB,CAACE,OAAO,CAACtC,KAAK,CAACK,IAAI,CAAC,EAAEL,KAAK,CAACa,QAAQ;MAClF;;MAEA;MACA,MAAM6B,UAAS,GAAI;QACjB,sBAAsB,EAAE1C,KAAK,CAACoB,WAAW,CAACuB,OAAM,IAAK,SAAS;QAC9D,wBAAwB,EAAE3C,KAAK,CAACoB,WAAW,CAACwB,SAAQ,IAAK,SAAS;QAClE,mBAAmB,EAAE5C,KAAK,CAACoB,WAAW,CAACyB,UAAS,IAAK,0BAA0B;QAC/E,mBAAmB,EAAE7C,KAAK,CAACoB,WAAW,CAAC0B,IAAG,IAAK;MACjD;MAEA,OAAO;QACL,GAAGX,SAAS;QACZ,GAAGO;MACL;IACF,CAAC;;IAED;IACA,MAAMK,UAAS,GAAIlD,QAAQ,CAAC,MAAM;MAChC,OAAO,SAASG,KAAK,CAACmB,KAAK,EAAC;IAC9B,CAAC;;IAED;IACA,MAAM6B,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIhD,KAAK,CAACwB,SAAQ,IAAK,CAACxB,KAAK,CAAC6B,QAAQ,EAAE;QACtCG,IAAI,CAAC,OAAO;MACd;IACF;;IAEA;IACA,MAAMiB,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIjD,KAAK,CAACyB,UAAS,IAAK,CAACzB,KAAK,CAAC6B,QAAQ,EAAE;QACvCG,IAAI,CAAC,QAAQ;MACf;IACF;IAEA,OAAO;MACLE,UAAU;MACVa,UAAU;MACVC,WAAW;MACXC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}