{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport BaseCard from '../BaseCard.vue';\nexport default {\n  name: 'MusicControlCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({\n        x: 1,\n        y: 2\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#9b59b6',\n        secondary: '#e74c3c',\n        background: 'rgba(155, 89, 182, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  emits: ['card-click', 'song-changed', 'play-state-changed'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式状态\n    const isPlaying = ref(false);\n    const isShuffled = ref(false);\n    const isRepeating = ref(false);\n    const isMuted = ref(false);\n    const volume = ref(75);\n    const currentTime = ref(0);\n    const currentSongIndex = ref(0);\n\n    // 播放列表数据\n    const playlist = ref([{\n      title: '夜空中最亮的星',\n      artist: '逃跑计划',\n      album: '世界',\n      duration: 245,\n      cover: null\n    }, {\n      title: '成都',\n      artist: '赵雷',\n      album: '无法长大',\n      duration: 327,\n      cover: null\n    }, {\n      title: '南山南',\n      artist: '马頔',\n      album: '孤岛',\n      duration: 290,\n      cover: null\n    }, {\n      title: '理想',\n      artist: '赵雷',\n      album: '赵小雷',\n      duration: 268,\n      cover: null\n    }]);\n\n    // 计算属性\n    const currentSong = computed(() => playlist.value[currentSongIndex.value]);\n    const progressPercentage = computed(() => {\n      if (currentSong.value.duration === 0) return 0;\n      return currentTime.value / currentSong.value.duration * 100;\n    });\n    const volumeIcon = computed(() => {\n      if (isMuted.value || volume.value === 0) {\n        return 'fas fa-volume-mute';\n      } else if (volume.value < 50) {\n        return 'fas fa-volume-down';\n      } else {\n        return 'fas fa-volume-up';\n      }\n    });\n\n    // 播放控制\n    let playTimer = null;\n    const togglePlay = () => {\n      isPlaying.value = !isPlaying.value;\n      if (isPlaying.value) {\n        startPlayTimer();\n      } else {\n        stopPlayTimer();\n      }\n      emit('play-state-changed', {\n        isPlaying: isPlaying.value,\n        song: currentSong.value\n      });\n    };\n    const startPlayTimer = () => {\n      playTimer = setInterval(() => {\n        currentTime.value += 1;\n\n        // 歌曲播放完毕\n        if (currentTime.value >= currentSong.value.duration) {\n          if (isRepeating.value) {\n            currentTime.value = 0;\n          } else {\n            nextSong();\n          }\n        }\n      }, 1000);\n    };\n    const stopPlayTimer = () => {\n      if (playTimer) {\n        clearInterval(playTimer);\n        playTimer = null;\n      }\n    };\n    const previousSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length);\n      } else {\n        currentSongIndex.value = currentSongIndex.value > 0 ? currentSongIndex.value - 1 : playlist.value.length - 1;\n      }\n      currentTime.value = 0;\n      emit('song-changed', currentSong.value);\n    };\n    const nextSong = () => {\n      if (isShuffled.value) {\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length);\n      } else {\n        currentSongIndex.value = currentSongIndex.value < playlist.value.length - 1 ? currentSongIndex.value + 1 : 0;\n      }\n      currentTime.value = 0;\n      emit('song-changed', currentSong.value);\n    };\n    const playSong = index => {\n      currentSongIndex.value = index;\n      currentTime.value = 0;\n      if (!isPlaying.value) {\n        togglePlay();\n      }\n      emit('song-changed', currentSong.value);\n    };\n    const seekTo = event => {\n      const progressBar = event.currentTarget;\n      const rect = progressBar.getBoundingClientRect();\n      const clickX = event.clientX - rect.left;\n      const percentage = clickX / rect.width;\n      currentTime.value = Math.floor(percentage * currentSong.value.duration);\n    };\n    const toggleShuffle = () => {\n      isShuffled.value = !isShuffled.value;\n    };\n    const toggleRepeat = () => {\n      isRepeating.value = !isRepeating.value;\n    };\n    const toggleMute = () => {\n      isMuted.value = !isMuted.value;\n    };\n    const openFullPlaylist = () => {\n      console.log('打开完整播放列表');\n    };\n    const handleCardClick = () => {\n      emit('card-click', 'music');\n    };\n\n    // 工具函数\n    const formatTime = seconds => {\n      const mins = Math.floor(seconds / 60);\n      const secs = seconds % 60;\n      return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      console.log('音乐控制卡片已加载');\n    });\n    onUnmounted(() => {\n      stopPlayTimer();\n    });\n    return {\n      isPlaying,\n      isShuffled,\n      isRepeating,\n      isMuted,\n      volume,\n      currentTime,\n      currentSongIndex,\n      playlist,\n      currentSong,\n      progressPercentage,\n      volumeIcon,\n      togglePlay,\n      previousSong,\n      nextSong,\n      playSong,\n      seekTo,\n      toggleShuffle,\n      toggleRepeat,\n      toggleMute,\n      openFullPlaylist,\n      handleCardClick,\n      formatTime\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "BaseCard", "name", "components", "props", "position", "type", "Object", "default", "x", "y", "theme", "String", "themeColors", "primary", "secondary", "background", "text", "emits", "setup", "emit", "isPlaying", "isShuffled", "isRepeating", "isMuted", "volume", "currentTime", "currentSongIndex", "playlist", "title", "artist", "album", "duration", "cover", "currentSong", "value", "progressPercentage", "volumeIcon", "playTimer", "togglePlay", "startPlayTimer", "stopPlayTimer", "song", "setInterval", "nextSong", "clearInterval", "previousSong", "Math", "floor", "random", "length", "playSong", "index", "seekTo", "event", "progressBar", "currentTarget", "rect", "getBoundingClientRect", "clickX", "clientX", "left", "percentage", "width", "toggleShuffle", "toggleRepeat", "toggleMute", "openFullPlaylist", "console", "log", "handleCardClick", "formatTime", "seconds", "mins", "secs", "toString", "padStart"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\MusicControlCard.vue"], "sourcesContent": ["<template>\r\n  <BaseCard\r\n    card-type=\"music\"\r\n    size=\"large\"\r\n    :position=\"position\"\r\n    :theme=\"theme\"\r\n    :theme-colors=\"themeColors\"\r\n    :title=\"'音乐控制'\"\r\n    :icon=\"'fas fa-music'\"\r\n    :clickable=\"true\"\r\n    :show-header=\"false\"\r\n    @click=\"handleCardClick\"\r\n    class=\"music-control-card\"\r\n  >\r\n    <div class=\"music-content\">\r\n      <!-- 专辑封面和歌曲信息 -->\r\n      <div class=\"music-info\">\r\n        <div class=\"album-cover\">\r\n          <img \r\n            v-if=\"currentSong.cover\"\r\n            :src=\"currentSong.cover\"\r\n            :alt=\"currentSong.title\"\r\n            class=\"cover-image\"\r\n          />\r\n          <div v-else class=\"cover-placeholder\">\r\n            <i class=\"fas fa-music\"></i>\r\n          </div>\r\n          \r\n          <!-- 播放状态覆盖层 -->\r\n          <div class=\"play-overlay\" :class=\"{ active: isPlaying }\">\r\n            <div class=\"sound-waves\">\r\n              <div class=\"wave\" v-for=\"i in 4\" :key=\"i\" :style=\"{ animationDelay: `${i * 0.1}s` }\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"song-details\">\r\n          <h3 class=\"song-title\">{{ currentSong.title }}</h3>\r\n          <p class=\"song-artist\">{{ currentSong.artist }}</p>\r\n          <p class=\"song-album\">{{ currentSong.album }}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 播放进度 -->\r\n      <div class=\"progress-section\">\r\n        <div class=\"time-display\">\r\n          <span class=\"current-time\">{{ formatTime(currentTime) }}</span>\r\n          <span class=\"total-time\">{{ formatTime(currentSong.duration) }}</span>\r\n        </div>\r\n        <div class=\"progress-bar\" @click=\"seekTo\">\r\n          <div class=\"progress-track\"></div>\r\n          <div \r\n            class=\"progress-fill\" \r\n            :style=\"{ width: `${progressPercentage}%` }\"\r\n          ></div>\r\n          <div \r\n            class=\"progress-thumb\" \r\n            :style=\"{ left: `${progressPercentage}%` }\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 播放控制 -->\r\n      <div class=\"control-section\">\r\n        <div class=\"main-controls\">\r\n          <button @click=\"previousSong\" class=\"control-btn\">\r\n            <i class=\"fas fa-step-backward\"></i>\r\n          </button>\r\n          \r\n          <button @click=\"togglePlay\" class=\"control-btn play-btn\">\r\n            <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\r\n          </button>\r\n          \r\n          <button @click=\"nextSong\" class=\"control-btn\">\r\n            <i class=\"fas fa-step-forward\"></i>\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"secondary-controls\">\r\n          <button @click=\"toggleShuffle\" :class=\"['control-btn', { active: isShuffled }]\">\r\n            <i class=\"fas fa-random\"></i>\r\n          </button>\r\n          \r\n          <button @click=\"toggleRepeat\" :class=\"['control-btn', { active: isRepeating }]\">\r\n            <i class=\"fas fa-redo\"></i>\r\n          </button>\r\n          \r\n          <div class=\"volume-control\">\r\n            <button @click=\"toggleMute\" class=\"control-btn volume-btn\">\r\n              <i :class=\"volumeIcon\"></i>\r\n            </button>\r\n            <div class=\"volume-slider\">\r\n              <input \r\n                type=\"range\" \r\n                min=\"0\" \r\n                max=\"100\" \r\n                v-model=\"volume\"\r\n                class=\"volume-input\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 播放列表预览 -->\r\n      <div class=\"playlist-preview\">\r\n        <div class=\"playlist-header\">\r\n          <span>播放列表</span>\r\n          <button @click=\"openFullPlaylist\" class=\"playlist-btn\">\r\n            <i class=\"fas fa-list\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"playlist-items\">\r\n          <div \r\n            v-for=\"(song, index) in playlist.slice(0, 3)\" \r\n            :key=\"index\"\r\n            :class=\"['playlist-item', { active: currentSongIndex === index }]\"\r\n            @click=\"playSong(index)\"\r\n          >\r\n            <div class=\"item-info\">\r\n              <span class=\"item-title\">{{ song.title }}</span>\r\n              <span class=\"item-artist\">{{ song.artist }}</span>\r\n            </div>\r\n            <div class=\"item-duration\">{{ formatTime(song.duration) }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </BaseCard>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport BaseCard from '../BaseCard.vue'\r\n\r\nexport default {\r\n  name: 'MusicControlCard',\r\n  components: {\r\n    BaseCard\r\n  },\r\n  props: {\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 2 })\r\n    },\r\n    theme: {\r\n      type: String,\r\n      default: 'glass'\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({\r\n        primary: '#9b59b6',\r\n        secondary: '#e74c3c',\r\n        background: 'rgba(155, 89, 182, 0.1)',\r\n        text: '#ffffff'\r\n      })\r\n    }\r\n  },\r\n  \r\n  emits: ['card-click', 'song-changed', 'play-state-changed'],\r\n  \r\n  setup(props, { emit }) {\r\n    // 响应式状态\r\n    const isPlaying = ref(false)\r\n    const isShuffled = ref(false)\r\n    const isRepeating = ref(false)\r\n    const isMuted = ref(false)\r\n    const volume = ref(75)\r\n    const currentTime = ref(0)\r\n    const currentSongIndex = ref(0)\r\n    \r\n    // 播放列表数据\r\n    const playlist = ref([\r\n      {\r\n        title: '夜空中最亮的星',\r\n        artist: '逃跑计划',\r\n        album: '世界',\r\n        duration: 245,\r\n        cover: null\r\n      },\r\n      {\r\n        title: '成都',\r\n        artist: '赵雷',\r\n        album: '无法长大',\r\n        duration: 327,\r\n        cover: null\r\n      },\r\n      {\r\n        title: '南山南',\r\n        artist: '马頔',\r\n        album: '孤岛',\r\n        duration: 290,\r\n        cover: null\r\n      },\r\n      {\r\n        title: '理想',\r\n        artist: '赵雷',\r\n        album: '赵小雷',\r\n        duration: 268,\r\n        cover: null\r\n      }\r\n    ])\r\n    \r\n    // 计算属性\r\n    const currentSong = computed(() => playlist.value[currentSongIndex.value])\r\n    \r\n    const progressPercentage = computed(() => {\r\n      if (currentSong.value.duration === 0) return 0\r\n      return (currentTime.value / currentSong.value.duration) * 100\r\n    })\r\n    \r\n    const volumeIcon = computed(() => {\r\n      if (isMuted.value || volume.value === 0) {\r\n        return 'fas fa-volume-mute'\r\n      } else if (volume.value < 50) {\r\n        return 'fas fa-volume-down'\r\n      } else {\r\n        return 'fas fa-volume-up'\r\n      }\r\n    })\r\n    \r\n    // 播放控制\r\n    let playTimer = null\r\n    \r\n    const togglePlay = () => {\r\n      isPlaying.value = !isPlaying.value\r\n      \r\n      if (isPlaying.value) {\r\n        startPlayTimer()\r\n      } else {\r\n        stopPlayTimer()\r\n      }\r\n      \r\n      emit('play-state-changed', {\r\n        isPlaying: isPlaying.value,\r\n        song: currentSong.value\r\n      })\r\n    }\r\n    \r\n    const startPlayTimer = () => {\r\n      playTimer = setInterval(() => {\r\n        currentTime.value += 1\r\n        \r\n        // 歌曲播放完毕\r\n        if (currentTime.value >= currentSong.value.duration) {\r\n          if (isRepeating.value) {\r\n            currentTime.value = 0\r\n          } else {\r\n            nextSong()\r\n          }\r\n        }\r\n      }, 1000)\r\n    }\r\n    \r\n    const stopPlayTimer = () => {\r\n      if (playTimer) {\r\n        clearInterval(playTimer)\r\n        playTimer = null\r\n      }\r\n    }\r\n    \r\n    const previousSong = () => {\r\n      if (isShuffled.value) {\r\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\r\n      } else {\r\n        currentSongIndex.value = currentSongIndex.value > 0 \r\n          ? currentSongIndex.value - 1 \r\n          : playlist.value.length - 1\r\n      }\r\n      currentTime.value = 0\r\n      emit('song-changed', currentSong.value)\r\n    }\r\n    \r\n    const nextSong = () => {\r\n      if (isShuffled.value) {\r\n        currentSongIndex.value = Math.floor(Math.random() * playlist.value.length)\r\n      } else {\r\n        currentSongIndex.value = currentSongIndex.value < playlist.value.length - 1 \r\n          ? currentSongIndex.value + 1 \r\n          : 0\r\n      }\r\n      currentTime.value = 0\r\n      emit('song-changed', currentSong.value)\r\n    }\r\n    \r\n    const playSong = (index) => {\r\n      currentSongIndex.value = index\r\n      currentTime.value = 0\r\n      if (!isPlaying.value) {\r\n        togglePlay()\r\n      }\r\n      emit('song-changed', currentSong.value)\r\n    }\r\n    \r\n    const seekTo = (event) => {\r\n      const progressBar = event.currentTarget\r\n      const rect = progressBar.getBoundingClientRect()\r\n      const clickX = event.clientX - rect.left\r\n      const percentage = clickX / rect.width\r\n      currentTime.value = Math.floor(percentage * currentSong.value.duration)\r\n    }\r\n    \r\n    const toggleShuffle = () => {\r\n      isShuffled.value = !isShuffled.value\r\n    }\r\n    \r\n    const toggleRepeat = () => {\r\n      isRepeating.value = !isRepeating.value\r\n    }\r\n    \r\n    const toggleMute = () => {\r\n      isMuted.value = !isMuted.value\r\n    }\r\n    \r\n    const openFullPlaylist = () => {\r\n      console.log('打开完整播放列表')\r\n    }\r\n    \r\n    const handleCardClick = () => {\r\n      emit('card-click', 'music')\r\n    }\r\n    \r\n    // 工具函数\r\n    const formatTime = (seconds) => {\r\n      const mins = Math.floor(seconds / 60)\r\n      const secs = seconds % 60\r\n      return `${mins}:${secs.toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 生命周期\r\n    onMounted(() => {\r\n      console.log('音乐控制卡片已加载')\r\n    })\r\n    \r\n    onUnmounted(() => {\r\n      stopPlayTimer()\r\n    })\r\n    \r\n    return {\r\n      isPlaying,\r\n      isShuffled,\r\n      isRepeating,\r\n      isMuted,\r\n      volume,\r\n      currentTime,\r\n      currentSongIndex,\r\n      playlist,\r\n      currentSong,\r\n      progressPercentage,\r\n      volumeIcon,\r\n      togglePlay,\r\n      previousSong,\r\n      nextSong,\r\n      playSong,\r\n      seekTo,\r\n      toggleShuffle,\r\n      toggleRepeat,\r\n      toggleMute,\r\n      openFullPlaylist,\r\n      handleCardClick,\r\n      formatTime\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.music-control-card {\r\n  background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(231, 76, 60, 0.1) 100%);\r\n}\r\n\r\n.music-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  height: 100%;\r\n}\r\n\r\n/* 音乐信息区域 */\r\n.music-info {\r\n  display: flex;\r\n  gap: 15px;\r\n  align-items: center;\r\n}\r\n\r\n.album-cover {\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.cover-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.cover-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n  color: var(--card-primary-color, #9b59b6);\r\n}\r\n\r\n.play-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.play-overlay.active {\r\n  opacity: 1;\r\n}\r\n\r\n.sound-waves {\r\n  display: flex;\r\n  gap: 2px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.wave {\r\n  width: 3px;\r\n  height: 10px;\r\n  background: white;\r\n  border-radius: 2px;\r\n  animation: wave 1s infinite ease-in-out;\r\n}\r\n\r\n.song-details {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.song-title {\r\n  margin: 0 0 5px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: var(--card-text-color, #ffffff);\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.song-artist, .song-album {\r\n  margin: 0;\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 进度区域 */\r\n.progress-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.time-display {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  font-size: 11px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n}\r\n\r\n.progress-bar {\r\n  position: relative;\r\n  height: 4px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 2px;\r\n  cursor: pointer;\r\n}\r\n\r\n.progress-track {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 2px;\r\n}\r\n\r\n.progress-fill {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  bottom: 0;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  border-radius: 2px;\r\n  transition: width 0.1s ease;\r\n}\r\n\r\n.progress-thumb {\r\n  position: absolute;\r\n  top: -4px;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  border-radius: 50%;\r\n  transform: translateX(-50%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.progress-bar:hover .progress-thumb {\r\n  opacity: 1;\r\n}\r\n\r\n/* 控制区域 */\r\n.control-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.main-controls {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.control-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.control-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.control-btn.active {\r\n  background: var(--card-primary-color, #9b59b6);\r\n}\r\n\r\n.play-btn {\r\n  width: 50px;\r\n  height: 50px;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  font-size: 18px;\r\n}\r\n\r\n.play-btn:hover {\r\n  background: var(--card-secondary-color, #e74c3c);\r\n}\r\n\r\n.secondary-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.volume-control {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.volume-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n.volume-slider {\r\n  width: 60px;\r\n}\r\n\r\n.volume-input {\r\n  width: 100%;\r\n  height: 4px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 2px;\r\n  outline: none;\r\n  -webkit-appearance: none;\r\n}\r\n\r\n.volume-input::-webkit-slider-thumb {\r\n  -webkit-appearance: none;\r\n  width: 12px;\r\n  height: 12px;\r\n  background: var(--card-primary-color, #9b59b6);\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 播放列表预览 */\r\n.playlist-preview {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 10px;\r\n  padding: 10px;\r\n  min-height: 0;\r\n}\r\n\r\n.playlist-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n}\r\n\r\n.playlist-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: var(--card-text-color, #ffffff);\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n}\r\n\r\n.playlist-items {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n}\r\n\r\n.playlist-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 6px 8px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.playlist-item:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.playlist-item.active {\r\n  background: var(--card-primary-color, #9b59b6);\r\n}\r\n\r\n.item-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.item-title {\r\n  display: block;\r\n  font-size: 11px;\r\n  color: var(--card-text-color, #ffffff);\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.item-artist {\r\n  display: block;\r\n  font-size: 10px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.7;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.item-duration {\r\n  font-size: 10px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.7;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes wave {\r\n  0%, 100% { height: 10px; }\r\n  50% { height: 20px; }\r\n}\r\n</style>\r\n"], "mappings": "AAoIA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,OAAOC,QAAO,MAAO,iBAAgB;AAErC,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLL,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX,CAAC;IACDK,WAAW,EAAE;MACXP,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QACdM,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,yBAAyB;QACrCC,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;EAEDC,KAAK,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,oBAAoB,CAAC;EAE3DC,KAAKA,CAACf,KAAK,EAAE;IAAEgB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,SAAQ,GAAIxB,GAAG,CAAC,KAAK;IAC3B,MAAMyB,UAAS,GAAIzB,GAAG,CAAC,KAAK;IAC5B,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,KAAK;IAC7B,MAAM2B,OAAM,GAAI3B,GAAG,CAAC,KAAK;IACzB,MAAM4B,MAAK,GAAI5B,GAAG,CAAC,EAAE;IACrB,MAAM6B,WAAU,GAAI7B,GAAG,CAAC,CAAC;IACzB,MAAM8B,gBAAe,GAAI9B,GAAG,CAAC,CAAC;;IAE9B;IACA,MAAM+B,QAAO,GAAI/B,GAAG,CAAC,CACnB;MACEgC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACEJ,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACEJ,KAAK,EAAE,KAAK;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,CAAC,EACD;MACEJ,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE,GAAG;MACbC,KAAK,EAAE;IACT,EACD;;IAED;IACA,MAAMC,WAAU,GAAIpC,QAAQ,CAAC,MAAM8B,QAAQ,CAACO,KAAK,CAACR,gBAAgB,CAACQ,KAAK,CAAC;IAEzE,MAAMC,kBAAiB,GAAItC,QAAQ,CAAC,MAAM;MACxC,IAAIoC,WAAW,CAACC,KAAK,CAACH,QAAO,KAAM,CAAC,EAAE,OAAO;MAC7C,OAAQN,WAAW,CAACS,KAAI,GAAID,WAAW,CAACC,KAAK,CAACH,QAAQ,GAAI,GAAE;IAC9D,CAAC;IAED,MAAMK,UAAS,GAAIvC,QAAQ,CAAC,MAAM;MAChC,IAAI0B,OAAO,CAACW,KAAI,IAAKV,MAAM,CAACU,KAAI,KAAM,CAAC,EAAE;QACvC,OAAO,oBAAmB;MAC5B,OAAO,IAAIV,MAAM,CAACU,KAAI,GAAI,EAAE,EAAE;QAC5B,OAAO,oBAAmB;MAC5B,OAAO;QACL,OAAO,kBAAiB;MAC1B;IACF,CAAC;;IAED;IACA,IAAIG,SAAQ,GAAI,IAAG;IAEnB,MAAMC,UAAS,GAAIA,CAAA,KAAM;MACvBlB,SAAS,CAACc,KAAI,GAAI,CAACd,SAAS,CAACc,KAAI;MAEjC,IAAId,SAAS,CAACc,KAAK,EAAE;QACnBK,cAAc,CAAC;MACjB,OAAO;QACLC,aAAa,CAAC;MAChB;MAEArB,IAAI,CAAC,oBAAoB,EAAE;QACzBC,SAAS,EAAEA,SAAS,CAACc,KAAK;QAC1BO,IAAI,EAAER,WAAW,CAACC;MACpB,CAAC;IACH;IAEA,MAAMK,cAAa,GAAIA,CAAA,KAAM;MAC3BF,SAAQ,GAAIK,WAAW,CAAC,MAAM;QAC5BjB,WAAW,CAACS,KAAI,IAAK;;QAErB;QACA,IAAIT,WAAW,CAACS,KAAI,IAAKD,WAAW,CAACC,KAAK,CAACH,QAAQ,EAAE;UACnD,IAAIT,WAAW,CAACY,KAAK,EAAE;YACrBT,WAAW,CAACS,KAAI,GAAI;UACtB,OAAO;YACLS,QAAQ,CAAC;UACX;QACF;MACF,CAAC,EAAE,IAAI;IACT;IAEA,MAAMH,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIH,SAAS,EAAE;QACbO,aAAa,CAACP,SAAS;QACvBA,SAAQ,GAAI,IAAG;MACjB;IACF;IAEA,MAAMQ,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIxB,UAAU,CAACa,KAAK,EAAE;QACpBR,gBAAgB,CAACQ,KAAI,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIrB,QAAQ,CAACO,KAAK,CAACe,MAAM;MAC3E,OAAO;QACLvB,gBAAgB,CAACQ,KAAI,GAAIR,gBAAgB,CAACQ,KAAI,GAAI,IAC9CR,gBAAgB,CAACQ,KAAI,GAAI,IACzBP,QAAQ,CAACO,KAAK,CAACe,MAAK,GAAI;MAC9B;MACAxB,WAAW,CAACS,KAAI,GAAI;MACpBf,IAAI,CAAC,cAAc,EAAEc,WAAW,CAACC,KAAK;IACxC;IAEA,MAAMS,QAAO,GAAIA,CAAA,KAAM;MACrB,IAAItB,UAAU,CAACa,KAAK,EAAE;QACpBR,gBAAgB,CAACQ,KAAI,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIrB,QAAQ,CAACO,KAAK,CAACe,MAAM;MAC3E,OAAO;QACLvB,gBAAgB,CAACQ,KAAI,GAAIR,gBAAgB,CAACQ,KAAI,GAAIP,QAAQ,CAACO,KAAK,CAACe,MAAK,GAAI,IACtEvB,gBAAgB,CAACQ,KAAI,GAAI,IACzB;MACN;MACAT,WAAW,CAACS,KAAI,GAAI;MACpBf,IAAI,CAAC,cAAc,EAAEc,WAAW,CAACC,KAAK;IACxC;IAEA,MAAMgB,QAAO,GAAKC,KAAK,IAAK;MAC1BzB,gBAAgB,CAACQ,KAAI,GAAIiB,KAAI;MAC7B1B,WAAW,CAACS,KAAI,GAAI;MACpB,IAAI,CAACd,SAAS,CAACc,KAAK,EAAE;QACpBI,UAAU,CAAC;MACb;MACAnB,IAAI,CAAC,cAAc,EAAEc,WAAW,CAACC,KAAK;IACxC;IAEA,MAAMkB,MAAK,GAAKC,KAAK,IAAK;MACxB,MAAMC,WAAU,GAAID,KAAK,CAACE,aAAY;MACtC,MAAMC,IAAG,GAAIF,WAAW,CAACG,qBAAqB,CAAC;MAC/C,MAAMC,MAAK,GAAIL,KAAK,CAACM,OAAM,GAAIH,IAAI,CAACI,IAAG;MACvC,MAAMC,UAAS,GAAIH,MAAK,GAAIF,IAAI,CAACM,KAAI;MACrCrC,WAAW,CAACS,KAAI,GAAIY,IAAI,CAACC,KAAK,CAACc,UAAS,GAAI5B,WAAW,CAACC,KAAK,CAACH,QAAQ;IACxE;IAEA,MAAMgC,aAAY,GAAIA,CAAA,KAAM;MAC1B1C,UAAU,CAACa,KAAI,GAAI,CAACb,UAAU,CAACa,KAAI;IACrC;IAEA,MAAM8B,YAAW,GAAIA,CAAA,KAAM;MACzB1C,WAAW,CAACY,KAAI,GAAI,CAACZ,WAAW,CAACY,KAAI;IACvC;IAEA,MAAM+B,UAAS,GAAIA,CAAA,KAAM;MACvB1C,OAAO,CAACW,KAAI,GAAI,CAACX,OAAO,CAACW,KAAI;IAC/B;IAEA,MAAMgC,gBAAe,GAAIA,CAAA,KAAM;MAC7BC,OAAO,CAACC,GAAG,CAAC,UAAU;IACxB;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5BlD,IAAI,CAAC,YAAY,EAAE,OAAO;IAC5B;;IAEA;IACA,MAAMmD,UAAS,GAAKC,OAAO,IAAK;MAC9B,MAAMC,IAAG,GAAI1B,IAAI,CAACC,KAAK,CAACwB,OAAM,GAAI,EAAE;MACpC,MAAME,IAAG,GAAIF,OAAM,GAAI,EAAC;MACxB,OAAO,GAAGC,IAAI,IAAIC,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IACrD;;IAEA;IACA7E,SAAS,CAAC,MAAM;MACdqE,OAAO,CAACC,GAAG,CAAC,WAAW;IACzB,CAAC;IAEDrE,WAAW,CAAC,MAAM;MAChByC,aAAa,CAAC;IAChB,CAAC;IAED,OAAO;MACLpB,SAAS;MACTC,UAAU;MACVC,WAAW;MACXC,OAAO;MACPC,MAAM;MACNC,WAAW;MACXC,gBAAgB;MAChBC,QAAQ;MACRM,WAAW;MACXE,kBAAkB;MAClBC,UAAU;MACVE,UAAU;MACVO,YAAY;MACZF,QAAQ;MACRO,QAAQ;MACRE,MAAM;MACNW,aAAa;MACbC,YAAY;MACZC,UAAU;MACVC,gBAAgB;MAChBG,eAAe;MACfC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}