{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useVpaStore } from '@/store/modules/vpa';\nexport default {\n  name: 'VPAAvatarWidget',\n  props: {\n    size: {\n      type: String,\n      default: 'medium',\n      validator: value => ['small', 'medium', 'large'].includes(value)\n    },\n    position: {\n      type: Object,\n      default: () => ({\n        x: 1,\n        y: 1\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#667eea',\n        secondary: '#764ba2',\n        background: 'rgba(102, 126, 234, 0.1)',\n        text: '#ffffff'\n      })\n    },\n    showQuickActions: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['avatar-click', 'mode-changed', 'animation-changed', 'quick-action'],\n  setup(props, {\n    emit\n  }) {\n    const vpaStore = useVpaStore();\n\n    // 响应式状态\n    const showHint = ref(false);\n    const showVoiceWave = ref(false);\n    const hintTimer = ref(null);\n\n    // 计算属性\n    const currentMode = computed(() => vpaStore.currentMode);\n    const currentAnimationState = computed(() => vpaStore.currentAnimationState);\n    const currentAnimationConfig = computed(() => vpaStore.currentAnimationConfig);\n    const currentAnimationResource = computed(() => {\n      const animationConfig = vpaStore.currentAnimationConfig;\n      if (animationConfig && animationConfig.resource) {\n        try {\n          // 直接返回资源路径，让img标签处理\n          return `/src/assets/${animationConfig.resource}`;\n        } catch (error) {\n          console.warn('动画资源加载失败:', error);\n          return null;\n        }\n      }\n      return null;\n    });\n    const currentHint = computed(() => {\n      const hints = {\n        companion: '我在这里陪伴您',\n        interaction: '点击与我交流',\n        hidden: ''\n      };\n      return hints[currentMode.value] || '点击与我互动';\n    });\n    const quickActions = computed(() => {\n      if (!props.showQuickActions) return [];\n      return [{\n        id: 'voice',\n        label: '语音交互',\n        icon: 'fas fa-microphone'\n      }, {\n        id: 'settings',\n        label: '设置',\n        icon: 'fas fa-cog'\n      }, {\n        id: 'help',\n        label: '帮助',\n        icon: 'fas fa-question-circle'\n      }];\n    });\n\n    // 方法\n    const handleAvatarClick = () => {\n      const clickData = {\n        mode: currentMode.value,\n        animationState: currentAnimationState.value,\n        timestamp: Date.now()\n      };\n      emit('avatar-click', clickData);\n\n      // 触发交互动画\n      if (currentAnimationState.value === 'idle') {\n        vpaStore.setAnimation('greeting');\n        setTimeout(() => {\n          vpaStore.setAnimation('idle');\n        }, 3000);\n      }\n\n      // 显示语音波形\n      showVoiceWave.value = true;\n      setTimeout(() => {\n        showVoiceWave.value = false;\n      }, 2000);\n    };\n    const handleQuickAction = action => {\n      emit('quick-action', action);\n\n      // 根据操作类型触发相应动画\n      switch (action.id) {\n        case 'voice':\n          vpaStore.setAnimation('listening');\n          showVoiceWave.value = true;\n          break;\n        case 'settings':\n          vpaStore.setAnimation('thinking');\n          break;\n        case 'help':\n          vpaStore.setAnimation('talking');\n          break;\n      }\n    };\n    const showInteractionHint = () => {\n      showHint.value = true;\n      hintTimer.value = setTimeout(() => {\n        showHint.value = false;\n      }, 3000);\n    };\n    const hideInteractionHint = () => {\n      showHint.value = false;\n      if (hintTimer.value) {\n        clearTimeout(hintTimer.value);\n        hintTimer.value = null;\n      }\n    };\n\n    // 生命周期\n    onMounted(() => {\n      console.log('VPA头像组件已加载');\n\n      // 定期显示交互提示\n      if (props.size !== 'small') {\n        setInterval(() => {\n          if (currentMode.value === 'companion' && !showHint.value) {\n            showInteractionHint();\n          }\n        }, 30000); // 30秒显示一次提示\n      }\n    });\n    onUnmounted(() => {\n      hideInteractionHint();\n    });\n    return {\n      currentMode,\n      currentAnimationState,\n      currentAnimationConfig,\n      currentAnimationResource,\n      currentHint,\n      quickActions,\n      showHint,\n      showVoiceWave,\n      handleAvatarClick,\n      handleQuickAction,\n      showInteractionHint,\n      hideInteractionHint\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "useVpaStore", "name", "props", "size", "type", "String", "default", "validator", "value", "includes", "position", "Object", "x", "y", "theme", "themeColors", "primary", "secondary", "background", "text", "showQuickActions", "Boolean", "emits", "setup", "emit", "vpaStore", "showHint", "showVoiceWave", "hintTimer", "currentMode", "currentAnimationState", "currentAnimationConfig", "currentAnimationResource", "animationConfig", "resource", "error", "console", "warn", "currentHint", "hints", "companion", "interaction", "hidden", "quickActions", "id", "label", "icon", "handleAvatarClick", "clickData", "mode", "animationState", "timestamp", "Date", "now", "setAnimation", "setTimeout", "handleQuickAction", "action", "showInteractionHint", "hideInteractionHint", "clearTimeout", "log", "setInterval"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\vpa\\VPAAvatarWidget.vue"], "sourcesContent": ["<template>\r\n  <div class=\"vpa-avatar-widget\" :class=\"[`size-${size}`, `mode-${currentMode}`]\">\r\n    <!-- VPA头像容器 -->\r\n    <div class=\"avatar-container\" @click=\"handleAvatarClick\">\r\n      <!-- 头像显示区域 -->\r\n      <div class=\"avatar-display\">\r\n        <div v-if=\"currentAnimationResource\" class=\"avatar-animation\">\r\n          <!-- 这里可以集成Live2D或其他动画资源 -->\r\n          <div class=\"animation-placeholder\" :class=\"currentAnimationState\">\r\n            <i class=\"fas fa-user-circle avatar-icon\"></i>\r\n          </div>\r\n        </div>\r\n        \r\n        <div v-else class=\"avatar-fallback\">\r\n          <i class=\"fas fa-user-circle avatar-icon\"></i>\r\n        </div>\r\n        \r\n        <!-- 状态指示器 -->\r\n        <div class=\"status-indicator\" :class=\"currentAnimationState\">\r\n          <div class=\"status-dot\"></div>\r\n        </div>\r\n        \r\n        <!-- 语音波形动画 -->\r\n        <div v-if=\"showVoiceWave\" class=\"voice-wave\">\r\n          <div class=\"wave\" v-for=\"i in 4\" :key=\"i\" :style=\"{ animationDelay: `${i * 0.1}s` }\"></div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 交互提示 -->\r\n      <div v-if=\"showHint\" class=\"interaction-hint\">\r\n        <span>{{ currentHint }}</span>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 快速操作按钮 -->\r\n    <div v-if=\"size !== 'small'\" class=\"quick-actions\">\r\n      <button \r\n        v-for=\"action in quickActions\" \r\n        :key=\"action.id\"\r\n        @click=\"handleQuickAction(action)\"\r\n        class=\"action-btn\"\r\n        :title=\"action.label\"\r\n      >\r\n        <i :class=\"action.icon\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport { useVpaStore } from '@/store/modules/vpa'\r\n\r\nexport default {\r\n  name: 'VPAAvatarWidget',\r\n  props: {\r\n    size: {\r\n      type: String,\r\n      default: 'medium',\r\n      validator: value => ['small', 'medium', 'large'].includes(value)\r\n    },\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 1 })\r\n    },\r\n    theme: {\r\n      type: String,\r\n      default: 'glass'\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({\r\n        primary: '#667eea',\r\n        secondary: '#764ba2',\r\n        background: 'rgba(102, 126, 234, 0.1)',\r\n        text: '#ffffff'\r\n      })\r\n    },\r\n    showQuickActions: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  emits: ['avatar-click', 'mode-changed', 'animation-changed', 'quick-action'],\r\n  \r\n  setup(props, { emit }) {\r\n    const vpaStore = useVpaStore()\r\n    \r\n    // 响应式状态\r\n    const showHint = ref(false)\r\n    const showVoiceWave = ref(false)\r\n    const hintTimer = ref(null)\r\n    \r\n    // 计算属性\r\n    const currentMode = computed(() => vpaStore.currentMode)\r\n    const currentAnimationState = computed(() => vpaStore.currentAnimationState)\r\n    const currentAnimationConfig = computed(() => vpaStore.currentAnimationConfig)\r\n    \r\n    const currentAnimationResource = computed(() => {\r\n      const animationConfig = vpaStore.currentAnimationConfig\r\n      if (animationConfig && animationConfig.resource) {\r\n        try {\r\n          // 直接返回资源路径，让img标签处理\r\n          return `/src/assets/${animationConfig.resource}`\r\n        } catch (error) {\r\n          console.warn('动画资源加载失败:', error)\r\n          return null\r\n        }\r\n      }\r\n      return null\r\n    })\r\n    \r\n    const currentHint = computed(() => {\r\n      const hints = {\r\n        companion: '我在这里陪伴您',\r\n        interaction: '点击与我交流',\r\n        hidden: ''\r\n      }\r\n      return hints[currentMode.value] || '点击与我互动'\r\n    })\r\n    \r\n    const quickActions = computed(() => {\r\n      if (!props.showQuickActions) return []\r\n      \r\n      return [\r\n        {\r\n          id: 'voice',\r\n          label: '语音交互',\r\n          icon: 'fas fa-microphone'\r\n        },\r\n        {\r\n          id: 'settings',\r\n          label: '设置',\r\n          icon: 'fas fa-cog'\r\n        },\r\n        {\r\n          id: 'help',\r\n          label: '帮助',\r\n          icon: 'fas fa-question-circle'\r\n        }\r\n      ]\r\n    })\r\n    \r\n    // 方法\r\n    const handleAvatarClick = () => {\r\n      const clickData = {\r\n        mode: currentMode.value,\r\n        animationState: currentAnimationState.value,\r\n        timestamp: Date.now()\r\n      }\r\n      \r\n      emit('avatar-click', clickData)\r\n      \r\n      // 触发交互动画\r\n      if (currentAnimationState.value === 'idle') {\r\n        vpaStore.setAnimation('greeting')\r\n        setTimeout(() => {\r\n          vpaStore.setAnimation('idle')\r\n        }, 3000)\r\n      }\r\n      \r\n      // 显示语音波形\r\n      showVoiceWave.value = true\r\n      setTimeout(() => {\r\n        showVoiceWave.value = false\r\n      }, 2000)\r\n    }\r\n    \r\n    const handleQuickAction = (action) => {\r\n      emit('quick-action', action)\r\n      \r\n      // 根据操作类型触发相应动画\r\n      switch (action.id) {\r\n        case 'voice':\r\n          vpaStore.setAnimation('listening')\r\n          showVoiceWave.value = true\r\n          break\r\n        case 'settings':\r\n          vpaStore.setAnimation('thinking')\r\n          break\r\n        case 'help':\r\n          vpaStore.setAnimation('talking')\r\n          break\r\n      }\r\n    }\r\n    \r\n    const showInteractionHint = () => {\r\n      showHint.value = true\r\n      hintTimer.value = setTimeout(() => {\r\n        showHint.value = false\r\n      }, 3000)\r\n    }\r\n    \r\n    const hideInteractionHint = () => {\r\n      showHint.value = false\r\n      if (hintTimer.value) {\r\n        clearTimeout(hintTimer.value)\r\n        hintTimer.value = null\r\n      }\r\n    }\r\n    \r\n    // 生命周期\r\n    onMounted(() => {\r\n      console.log('VPA头像组件已加载')\r\n      \r\n      // 定期显示交互提示\r\n      if (props.size !== 'small') {\r\n        setInterval(() => {\r\n          if (currentMode.value === 'companion' && !showHint.value) {\r\n            showInteractionHint()\r\n          }\r\n        }, 30000) // 30秒显示一次提示\r\n      }\r\n    })\r\n    \r\n    onUnmounted(() => {\r\n      hideInteractionHint()\r\n    })\r\n    \r\n    return {\r\n      currentMode,\r\n      currentAnimationState,\r\n      currentAnimationConfig,\r\n      currentAnimationResource,\r\n      currentHint,\r\n      quickActions,\r\n      showHint,\r\n      showVoiceWave,\r\n      handleAvatarClick,\r\n      handleQuickAction,\r\n      showInteractionHint,\r\n      hideInteractionHint\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.vpa-avatar-widget {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 10px;\r\n  padding: 10px;\r\n  border-radius: 15px;\r\n  background: var(--card-background, rgba(255, 255, 255, 0.1));\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.vpa-avatar-widget:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 尺寸变体 */\r\n.size-small {\r\n  width: 80px;\r\n  height: 80px;\r\n  padding: 8px;\r\n}\r\n\r\n.size-medium {\r\n  width: 120px;\r\n  height: 140px;\r\n  padding: 10px;\r\n}\r\n\r\n.size-large {\r\n  width: 160px;\r\n  height: 200px;\r\n  padding: 15px;\r\n}\r\n\r\n/* 头像容器 */\r\n.avatar-container {\r\n  position: relative;\r\n  cursor: pointer;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.avatar-container:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.avatar-display {\r\n  position: relative;\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, var(--card-primary-color, #667eea) 0%, var(--card-secondary-color, #764ba2) 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.size-small .avatar-display {\r\n  width: 50px;\r\n  height: 50px;\r\n}\r\n\r\n.size-large .avatar-display {\r\n  width: 80px;\r\n  height: 80px;\r\n}\r\n\r\n.avatar-icon {\r\n  font-size: 40px;\r\n  color: white;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.size-small .avatar-icon {\r\n  font-size: 30px;\r\n}\r\n\r\n.size-large .avatar-icon {\r\n  font-size: 50px;\r\n}\r\n\r\n/* 动画状态 */\r\n.animation-placeholder.idle .avatar-icon {\r\n  animation: breathe 3s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.talking .avatar-icon {\r\n  animation: talking 0.5s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.listening .avatar-icon {\r\n  animation: pulse 1s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.thinking .avatar-icon {\r\n  animation: thinking 2s ease-in-out infinite;\r\n}\r\n\r\n.animation-placeholder.greeting .avatar-icon {\r\n  animation: bounce 0.6s ease-in-out;\r\n}\r\n\r\n/* 状态指示器 */\r\n.status-indicator {\r\n  position: absolute;\r\n  bottom: 5px;\r\n  right: 5px;\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  border: 2px solid white;\r\n}\r\n\r\n.status-dot {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  background: #4CAF50;\r\n  animation: statusPulse 2s ease-in-out infinite;\r\n}\r\n\r\n.status-indicator.talking .status-dot {\r\n  background: #2196F3;\r\n  animation: statusBlink 0.5s ease-in-out infinite;\r\n}\r\n\r\n.status-indicator.listening .status-dot {\r\n  background: #FF9800;\r\n  animation: statusPulse 1s ease-in-out infinite;\r\n}\r\n\r\n.status-indicator.thinking .status-dot {\r\n  background: #9C27B0;\r\n  animation: statusRotate 2s linear infinite;\r\n}\r\n\r\n/* 语音波形 */\r\n.voice-wave {\r\n  position: absolute;\r\n  bottom: -10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  display: flex;\r\n  gap: 2px;\r\n  align-items: flex-end;\r\n}\r\n\r\n.wave {\r\n  width: 3px;\r\n  height: 8px;\r\n  background: var(--card-primary-color, #667eea);\r\n  border-radius: 2px;\r\n  animation: wave 1s infinite ease-in-out;\r\n}\r\n\r\n/* 交互提示 */\r\n.interaction-hint {\r\n  position: absolute;\r\n  top: -35px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 5px 10px;\r\n  border-radius: 15px;\r\n  font-size: 11px;\r\n  white-space: nowrap;\r\n  opacity: 0;\r\n  animation: hintFadeIn 0.3s ease forwards;\r\n}\r\n\r\n.interaction-hint::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  border: 5px solid transparent;\r\n  border-top-color: rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n/* 快速操作按钮 */\r\n.quick-actions {\r\n  display: flex;\r\n  gap: 5px;\r\n  margin-top: 5px;\r\n}\r\n\r\n.action-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  border: none;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: var(--card-primary-color, #667eea);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 动画定义 */\r\n@keyframes breathe {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n@keyframes talking {\r\n  0%, 100% { transform: scaleY(1); }\r\n  50% { transform: scaleY(1.1); }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%, 100% { transform: rotate(0deg); }\r\n  50% { transform: rotate(5deg); }\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-5px); }\r\n}\r\n\r\n@keyframes statusPulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes statusBlink {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0; }\r\n}\r\n\r\n@keyframes statusRotate {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes hintFadeIn {\r\n  0% { opacity: 0; transform: translateX(-50%) translateY(-5px); }\r\n  100% { opacity: 1; transform: translateX(-50%) translateY(0); }\r\n}\r\n\r\n@keyframes wave {\r\n  0%, 100% { height: 8px; }\r\n  50% { height: 16px; }\r\n}\r\n</style>\r\n"], "mappings": "AAkDA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,SAASC,WAAU,QAAS,qBAAoB;AAEhD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,SAAS,EAAEC,KAAI,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACjE,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QAAEM,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLV,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDS,WAAW,EAAE;MACXX,IAAI,EAAEO,MAAM;MACZL,OAAO,EAAEA,CAAA,MAAO;QACdU,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,0BAA0B;QACtCC,IAAI,EAAE;MACR,CAAC;IACH,CAAC;IACDC,gBAAgB,EAAE;MAChBhB,IAAI,EAAEiB,OAAO;MACbf,OAAO,EAAE;IACX;EACF,CAAC;EAEDgB,KAAK,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,mBAAmB,EAAE,cAAc,CAAC;EAE5EC,KAAKA,CAACrB,KAAK,EAAE;IAAEsB;EAAK,CAAC,EAAE;IACrB,MAAMC,QAAO,GAAIzB,WAAW,CAAC;;IAE7B;IACA,MAAM0B,QAAO,GAAI9B,GAAG,CAAC,KAAK;IAC1B,MAAM+B,aAAY,GAAI/B,GAAG,CAAC,KAAK;IAC/B,MAAMgC,SAAQ,GAAIhC,GAAG,CAAC,IAAI;;IAE1B;IACA,MAAMiC,WAAU,GAAIhC,QAAQ,CAAC,MAAM4B,QAAQ,CAACI,WAAW;IACvD,MAAMC,qBAAoB,GAAIjC,QAAQ,CAAC,MAAM4B,QAAQ,CAACK,qBAAqB;IAC3E,MAAMC,sBAAqB,GAAIlC,QAAQ,CAAC,MAAM4B,QAAQ,CAACM,sBAAsB;IAE7E,MAAMC,wBAAuB,GAAInC,QAAQ,CAAC,MAAM;MAC9C,MAAMoC,eAAc,GAAIR,QAAQ,CAACM,sBAAqB;MACtD,IAAIE,eAAc,IAAKA,eAAe,CAACC,QAAQ,EAAE;QAC/C,IAAI;UACF;UACA,OAAO,eAAeD,eAAe,CAACC,QAAQ,EAAC;QACjD,EAAE,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,WAAW,EAAEF,KAAK;UAC/B,OAAO,IAAG;QACZ;MACF;MACA,OAAO,IAAG;IACZ,CAAC;IAED,MAAMG,WAAU,GAAIzC,QAAQ,CAAC,MAAM;MACjC,MAAM0C,KAAI,GAAI;QACZC,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,QAAQ;QACrBC,MAAM,EAAE;MACV;MACA,OAAOH,KAAK,CAACV,WAAW,CAACrB,KAAK,KAAK,QAAO;IAC5C,CAAC;IAED,MAAMmC,YAAW,GAAI9C,QAAQ,CAAC,MAAM;MAClC,IAAI,CAACK,KAAK,CAACkB,gBAAgB,EAAE,OAAO,EAAC;MAErC,OAAO,CACL;QACEwB,EAAE,EAAE,OAAO;QACXC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,EAAE,EAAE,UAAU;QACdC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,EAAE,EAAE,MAAM;QACVC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE;MACR,EACF;IACF,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,MAAMC,SAAQ,GAAI;QAChBC,IAAI,EAAEpB,WAAW,CAACrB,KAAK;QACvB0C,cAAc,EAAEpB,qBAAqB,CAACtB,KAAK;QAC3C2C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACtB;MAEA7B,IAAI,CAAC,cAAc,EAAEwB,SAAS;;MAE9B;MACA,IAAIlB,qBAAqB,CAACtB,KAAI,KAAM,MAAM,EAAE;QAC1CiB,QAAQ,CAAC6B,YAAY,CAAC,UAAU;QAChCC,UAAU,CAAC,MAAM;UACf9B,QAAQ,CAAC6B,YAAY,CAAC,MAAM;QAC9B,CAAC,EAAE,IAAI;MACT;;MAEA;MACA3B,aAAa,CAACnB,KAAI,GAAI,IAAG;MACzB+C,UAAU,CAAC,MAAM;QACf5B,aAAa,CAACnB,KAAI,GAAI,KAAI;MAC5B,CAAC,EAAE,IAAI;IACT;IAEA,MAAMgD,iBAAgB,GAAKC,MAAM,IAAK;MACpCjC,IAAI,CAAC,cAAc,EAAEiC,MAAM;;MAE3B;MACA,QAAQA,MAAM,CAACb,EAAE;QACf,KAAK,OAAO;UACVnB,QAAQ,CAAC6B,YAAY,CAAC,WAAW;UACjC3B,aAAa,CAACnB,KAAI,GAAI,IAAG;UACzB;QACF,KAAK,UAAU;UACbiB,QAAQ,CAAC6B,YAAY,CAAC,UAAU;UAChC;QACF,KAAK,MAAM;UACT7B,QAAQ,CAAC6B,YAAY,CAAC,SAAS;UAC/B;MACJ;IACF;IAEA,MAAMI,mBAAkB,GAAIA,CAAA,KAAM;MAChChC,QAAQ,CAAClB,KAAI,GAAI,IAAG;MACpBoB,SAAS,CAACpB,KAAI,GAAI+C,UAAU,CAAC,MAAM;QACjC7B,QAAQ,CAAClB,KAAI,GAAI,KAAI;MACvB,CAAC,EAAE,IAAI;IACT;IAEA,MAAMmD,mBAAkB,GAAIA,CAAA,KAAM;MAChCjC,QAAQ,CAAClB,KAAI,GAAI,KAAI;MACrB,IAAIoB,SAAS,CAACpB,KAAK,EAAE;QACnBoD,YAAY,CAAChC,SAAS,CAACpB,KAAK;QAC5BoB,SAAS,CAACpB,KAAI,GAAI,IAAG;MACvB;IACF;;IAEA;IACAV,SAAS,CAAC,MAAM;MACdsC,OAAO,CAACyB,GAAG,CAAC,YAAY;;MAExB;MACA,IAAI3D,KAAK,CAACC,IAAG,KAAM,OAAO,EAAE;QAC1B2D,WAAW,CAAC,MAAM;UAChB,IAAIjC,WAAW,CAACrB,KAAI,KAAM,WAAU,IAAK,CAACkB,QAAQ,CAAClB,KAAK,EAAE;YACxDkD,mBAAmB,CAAC;UACtB;QACF,CAAC,EAAE,KAAK,GAAE;MACZ;IACF,CAAC;IAED3D,WAAW,CAAC,MAAM;MAChB4D,mBAAmB,CAAC;IACtB,CAAC;IAED,OAAO;MACL9B,WAAW;MACXC,qBAAqB;MACrBC,sBAAsB;MACtBC,wBAAwB;MACxBM,WAAW;MACXK,YAAY;MACZjB,QAAQ;MACRC,aAAa;MACboB,iBAAiB;MACjBS,iBAAiB;MACjBE,mBAAmB;MACnBC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}