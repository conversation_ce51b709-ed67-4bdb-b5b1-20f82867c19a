{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n// 场景感知上下文管理器\n// 专门为车载HMI系统设计的用户状态感知服务\nexport default class SceneContextManager {\n  constructor() {\n    this.context = this.initializeContext();\n    this.observers = [];\n    this.lastUpdate = Date.now();\n  }\n\n  /**\r\n   * 初始化上下文信息\r\n   */\n  initializeContext() {\n    return {\n      // 基础时间信息\n      timestamp: Date.now(),\n      timeOfDay: this.getCurrentTimeOfDay(),\n      isWeekend: this.isWeekend(),\n      season: this.getCurrentSeason(),\n      // 环境信息\n      weather: 'clear',\n      // 默认晴朗，可以通过API获取\n      temperature: 22,\n      // 默认温度\n\n      // 驾驶状态\n      drivingDuration: 0,\n      isDriving: false,\n      gear: 'P',\n      speed: 0,\n      // 用户信息\n      userRole: 'driver',\n      mood: 'neutral',\n      stressLevel: 0,\n      fatigueLevel: 0,\n      // 乘客信息\n      passengers: [],\n      passengerCount: 1,\n      // 行程信息\n      destination: '',\n      destinationType: '',\n      tripPurpose: 'unknown',\n      // 车辆状态\n      fuelLevel: 80,\n      batteryLevel: 85,\n      vehicleStatus: 'normal',\n      // 位置信息\n      locationType: 'city',\n      roadType: 'city',\n      // 历史信息\n      recentScenes: [],\n      sceneSwitchCount: 0,\n      // 个性化信息\n      userPreferences: {\n        musicStyle: 'balanced',\n        lightingPreference: 'auto',\n        voiceAssistantVolume: 70\n      }\n    };\n  }\n\n  /**\r\n   * 获取当前时间段描述\r\n   */\n  getCurrentTimeOfDay() {\n    const hour = new Date().getHours();\n    if (hour >= 5 && hour < 8) return 'earlyMorning';\n    if (hour >= 8 && hour < 12) return 'morning';\n    if (hour >= 12 && hour < 14) return 'noon';\n    if (hour >= 14 && hour < 17) return 'afternoon';\n    if (hour >= 17 && hour < 19) return 'evening';\n    if (hour >= 19 && hour < 22) return 'night';\n    return 'lateNight';\n  }\n\n  /**\r\n   * 判断是否为周末\r\n   */\n  isWeekend() {\n    const day = new Date().getDay();\n    return day === 0 || day === 6;\n  }\n\n  /**\r\n   * 获取当前季节\r\n   */\n  getCurrentSeason() {\n    const month = new Date().getMonth();\n    if (month >= 2 && month <= 4) return 'spring';\n    if (month >= 5 && month <= 7) return 'summer';\n    if (month >= 8 && month <= 10) return 'autumn';\n    return 'winter';\n  }\n\n  /**\r\n   * 更新上下文信息\r\n   */\n  updateContext(updates) {\n    const oldContext = {\n      ...this.context\n    };\n    this.context = {\n      ...this.context,\n      ...updates,\n      timestamp: Date.now()\n    };\n\n    // 触发观察者通知\n    this.notifyObservers(oldContext, this.context);\n\n    // 更新时间相关信息\n    this.updateTimeContext();\n    this.lastUpdate = Date.now();\n  }\n\n  /**\r\n   * 更新时间相关上下文\r\n   */\n  updateTimeContext() {\n    this.context.timeOfDay = this.getCurrentTimeOfDay();\n    this.context.isWeekend = this.isWeekend();\n    this.context.season = this.getCurrentSeason();\n  }\n\n  /**\r\n   * 推断用户情绪状态\r\n   */\n  inferUserMood() {\n    const {\n      timeOfDay,\n      isWeekend,\n      drivingDuration,\n      fatigueLevel,\n      stressLevel,\n      passengerCount\n    } = this.context;\n\n    // 基于时间推断情绪\n    let mood = 'neutral';\n\n    // 早高峰情绪推断\n    if (timeOfDay === 'earlyMorning' || timeOfDay === 'morning') {\n      if (!isWeekend) {\n        mood = passengerCount > 1 ? 'family_care' : 'focused_commute';\n      } else {\n        mood = 'relaxed_weekend';\n      }\n    }\n\n    // 下班时间情绪推断\n    else if (timeOfDay === 'evening') {\n      mood = 'relieved_tired';\n    }\n\n    // 深夜情绪推断\n    else if (timeOfDay === 'lateNight') {\n      mood = 'tired_quiet';\n    }\n\n    // 周末情绪推断\n    else if (isWeekend) {\n      mood = 'relaxed_happy';\n    }\n\n    // 基于驾驶时长调整情绪\n    if (drivingDuration > 7200000) {\n      // 超过2小时\n      mood = mood.includes('tired') ? mood : 'driving_fatigue';\n    }\n\n    // 基于疲劳程度调整\n    if (fatigueLevel > 7) {\n      mood = 'very_tired';\n    }\n\n    // 基于压力程度调整\n    if (stressLevel > 7) {\n      mood = 'stressed';\n    }\n    this.context.mood = mood;\n    return mood;\n  }\n\n  /**\r\n   * 推断行程目的\r\n   */\n  inferTripPurpose() {\n    const {\n      timeOfDay,\n      isWeekend,\n      destinationType,\n      passengers\n    } = this.context;\n    let purpose = 'unknown';\n\n    // 工作日早高峰\n    if (!isWeekend && (timeOfDay === 'earlyMorning' || timeOfDay === 'morning')) {\n      if (destinationType === 'work' || destinationType === 'office') {\n        purpose = 'commute_to_work';\n      } else if (passengers.includes('child')) {\n        purpose = 'take_child_to_school';\n      }\n    }\n\n    // 工作日晚高峰\n    else if (!isWeekend && timeOfDay === 'evening') {\n      if (destinationType === 'home') {\n        purpose = 'return_home';\n      }\n    }\n\n    // 周末\n    else if (isWeekend) {\n      if (destinationType === 'park' || destinationType === 'recreation') {\n        purpose = 'weekend_trip';\n      } else if (destinationType === 'restaurant') {\n        purpose = 'dining_out';\n      } else if (destinationType === 'shopping') {\n        purpose = 'shopping';\n      }\n    }\n    this.context.tripPurpose = purpose;\n    return purpose;\n  }\n\n  /**\r\n   * 获取情感化上下文描述\r\n   */\n  getEmotionalContext() {\n    const {\n      timeOfDay,\n      weather,\n      mood,\n      passengers,\n      destination,\n      season\n    } = this.context;\n    const timeEmotion = this.getTimeEmotion(timeOfDay);\n    const weatherEmotion = this.getWeatherEmotion(weather);\n    const moodEmotion = this.getMoodEmotion(mood);\n    const passengerEmotion = this.getPassengerEmotion(passengers);\n    return {\n      ...timeEmotion,\n      ...weatherEmotion,\n      ...moodEmotion,\n      ...passengerEmotion,\n      season,\n      destination,\n      userRole: this.getUserRole(passengers)\n    };\n  }\n\n  /**\r\n   * 获取时间情感描述\r\n   */\n  getTimeEmotion(timeOfDay) {\n    const emotions = {\n      earlyMorning: {\n        timeOfDay: '清晨',\n        mood: '清新宁静',\n        lighting: '柔和的晨光',\n        atmosphere: '宁静祥和',\n        userFeeling: '带着期待开始新的一天'\n      },\n      morning: {\n        timeOfDay: '上午',\n        mood: '充满活力',\n        lighting: '明亮的日光',\n        atmosphere: '活跃向上',\n        userFeeling: '精力充沛，专注当前任务'\n      },\n      noon: {\n        timeOfDay: '中午',\n        mood: '略带疲惫',\n        lighting: '强烈的阳光',\n        atmosphere: '热情充沛',\n        userFeeling: '需要短暂的休息和调整'\n      },\n      afternoon: {\n        timeOfDay: '下午',\n        mood: '专注沉稳',\n        lighting: '倾斜的阳光',\n        atmosphere: '沉稳专注',\n        userFeeling: '保持专注，可能有些疲劳'\n      },\n      evening: {\n        timeOfDay: '傍晚',\n        mood: '期待放松',\n        lighting: '温暖的夕阳',\n        atmosphere: '温暖归家',\n        userFeeling: '期待着回到温暖的家'\n      },\n      night: {\n        timeOfDay: '夜晚',\n        mood: '宁静温馨',\n        lighting: '温暖的灯光',\n        atmosphere: '温馨舒适',\n        userFeeling: '享受夜晚的宁静时光'\n      },\n      lateNight: {\n        timeOfDay: '深夜',\n        mood: '宁静安详',\n        lighting: '柔和的月光',\n        atmosphere: '宁静神秘',\n        userFeeling: '准备休息，享受宁静'\n      }\n    };\n    return emotions[timeOfDay] || emotions.morning;\n  }\n\n  /**\r\n   * 获取天气情感描述\r\n   */\n  getWeatherEmotion(weather) {\n    const emotions = {\n      clear: {\n        weather: '晴朗',\n        mood: '愉悦开朗',\n        lighting: '明媚的阳光',\n        atmosphere: '明亮开朗',\n        visualElements: '蓝天白云，阳光透过车窗'\n      },\n      cloudy: {\n        weather: '多云',\n        mood: '平静舒缓',\n        lighting: '柔和的光线',\n        atmosphere: '柔和舒适',\n        visualElements: '云层柔和，光线散射'\n      },\n      rainy: {\n        weather: '下雨',\n        mood: '宁静沉思',\n        lighting: '雨滴的折射光',\n        atmosphere: '清新宁静',\n        visualElements: '雨滴在车窗上滑落，湿润的光线'\n      },\n      night: {\n        weather: '夜晚',\n        mood: '神秘安静',\n        lighting: '温暖的灯光',\n        atmosphere: '宁静神秘',\n        visualElements: '温暖的灯光，夜色中的车流'\n      }\n    };\n    return emotions[weather] || emotions.clear;\n  }\n\n  /**\r\n   * 获取情绪情感描述\r\n   */\n  getMoodEmotion(mood) {\n    const emotions = {\n      neutral: {\n        mood: '平静',\n        emotionalState: '心态平和',\n        psychologicalNeeds: '稳定舒适的环境',\n        colorPreference: '中性色调'\n      },\n      focused_commute: {\n        mood: '专注通勤',\n        emotionalState: '目标导向，略带紧迫',\n        psychologicalNeeds: '高效清晰的信息展示',\n        colorPreference: '清爽的蓝色调'\n      },\n      family_care: {\n        mood: '家庭关爱',\n        emotionalState: '温馨负责',\n        psychologicalNeeds: '安全舒适的氛围',\n        colorPreference: '温暖的橙黄色调'\n      },\n      relieved_tired: {\n        mood: '疲惫释然',\n        emotionalState: '放松但有些疲劳',\n        psychologicalNeeds: '舒缓放松的环境',\n        colorPreference: '柔和的暖色调'\n      },\n      relaxed_weekend: {\n        mood: '周末放松',\n        emotionalState: '轻松愉悦',\n        psychologicalNeeds: '轻松愉快的氛围',\n        colorPreference: '明亮的色彩'\n      },\n      driving_fatigue: {\n        mood: '驾驶疲劳',\n        emotionalState: '需要休息',\n        psychologicalNeeds: '提神醒脑的环境',\n        colorPreference: '清新的绿色调'\n      },\n      stressed: {\n        mood: '压力较大',\n        emotionalState: '紧张焦虑',\n        psychologicalNeeds: '舒缓压力的环境',\n        colorPreference: '平静的蓝色调'\n      },\n      very_tired: {\n        mood: '非常疲劳',\n        emotionalState: '极度需要休息',\n        psychologicalNeeds: '极度舒适的环境',\n        colorPreference: '柔和的深色调'\n      }\n    };\n    return emotions[mood] || emotions.neutral;\n  }\n\n  /**\r\n   * 获取乘客情感描述\r\n   */\n  getPassengerEmotion(passengers) {\n    const passengerCount = passengers.length;\n    if (passengerCount === 0) {\n      return {\n        passengerSituation: '独自驾驶',\n        socialAtmosphere: '安静私密',\n        interactionNeeds: '个人空间',\n        emotionalTone: '独立自主'\n      };\n    } else if (passengers.includes('child')) {\n      return {\n        passengerSituation: '与孩子同行',\n        socialAtmosphere: '温馨活泼',\n        interactionNeeds: '家庭互动',\n        emotionalTone: '关爱负责'\n      };\n    } else if (passengers.includes('spouse')) {\n      return {\n        passengerSituation: '与伴侣同行',\n        socialAtmosphere: '亲密温馨',\n        interactionNeeds: '二人世界',\n        emotionalTone: '浪漫亲密'\n      };\n    } else if (passengers.includes('friend')) {\n      return {\n        passengerSituation: '与朋友同行',\n        socialAtmosphere: '轻松愉快',\n        interactionNeeds: '朋友交流',\n        emotionalTone: '轻松友好'\n      };\n    } else if (passengerCount > 2) {\n      return {\n        passengerSituation: '多人同行',\n        socialAtmosphere: '热闹活跃',\n        interactionNeeds: '群体互动',\n        emotionalTone: '热闹欢快'\n      };\n    } else {\n      return {\n        passengerSituation: '有乘客同行',\n        socialAtmosphere: '舒适友好',\n        interactionNeeds: '正常交流',\n        emotionalTone: '平和友好'\n      };\n    }\n  }\n\n  /**\r\n   * 获取用户角色\r\n   */\n  getUserRole(passengers) {\n    if (passengers.length === 0) return '独自驾驶';\n    if (passengers.includes('child')) return '家长';\n    if (passengers.includes('spouse')) return '伴侣';\n    if (passengers.includes('friend')) return '朋友';\n    return '驾驶者';\n  }\n\n  /**\r\n   * 添加观察者\r\n   */\n  addObserver(callback) {\n    this.observers.push(callback);\n  }\n\n  /**\r\n   * 移除观察者\r\n   */\n  removeObserver(callback) {\n    const index = this.observers.indexOf(callback);\n    if (index > -1) {\n      this.observers.splice(index, 1);\n    }\n  }\n\n  /**\r\n   * 通知观察者\r\n   */\n  notifyObservers(oldContext, newContext) {\n    this.observers.forEach(callback => {\n      try {\n        callback(oldContext, newContext);\n      } catch (error) {\n        console.error('Context observer notification failed:', error);\n      }\n    });\n  }\n\n  /**\r\n   * 获取用于提示词生成的上下文\r\n   */\n  getPromptGenerationContext() {\n    this.inferUserMood();\n    this.inferTripPurpose();\n    return {\n      timeOfDay: this.context.timeOfDay,\n      weather: this.context.weather,\n      mood: this.context.mood,\n      userRole: this.getUserRole(this.context.passengers),\n      passengers: this.context.passengers.join(', ') || '独自',\n      destination: this.context.destination,\n      isWeekend: this.context.isWeekend,\n      drivingDuration: this.formatDuration(this.context.drivingDuration),\n      season: this.context.season,\n      tripPurpose: this.context.tripPurpose\n    };\n  }\n\n  /**\r\n   * 格式化时长\r\n   */\n  formatDuration(milliseconds) {\n    const minutes = Math.floor(milliseconds / 60000);\n    if (minutes < 60) {\n      return `${minutes}分钟`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const remainingMinutes = minutes % 60;\n      return `${hours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`;\n    }\n  }\n\n  /**\r\n   * 模拟驾驶状态更新\r\n   */\n  simulateDrivingUpdate() {\n    if (this.context.isDriving) {\n      this.updateContext({\n        drivingDuration: this.context.drivingDuration + 60000,\n        // 增加1分钟\n        fatigueLevel: Math.min(10, this.context.fatigueLevel + 0.1)\n      });\n    }\n  }\n\n  /**\r\n   * 获取完整上下文信息\r\n   */\n  getContext() {\n    return {\n      ...this.context\n    };\n  }\n\n  /**\r\n   * 重置上下文\r\n   */\n  reset() {\n    this.context = this.initializeContext();\n    this.lastUpdate = Date.now();\n  }\n\n  /**\r\n   * 获取上下文统计信息\r\n   */\n  getStatistics() {\n    return {\n      lastUpdate: this.lastUpdate,\n      sceneSwitchCount: this.context.sceneSwitchCount,\n      drivingDuration: this.formatDuration(this.context.drivingDuration),\n      currentMood: this.context.mood,\n      tripPurpose: this.context.tripPurpose,\n      observerCount: this.observers.length\n    };\n  }\n}", "map": {"version": 3, "names": ["SceneContextManager", "constructor", "context", "initializeContext", "observers", "lastUpdate", "Date", "now", "timestamp", "timeOfDay", "getCurrentTimeOfDay", "isWeekend", "season", "getCurrentSeason", "weather", "temperature", "drivingDuration", "isDriving", "gear", "speed", "userRole", "mood", "stressLevel", "fatigueLevel", "passengers", "passengerCount", "destination", "destinationType", "tripPurpose", "fuelLevel", "batteryLevel", "vehicleStatus", "locationType", "roadType", "recentScenes", "sceneSwitchCount", "userPreferences", "musicStyle", "lightingPreference", "voiceAssistantVolume", "hour", "getHours", "day", "getDay", "month", "getMonth", "updateContext", "updates", "oldContext", "notifyObservers", "updateTimeContext", "inferUserMood", "includes", "inferTripPurpose", "purpose", "getEmotionalContext", "timeEmotion", "getTimeEmotion", "weatherEmotion", "getWeatherEmotion", "moodEmotion", "getMoodEmotion", "passengerEmotion", "getPassengerEmotion", "getUserRole", "emotions", "earlyMorning", "lighting", "atmosphere", "userFeeling", "morning", "noon", "afternoon", "evening", "night", "lateNight", "clear", "visualElements", "cloudy", "rainy", "neutral", "emotionalState", "psychologicalNeeds", "colorPreference", "focused_commute", "family_care", "relieved_tired", "relaxed_weekend", "driving_fatigue", "stressed", "very_tired", "length", "passengerSituation", "socialAtmosphere", "interactionNeeds", "emotionalTone", "addObserver", "callback", "push", "removeObserver", "index", "indexOf", "splice", "newContext", "for<PERSON>ach", "error", "console", "getPromptGenerationContext", "join", "formatDuration", "milliseconds", "minutes", "Math", "floor", "hours", "remainingMinutes", "simulateDrivingUpdate", "min", "getContext", "reset", "getStatistics", "current<PERSON><PERSON>", "observerCount"], "sources": ["D:/code/pythonWork/theme/ai-hmi/src/services/SceneContextManager.js"], "sourcesContent": ["// 场景感知上下文管理器\r\n// 专门为车载HMI系统设计的用户状态感知服务\r\nexport default class SceneContextManager {\r\n  constructor() {\r\n    this.context = this.initializeContext()\r\n    this.observers = []\r\n    this.lastUpdate = Date.now()\r\n  }\r\n\r\n  /**\r\n   * 初始化上下文信息\r\n   */\r\n  initializeContext() {\r\n    return {\r\n      // 基础时间信息\r\n      timestamp: Date.now(),\r\n      timeOfDay: this.getCurrentTimeOfDay(),\r\n      isWeekend: this.isWeekend(),\r\n      season: this.getCurrentSeason(),\r\n      \r\n      // 环境信息\r\n      weather: 'clear', // 默认晴朗，可以通过API获取\r\n      temperature: 22, // 默认温度\r\n      \r\n      // 驾驶状态\r\n      drivingDuration: 0,\r\n      isDriving: false,\r\n      gear: 'P',\r\n      speed: 0,\r\n      \r\n      // 用户信息\r\n      userRole: 'driver',\r\n      mood: 'neutral',\r\n      stressLevel: 0,\r\n      fatigueLevel: 0,\r\n      \r\n      // 乘客信息\r\n      passengers: [],\r\n      passengerCount: 1,\r\n      \r\n      // 行程信息\r\n      destination: '',\r\n      destinationType: '',\r\n      tripPurpose: 'unknown',\r\n      \r\n      // 车辆状态\r\n      fuelLevel: 80,\r\n      batteryLevel: 85,\r\n      vehicleStatus: 'normal',\r\n      \r\n      // 位置信息\r\n      locationType: 'city',\r\n      roadType: 'city',\r\n      \r\n      // 历史信息\r\n      recentScenes: [],\r\n      sceneSwitchCount: 0,\r\n      \r\n      // 个性化信息\r\n      userPreferences: {\r\n        musicStyle: 'balanced',\r\n        lightingPreference: 'auto',\r\n        voiceAssistantVolume: 70\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取当前时间段描述\r\n   */\r\n  getCurrentTimeOfDay() {\r\n    const hour = new Date().getHours()\r\n    \r\n    if (hour >= 5 && hour < 8) return 'earlyMorning'\r\n    if (hour >= 8 && hour < 12) return 'morning'\r\n    if (hour >= 12 && hour < 14) return 'noon'\r\n    if (hour >= 14 && hour < 17) return 'afternoon'\r\n    if (hour >= 17 && hour < 19) return 'evening'\r\n    if (hour >= 19 && hour < 22) return 'night'\r\n    return 'lateNight'\r\n  }\r\n\r\n  /**\r\n   * 判断是否为周末\r\n   */\r\n  isWeekend() {\r\n    const day = new Date().getDay()\r\n    return day === 0 || day === 6\r\n  }\r\n\r\n  /**\r\n   * 获取当前季节\r\n   */\r\n  getCurrentSeason() {\r\n    const month = new Date().getMonth()\r\n    \r\n    if (month >= 2 && month <= 4) return 'spring'\r\n    if (month >= 5 && month <= 7) return 'summer'\r\n    if (month >= 8 && month <= 10) return 'autumn'\r\n    return 'winter'\r\n  }\r\n\r\n  /**\r\n   * 更新上下文信息\r\n   */\r\n  updateContext(updates) {\r\n    const oldContext = { ...this.context }\r\n    this.context = { ...this.context, ...updates, timestamp: Date.now() }\r\n    \r\n    // 触发观察者通知\r\n    this.notifyObservers(oldContext, this.context)\r\n    \r\n    // 更新时间相关信息\r\n    this.updateTimeContext()\r\n    \r\n    this.lastUpdate = Date.now()\r\n  }\r\n\r\n  /**\r\n   * 更新时间相关上下文\r\n   */\r\n  updateTimeContext() {\r\n    this.context.timeOfDay = this.getCurrentTimeOfDay()\r\n    this.context.isWeekend = this.isWeekend()\r\n    this.context.season = this.getCurrentSeason()\r\n  }\r\n\r\n  /**\r\n   * 推断用户情绪状态\r\n   */\r\n  inferUserMood() {\r\n    const { timeOfDay, isWeekend, drivingDuration, fatigueLevel, stressLevel, passengerCount } = this.context\r\n    \r\n    // 基于时间推断情绪\r\n    let mood = 'neutral'\r\n    \r\n    // 早高峰情绪推断\r\n    if (timeOfDay === 'earlyMorning' || timeOfDay === 'morning') {\r\n      if (!isWeekend) {\r\n        mood = passengerCount > 1 ? 'family_care' : 'focused_commute'\r\n      } else {\r\n        mood = 'relaxed_weekend'\r\n      }\r\n    }\r\n    \r\n    // 下班时间情绪推断\r\n    else if (timeOfDay === 'evening') {\r\n      mood = 'relieved_tired'\r\n    }\r\n    \r\n    // 深夜情绪推断\r\n    else if (timeOfDay === 'lateNight') {\r\n      mood = 'tired_quiet'\r\n    }\r\n    \r\n    // 周末情绪推断\r\n    else if (isWeekend) {\r\n      mood = 'relaxed_happy'\r\n    }\r\n    \r\n    // 基于驾驶时长调整情绪\r\n    if (drivingDuration > 7200000) { // 超过2小时\r\n      mood = mood.includes('tired') ? mood : 'driving_fatigue'\r\n    }\r\n    \r\n    // 基于疲劳程度调整\r\n    if (fatigueLevel > 7) {\r\n      mood = 'very_tired'\r\n    }\r\n    \r\n    // 基于压力程度调整\r\n    if (stressLevel > 7) {\r\n      mood = 'stressed'\r\n    }\r\n    \r\n    this.context.mood = mood\r\n    return mood\r\n  }\r\n\r\n  /**\r\n   * 推断行程目的\r\n   */\r\n  inferTripPurpose() {\r\n    const { timeOfDay, isWeekend, destinationType, passengers } = this.context\r\n    \r\n    let purpose = 'unknown'\r\n    \r\n    // 工作日早高峰\r\n    if (!isWeekend && (timeOfDay === 'earlyMorning' || timeOfDay === 'morning')) {\r\n      if (destinationType === 'work' || destinationType === 'office') {\r\n        purpose = 'commute_to_work'\r\n      } else if (passengers.includes('child')) {\r\n        purpose = 'take_child_to_school'\r\n      }\r\n    }\r\n    \r\n    // 工作日晚高峰\r\n    else if (!isWeekend && timeOfDay === 'evening') {\r\n      if (destinationType === 'home') {\r\n        purpose = 'return_home'\r\n      }\r\n    }\r\n    \r\n    // 周末\r\n    else if (isWeekend) {\r\n      if (destinationType === 'park' || destinationType === 'recreation') {\r\n        purpose = 'weekend_trip'\r\n      } else if (destinationType === 'restaurant') {\r\n        purpose = 'dining_out'\r\n      } else if (destinationType === 'shopping') {\r\n        purpose = 'shopping'\r\n      }\r\n    }\r\n    \r\n    this.context.tripPurpose = purpose\r\n    return purpose\r\n  }\r\n\r\n  /**\r\n   * 获取情感化上下文描述\r\n   */\r\n  getEmotionalContext() {\r\n    const { timeOfDay, weather, mood, passengers, destination, season } = this.context\r\n    \r\n    const timeEmotion = this.getTimeEmotion(timeOfDay)\r\n    const weatherEmotion = this.getWeatherEmotion(weather)\r\n    const moodEmotion = this.getMoodEmotion(mood)\r\n    const passengerEmotion = this.getPassengerEmotion(passengers)\r\n    \r\n    return {\r\n      ...timeEmotion,\r\n      ...weatherEmotion,\r\n      ...moodEmotion,\r\n      ...passengerEmotion,\r\n      season,\r\n      destination,\r\n      userRole: this.getUserRole(passengers)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取时间情感描述\r\n   */\r\n  getTimeEmotion(timeOfDay) {\r\n    const emotions = {\r\n      earlyMorning: {\r\n        timeOfDay: '清晨',\r\n        mood: '清新宁静',\r\n        lighting: '柔和的晨光',\r\n        atmosphere: '宁静祥和',\r\n        userFeeling: '带着期待开始新的一天'\r\n      },\r\n      morning: {\r\n        timeOfDay: '上午',\r\n        mood: '充满活力',\r\n        lighting: '明亮的日光',\r\n        atmosphere: '活跃向上',\r\n        userFeeling: '精力充沛，专注当前任务'\r\n      },\r\n      noon: {\r\n        timeOfDay: '中午',\r\n        mood: '略带疲惫',\r\n        lighting: '强烈的阳光',\r\n        atmosphere: '热情充沛',\r\n        userFeeling: '需要短暂的休息和调整'\r\n      },\r\n      afternoon: {\r\n        timeOfDay: '下午',\r\n        mood: '专注沉稳',\r\n        lighting: '倾斜的阳光',\r\n        atmosphere: '沉稳专注',\r\n        userFeeling: '保持专注，可能有些疲劳'\r\n      },\r\n      evening: {\r\n        timeOfDay: '傍晚',\r\n        mood: '期待放松',\r\n        lighting: '温暖的夕阳',\r\n        atmosphere: '温暖归家',\r\n        userFeeling: '期待着回到温暖的家'\r\n      },\r\n      night: {\r\n        timeOfDay: '夜晚',\r\n        mood: '宁静温馨',\r\n        lighting: '温暖的灯光',\r\n        atmosphere: '温馨舒适',\r\n        userFeeling: '享受夜晚的宁静时光'\r\n      },\r\n      lateNight: {\r\n        timeOfDay: '深夜',\r\n        mood: '宁静安详',\r\n        lighting: '柔和的月光',\r\n        atmosphere: '宁静神秘',\r\n        userFeeling: '准备休息，享受宁静'\r\n      }\r\n    }\r\n    \r\n    return emotions[timeOfDay] || emotions.morning\r\n  }\r\n\r\n  /**\r\n   * 获取天气情感描述\r\n   */\r\n  getWeatherEmotion(weather) {\r\n    const emotions = {\r\n      clear: {\r\n        weather: '晴朗',\r\n        mood: '愉悦开朗',\r\n        lighting: '明媚的阳光',\r\n        atmosphere: '明亮开朗',\r\n        visualElements: '蓝天白云，阳光透过车窗'\r\n      },\r\n      cloudy: {\r\n        weather: '多云',\r\n        mood: '平静舒缓',\r\n        lighting: '柔和的光线',\r\n        atmosphere: '柔和舒适',\r\n        visualElements: '云层柔和，光线散射'\r\n      },\r\n      rainy: {\r\n        weather: '下雨',\r\n        mood: '宁静沉思',\r\n        lighting: '雨滴的折射光',\r\n        atmosphere: '清新宁静',\r\n        visualElements: '雨滴在车窗上滑落，湿润的光线'\r\n      },\r\n      night: {\r\n        weather: '夜晚',\r\n        mood: '神秘安静',\r\n        lighting: '温暖的灯光',\r\n        atmosphere: '宁静神秘',\r\n        visualElements: '温暖的灯光，夜色中的车流'\r\n      }\r\n    }\r\n    \r\n    return emotions[weather] || emotions.clear\r\n  }\r\n\r\n  /**\r\n   * 获取情绪情感描述\r\n   */\r\n  getMoodEmotion(mood) {\r\n    const emotions = {\r\n      neutral: {\r\n        mood: '平静',\r\n        emotionalState: '心态平和',\r\n        psychologicalNeeds: '稳定舒适的环境',\r\n        colorPreference: '中性色调'\r\n      },\r\n      focused_commute: {\r\n        mood: '专注通勤',\r\n        emotionalState: '目标导向，略带紧迫',\r\n        psychologicalNeeds: '高效清晰的信息展示',\r\n        colorPreference: '清爽的蓝色调'\r\n      },\r\n      family_care: {\r\n        mood: '家庭关爱',\r\n        emotionalState: '温馨负责',\r\n        psychologicalNeeds: '安全舒适的氛围',\r\n        colorPreference: '温暖的橙黄色调'\r\n      },\r\n      relieved_tired: {\r\n        mood: '疲惫释然',\r\n        emotionalState: '放松但有些疲劳',\r\n        psychologicalNeeds: '舒缓放松的环境',\r\n        colorPreference: '柔和的暖色调'\r\n      },\r\n      relaxed_weekend: {\r\n        mood: '周末放松',\r\n        emotionalState: '轻松愉悦',\r\n        psychologicalNeeds: '轻松愉快的氛围',\r\n        colorPreference: '明亮的色彩'\r\n      },\r\n      driving_fatigue: {\r\n        mood: '驾驶疲劳',\r\n        emotionalState: '需要休息',\r\n        psychologicalNeeds: '提神醒脑的环境',\r\n        colorPreference: '清新的绿色调'\r\n      },\r\n      stressed: {\r\n        mood: '压力较大',\r\n        emotionalState: '紧张焦虑',\r\n        psychologicalNeeds: '舒缓压力的环境',\r\n        colorPreference: '平静的蓝色调'\r\n      },\r\n      very_tired: {\r\n        mood: '非常疲劳',\r\n        emotionalState: '极度需要休息',\r\n        psychologicalNeeds: '极度舒适的环境',\r\n        colorPreference: '柔和的深色调'\r\n      }\r\n    }\r\n    \r\n    return emotions[mood] || emotions.neutral\r\n  }\r\n\r\n  /**\r\n   * 获取乘客情感描述\r\n   */\r\n  getPassengerEmotion(passengers) {\r\n    const passengerCount = passengers.length\r\n    \r\n    if (passengerCount === 0) {\r\n      return {\r\n        passengerSituation: '独自驾驶',\r\n        socialAtmosphere: '安静私密',\r\n        interactionNeeds: '个人空间',\r\n        emotionalTone: '独立自主'\r\n      }\r\n    } else if (passengers.includes('child')) {\r\n      return {\r\n        passengerSituation: '与孩子同行',\r\n        socialAtmosphere: '温馨活泼',\r\n        interactionNeeds: '家庭互动',\r\n        emotionalTone: '关爱负责'\r\n      }\r\n    } else if (passengers.includes('spouse')) {\r\n      return {\r\n        passengerSituation: '与伴侣同行',\r\n        socialAtmosphere: '亲密温馨',\r\n        interactionNeeds: '二人世界',\r\n        emotionalTone: '浪漫亲密'\r\n      }\r\n    } else if (passengers.includes('friend')) {\r\n      return {\r\n        passengerSituation: '与朋友同行',\r\n        socialAtmosphere: '轻松愉快',\r\n        interactionNeeds: '朋友交流',\r\n        emotionalTone: '轻松友好'\r\n      }\r\n    } else if (passengerCount > 2) {\r\n      return {\r\n        passengerSituation: '多人同行',\r\n        socialAtmosphere: '热闹活跃',\r\n        interactionNeeds: '群体互动',\r\n        emotionalTone: '热闹欢快'\r\n      }\r\n    } else {\r\n      return {\r\n        passengerSituation: '有乘客同行',\r\n        socialAtmosphere: '舒适友好',\r\n        interactionNeeds: '正常交流',\r\n        emotionalTone: '平和友好'\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取用户角色\r\n   */\r\n  getUserRole(passengers) {\r\n    if (passengers.length === 0) return '独自驾驶'\r\n    if (passengers.includes('child')) return '家长'\r\n    if (passengers.includes('spouse')) return '伴侣'\r\n    if (passengers.includes('friend')) return '朋友'\r\n    return '驾驶者'\r\n  }\r\n\r\n  /**\r\n   * 添加观察者\r\n   */\r\n  addObserver(callback) {\r\n    this.observers.push(callback)\r\n  }\r\n\r\n  /**\r\n   * 移除观察者\r\n   */\r\n  removeObserver(callback) {\r\n    const index = this.observers.indexOf(callback)\r\n    if (index > -1) {\r\n      this.observers.splice(index, 1)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 通知观察者\r\n   */\r\n  notifyObservers(oldContext, newContext) {\r\n    this.observers.forEach(callback => {\r\n      try {\r\n        callback(oldContext, newContext)\r\n      } catch (error) {\r\n        console.error('Context observer notification failed:', error)\r\n      }\r\n    })\r\n  }\r\n\r\n  /**\r\n   * 获取用于提示词生成的上下文\r\n   */\r\n  getPromptGenerationContext() {\r\n    this.inferUserMood()\r\n    this.inferTripPurpose()\r\n    \r\n    return {\r\n      timeOfDay: this.context.timeOfDay,\r\n      weather: this.context.weather,\r\n      mood: this.context.mood,\r\n      userRole: this.getUserRole(this.context.passengers),\r\n      passengers: this.context.passengers.join(', ') || '独自',\r\n      destination: this.context.destination,\r\n      isWeekend: this.context.isWeekend,\r\n      drivingDuration: this.formatDuration(this.context.drivingDuration),\r\n      season: this.context.season,\r\n      tripPurpose: this.context.tripPurpose\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 格式化时长\r\n   */\r\n  formatDuration(milliseconds) {\r\n    const minutes = Math.floor(milliseconds / 60000)\r\n    if (minutes < 60) {\r\n      return `${minutes}分钟`\r\n    } else {\r\n      const hours = Math.floor(minutes / 60)\r\n      const remainingMinutes = minutes % 60\r\n      return `${hours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 模拟驾驶状态更新\r\n   */\r\n  simulateDrivingUpdate() {\r\n    if (this.context.isDriving) {\r\n      this.updateContext({\r\n        drivingDuration: this.context.drivingDuration + 60000, // 增加1分钟\r\n        fatigueLevel: Math.min(10, this.context.fatigueLevel + 0.1)\r\n      })\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取完整上下文信息\r\n   */\r\n  getContext() {\r\n    return { ...this.context }\r\n  }\r\n\r\n  /**\r\n   * 重置上下文\r\n   */\r\n  reset() {\r\n    this.context = this.initializeContext()\r\n    this.lastUpdate = Date.now()\r\n  }\r\n\r\n  /**\r\n   * 获取上下文统计信息\r\n   */\r\n  getStatistics() {\r\n    return {\r\n      lastUpdate: this.lastUpdate,\r\n      sceneSwitchCount: this.context.sceneSwitchCount,\r\n      drivingDuration: this.formatDuration(this.context.drivingDuration),\r\n      currentMood: this.context.mood,\r\n      tripPurpose: this.context.tripPurpose,\r\n      observerCount: this.observers.length\r\n    }\r\n  }\r\n}"], "mappings": ";;;AAAA;AACA;AACA,eAAe,MAAMA,mBAAmB,CAAC;EACvCC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACvC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC9B;;EAEA;AACF;AACA;EACEJ,iBAAiBA,CAAA,EAAG;IAClB,OAAO;MACL;MACAK,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBE,SAAS,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACrCC,SAAS,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC;MAC3BC,MAAM,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAE/B;MACAC,OAAO,EAAE,OAAO;MAAE;MAClBC,WAAW,EAAE,EAAE;MAAE;;MAEjB;MACAC,eAAe,EAAE,CAAC;MAClBC,SAAS,EAAE,KAAK;MAChBC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,CAAC;MAER;MACAC,QAAQ,EAAE,QAAQ;MAClBC,IAAI,EAAE,SAAS;MACfC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MAEf;MACAC,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE,CAAC;MAEjB;MACAC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,WAAW,EAAE,SAAS;MAEtB;MACAC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,QAAQ;MAEvB;MACAC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,MAAM;MAEhB;MACAC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE,CAAC;MAEnB;MACAC,eAAe,EAAE;QACfC,UAAU,EAAE,UAAU;QACtBC,kBAAkB,EAAE,MAAM;QAC1BC,oBAAoB,EAAE;MACxB;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACE7B,mBAAmBA,CAAA,EAAG;IACpB,MAAM8B,IAAI,GAAG,IAAIlC,IAAI,CAAC,CAAC,CAACmC,QAAQ,CAAC,CAAC;IAElC,IAAID,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE,OAAO,cAAc;IAChD,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC5C,IAAIA,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,MAAM;IAC1C,IAAIA,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,WAAW;IAC/C,IAAIA,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC7C,IAAIA,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,OAAO;IAC3C,OAAO,WAAW;EACpB;;EAEA;AACF;AACA;EACE7B,SAASA,CAAA,EAAG;IACV,MAAM+B,GAAG,GAAG,IAAIpC,IAAI,CAAC,CAAC,CAACqC,MAAM,CAAC,CAAC;IAC/B,OAAOD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;EAC/B;;EAEA;AACF;AACA;EACE7B,gBAAgBA,CAAA,EAAG;IACjB,MAAM+B,KAAK,GAAG,IAAItC,IAAI,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC;IAEnC,IAAID,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC7C,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC7C,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC9C,OAAO,QAAQ;EACjB;;EAEA;AACF;AACA;EACEE,aAAaA,CAACC,OAAO,EAAE;IACrB,MAAMC,UAAU,GAAG;MAAE,GAAG,IAAI,CAAC9C;IAAQ,CAAC;IACtC,IAAI,CAACA,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAG6C,OAAO;MAAEvC,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC;IAAE,CAAC;;IAErE;IACA,IAAI,CAAC0C,eAAe,CAACD,UAAU,EAAE,IAAI,CAAC9C,OAAO,CAAC;;IAE9C;IACA,IAAI,CAACgD,iBAAiB,CAAC,CAAC;IAExB,IAAI,CAAC7C,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC9B;;EAEA;AACF;AACA;EACE2C,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAAChD,OAAO,CAACO,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IACnD,IAAI,CAACR,OAAO,CAACS,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,CAAC;IACzC,IAAI,CAACT,OAAO,CAACU,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC/C;;EAEA;AACF;AACA;EACEsC,aAAaA,CAAA,EAAG;IACd,MAAM;MAAE1C,SAAS;MAAEE,SAAS;MAAEK,eAAe;MAAEO,YAAY;MAAED,WAAW;MAAEG;IAAe,CAAC,GAAG,IAAI,CAACvB,OAAO;;IAEzG;IACA,IAAImB,IAAI,GAAG,SAAS;;IAEpB;IACA,IAAIZ,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,SAAS,EAAE;MAC3D,IAAI,CAACE,SAAS,EAAE;QACdU,IAAI,GAAGI,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG,iBAAiB;MAC/D,CAAC,MAAM;QACLJ,IAAI,GAAG,iBAAiB;MAC1B;IACF;;IAEA;IAAA,KACK,IAAIZ,SAAS,KAAK,SAAS,EAAE;MAChCY,IAAI,GAAG,gBAAgB;IACzB;;IAEA;IAAA,KACK,IAAIZ,SAAS,KAAK,WAAW,EAAE;MAClCY,IAAI,GAAG,aAAa;IACtB;;IAEA;IAAA,KACK,IAAIV,SAAS,EAAE;MAClBU,IAAI,GAAG,eAAe;IACxB;;IAEA;IACA,IAAIL,eAAe,GAAG,OAAO,EAAE;MAAE;MAC/BK,IAAI,GAAGA,IAAI,CAAC+B,QAAQ,CAAC,OAAO,CAAC,GAAG/B,IAAI,GAAG,iBAAiB;IAC1D;;IAEA;IACA,IAAIE,YAAY,GAAG,CAAC,EAAE;MACpBF,IAAI,GAAG,YAAY;IACrB;;IAEA;IACA,IAAIC,WAAW,GAAG,CAAC,EAAE;MACnBD,IAAI,GAAG,UAAU;IACnB;IAEA,IAAI,CAACnB,OAAO,CAACmB,IAAI,GAAGA,IAAI;IACxB,OAAOA,IAAI;EACb;;EAEA;AACF;AACA;EACEgC,gBAAgBA,CAAA,EAAG;IACjB,MAAM;MAAE5C,SAAS;MAAEE,SAAS;MAAEgB,eAAe;MAAEH;IAAW,CAAC,GAAG,IAAI,CAACtB,OAAO;IAE1E,IAAIoD,OAAO,GAAG,SAAS;;IAEvB;IACA,IAAI,CAAC3C,SAAS,KAAKF,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,SAAS,CAAC,EAAE;MAC3E,IAAIkB,eAAe,KAAK,MAAM,IAAIA,eAAe,KAAK,QAAQ,EAAE;QAC9D2B,OAAO,GAAG,iBAAiB;MAC7B,CAAC,MAAM,IAAI9B,UAAU,CAAC4B,QAAQ,CAAC,OAAO,CAAC,EAAE;QACvCE,OAAO,GAAG,sBAAsB;MAClC;IACF;;IAEA;IAAA,KACK,IAAI,CAAC3C,SAAS,IAAIF,SAAS,KAAK,SAAS,EAAE;MAC9C,IAAIkB,eAAe,KAAK,MAAM,EAAE;QAC9B2B,OAAO,GAAG,aAAa;MACzB;IACF;;IAEA;IAAA,KACK,IAAI3C,SAAS,EAAE;MAClB,IAAIgB,eAAe,KAAK,MAAM,IAAIA,eAAe,KAAK,YAAY,EAAE;QAClE2B,OAAO,GAAG,cAAc;MAC1B,CAAC,MAAM,IAAI3B,eAAe,KAAK,YAAY,EAAE;QAC3C2B,OAAO,GAAG,YAAY;MACxB,CAAC,MAAM,IAAI3B,eAAe,KAAK,UAAU,EAAE;QACzC2B,OAAO,GAAG,UAAU;MACtB;IACF;IAEA,IAAI,CAACpD,OAAO,CAAC0B,WAAW,GAAG0B,OAAO;IAClC,OAAOA,OAAO;EAChB;;EAEA;AACF;AACA;EACEC,mBAAmBA,CAAA,EAAG;IACpB,MAAM;MAAE9C,SAAS;MAAEK,OAAO;MAAEO,IAAI;MAAEG,UAAU;MAAEE,WAAW;MAAEd;IAAO,CAAC,GAAG,IAAI,CAACV,OAAO;IAElF,MAAMsD,WAAW,GAAG,IAAI,CAACC,cAAc,CAAChD,SAAS,CAAC;IAClD,MAAMiD,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC7C,OAAO,CAAC;IACtD,MAAM8C,WAAW,GAAG,IAAI,CAACC,cAAc,CAACxC,IAAI,CAAC;IAC7C,MAAMyC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAACvC,UAAU,CAAC;IAE7D,OAAO;MACL,GAAGgC,WAAW;MACd,GAAGE,cAAc;MACjB,GAAGE,WAAW;MACd,GAAGE,gBAAgB;MACnBlD,MAAM;MACNc,WAAW;MACXN,QAAQ,EAAE,IAAI,CAAC4C,WAAW,CAACxC,UAAU;IACvC,CAAC;EACH;;EAEA;AACF;AACA;EACEiC,cAAcA,CAAChD,SAAS,EAAE;IACxB,MAAMwD,QAAQ,GAAG;MACfC,YAAY,EAAE;QACZzD,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf,CAAC;MACDC,OAAO,EAAE;QACP7D,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf,CAAC;MACDE,IAAI,EAAE;QACJ9D,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf,CAAC;MACDG,SAAS,EAAE;QACT/D,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf,CAAC;MACDI,OAAO,EAAE;QACPhE,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf,CAAC;MACDK,KAAK,EAAE;QACLjE,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf,CAAC;MACDM,SAAS,EAAE;QACTlE,SAAS,EAAE,IAAI;QACfY,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACf;IACF,CAAC;IAED,OAAOJ,QAAQ,CAACxD,SAAS,CAAC,IAAIwD,QAAQ,CAACK,OAAO;EAChD;;EAEA;AACF;AACA;EACEX,iBAAiBA,CAAC7C,OAAO,EAAE;IACzB,MAAMmD,QAAQ,GAAG;MACfW,KAAK,EAAE;QACL9D,OAAO,EAAE,IAAI;QACbO,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBS,cAAc,EAAE;MAClB,CAAC;MACDC,MAAM,EAAE;QACNhE,OAAO,EAAE,IAAI;QACbO,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBS,cAAc,EAAE;MAClB,CAAC;MACDE,KAAK,EAAE;QACLjE,OAAO,EAAE,IAAI;QACbO,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,MAAM;QAClBS,cAAc,EAAE;MAClB,CAAC;MACDH,KAAK,EAAE;QACL5D,OAAO,EAAE,IAAI;QACbO,IAAI,EAAE,MAAM;QACZ8C,QAAQ,EAAE,OAAO;QACjBC,UAAU,EAAE,MAAM;QAClBS,cAAc,EAAE;MAClB;IACF,CAAC;IAED,OAAOZ,QAAQ,CAACnD,OAAO,CAAC,IAAImD,QAAQ,CAACW,KAAK;EAC5C;;EAEA;AACF;AACA;EACEf,cAAcA,CAACxC,IAAI,EAAE;IACnB,MAAM4C,QAAQ,GAAG;MACfe,OAAO,EAAE;QACP3D,IAAI,EAAE,IAAI;QACV4D,cAAc,EAAE,MAAM;QACtBC,kBAAkB,EAAE,SAAS;QAC7BC,eAAe,EAAE;MACnB,CAAC;MACDC,eAAe,EAAE;QACf/D,IAAI,EAAE,MAAM;QACZ4D,cAAc,EAAE,WAAW;QAC3BC,kBAAkB,EAAE,WAAW;QAC/BC,eAAe,EAAE;MACnB,CAAC;MACDE,WAAW,EAAE;QACXhE,IAAI,EAAE,MAAM;QACZ4D,cAAc,EAAE,MAAM;QACtBC,kBAAkB,EAAE,SAAS;QAC7BC,eAAe,EAAE;MACnB,CAAC;MACDG,cAAc,EAAE;QACdjE,IAAI,EAAE,MAAM;QACZ4D,cAAc,EAAE,SAAS;QACzBC,kBAAkB,EAAE,SAAS;QAC7BC,eAAe,EAAE;MACnB,CAAC;MACDI,eAAe,EAAE;QACflE,IAAI,EAAE,MAAM;QACZ4D,cAAc,EAAE,MAAM;QACtBC,kBAAkB,EAAE,SAAS;QAC7BC,eAAe,EAAE;MACnB,CAAC;MACDK,eAAe,EAAE;QACfnE,IAAI,EAAE,MAAM;QACZ4D,cAAc,EAAE,MAAM;QACtBC,kBAAkB,EAAE,SAAS;QAC7BC,eAAe,EAAE;MACnB,CAAC;MACDM,QAAQ,EAAE;QACRpE,IAAI,EAAE,MAAM;QACZ4D,cAAc,EAAE,MAAM;QACtBC,kBAAkB,EAAE,SAAS;QAC7BC,eAAe,EAAE;MACnB,CAAC;MACDO,UAAU,EAAE;QACVrE,IAAI,EAAE,MAAM;QACZ4D,cAAc,EAAE,QAAQ;QACxBC,kBAAkB,EAAE,SAAS;QAC7BC,eAAe,EAAE;MACnB;IACF,CAAC;IAED,OAAOlB,QAAQ,CAAC5C,IAAI,CAAC,IAAI4C,QAAQ,CAACe,OAAO;EAC3C;;EAEA;AACF;AACA;EACEjB,mBAAmBA,CAACvC,UAAU,EAAE;IAC9B,MAAMC,cAAc,GAAGD,UAAU,CAACmE,MAAM;IAExC,IAAIlE,cAAc,KAAK,CAAC,EAAE;MACxB,OAAO;QACLmE,kBAAkB,EAAE,MAAM;QAC1BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,MAAM;QACxBC,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,MAAM,IAAIvE,UAAU,CAAC4B,QAAQ,CAAC,OAAO,CAAC,EAAE;MACvC,OAAO;QACLwC,kBAAkB,EAAE,OAAO;QAC3BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,MAAM;QACxBC,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,MAAM,IAAIvE,UAAU,CAAC4B,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACxC,OAAO;QACLwC,kBAAkB,EAAE,OAAO;QAC3BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,MAAM;QACxBC,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,MAAM,IAAIvE,UAAU,CAAC4B,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACxC,OAAO;QACLwC,kBAAkB,EAAE,OAAO;QAC3BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,MAAM;QACxBC,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,MAAM,IAAItE,cAAc,GAAG,CAAC,EAAE;MAC7B,OAAO;QACLmE,kBAAkB,EAAE,MAAM;QAC1BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,MAAM;QACxBC,aAAa,EAAE;MACjB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLH,kBAAkB,EAAE,OAAO;QAC3BC,gBAAgB,EAAE,MAAM;QACxBC,gBAAgB,EAAE,MAAM;QACxBC,aAAa,EAAE;MACjB,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE/B,WAAWA,CAACxC,UAAU,EAAE;IACtB,IAAIA,UAAU,CAACmE,MAAM,KAAK,CAAC,EAAE,OAAO,MAAM;IAC1C,IAAInE,UAAU,CAAC4B,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAI5B,UAAU,CAAC4B,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC9C,IAAI5B,UAAU,CAAC4B,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC9C,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACE4C,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAAC7F,SAAS,CAAC8F,IAAI,CAACD,QAAQ,CAAC;EAC/B;;EAEA;AACF;AACA;EACEE,cAAcA,CAACF,QAAQ,EAAE;IACvB,MAAMG,KAAK,GAAG,IAAI,CAAChG,SAAS,CAACiG,OAAO,CAACJ,QAAQ,CAAC;IAC9C,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAChG,SAAS,CAACkG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IACjC;EACF;;EAEA;AACF;AACA;EACEnD,eAAeA,CAACD,UAAU,EAAEuD,UAAU,EAAE;IACtC,IAAI,CAACnG,SAAS,CAACoG,OAAO,CAACP,QAAQ,IAAI;MACjC,IAAI;QACFA,QAAQ,CAACjD,UAAU,EAAEuD,UAAU,CAAC;MAClC,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEE,0BAA0BA,CAAA,EAAG;IAC3B,IAAI,CAACxD,aAAa,CAAC,CAAC;IACpB,IAAI,CAACE,gBAAgB,CAAC,CAAC;IAEvB,OAAO;MACL5C,SAAS,EAAE,IAAI,CAACP,OAAO,CAACO,SAAS;MACjCK,OAAO,EAAE,IAAI,CAACZ,OAAO,CAACY,OAAO;MAC7BO,IAAI,EAAE,IAAI,CAACnB,OAAO,CAACmB,IAAI;MACvBD,QAAQ,EAAE,IAAI,CAAC4C,WAAW,CAAC,IAAI,CAAC9D,OAAO,CAACsB,UAAU,CAAC;MACnDA,UAAU,EAAE,IAAI,CAACtB,OAAO,CAACsB,UAAU,CAACoF,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;MACtDlF,WAAW,EAAE,IAAI,CAACxB,OAAO,CAACwB,WAAW;MACrCf,SAAS,EAAE,IAAI,CAACT,OAAO,CAACS,SAAS;MACjCK,eAAe,EAAE,IAAI,CAAC6F,cAAc,CAAC,IAAI,CAAC3G,OAAO,CAACc,eAAe,CAAC;MAClEJ,MAAM,EAAE,IAAI,CAACV,OAAO,CAACU,MAAM;MAC3BgB,WAAW,EAAE,IAAI,CAAC1B,OAAO,CAAC0B;IAC5B,CAAC;EACH;;EAEA;AACF;AACA;EACEiF,cAAcA,CAACC,YAAY,EAAE;IAC3B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,KAAK,CAAC;IAChD,IAAIC,OAAO,GAAG,EAAE,EAAE;MAChB,OAAO,GAAGA,OAAO,IAAI;IACvB,CAAC,MAAM;MACL,MAAMG,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;MACrC,OAAO,GAAGG,KAAK,KAAKC,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAG,IAAI,GAAG,EAAE,EAAE;IAC3E;EACF;;EAEA;AACF;AACA;EACEC,qBAAqBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAClH,OAAO,CAACe,SAAS,EAAE;MAC1B,IAAI,CAAC6B,aAAa,CAAC;QACjB9B,eAAe,EAAE,IAAI,CAACd,OAAO,CAACc,eAAe,GAAG,KAAK;QAAE;QACvDO,YAAY,EAAEyF,IAAI,CAACK,GAAG,CAAC,EAAE,EAAE,IAAI,CAACnH,OAAO,CAACqB,YAAY,GAAG,GAAG;MAC5D,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACE+F,UAAUA,CAAA,EAAG;IACX,OAAO;MAAE,GAAG,IAAI,CAACpH;IAAQ,CAAC;EAC5B;;EAEA;AACF;AACA;EACEqH,KAAKA,CAAA,EAAG;IACN,IAAI,CAACrH,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACvC,IAAI,CAACE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC9B;;EAEA;AACF;AACA;EACEiH,aAAaA,CAAA,EAAG;IACd,OAAO;MACLnH,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B8B,gBAAgB,EAAE,IAAI,CAACjC,OAAO,CAACiC,gBAAgB;MAC/CnB,eAAe,EAAE,IAAI,CAAC6F,cAAc,CAAC,IAAI,CAAC3G,OAAO,CAACc,eAAe,CAAC;MAClEyG,WAAW,EAAE,IAAI,CAACvH,OAAO,CAACmB,IAAI;MAC9BO,WAAW,EAAE,IAAI,CAAC1B,OAAO,CAAC0B,WAAW;MACrC8F,aAAa,EAAE,IAAI,CAACtH,SAAS,CAACuF;IAChC,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}