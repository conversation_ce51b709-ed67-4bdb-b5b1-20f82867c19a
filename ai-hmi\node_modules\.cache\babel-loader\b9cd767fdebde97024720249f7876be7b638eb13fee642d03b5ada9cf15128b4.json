{"ast": null, "code": "import { ref, computed, onMounted } from 'vue';\nimport BaseCard from '../BaseCard.vue';\nexport default {\n  name: 'KidEducationCard',\n  components: {\n    BaseCard\n  },\n  props: {\n    position: {\n      type: Object,\n      default: () => ({\n        x: 1,\n        y: 2\n      })\n    },\n    theme: {\n      type: String,\n      default: 'glass'\n    },\n    themeColors: {\n      type: Object,\n      default: () => ({\n        primary: '#ff6b6b',\n        secondary: '#4ecdc4',\n        background: 'rgba(255, 107, 107, 0.1)',\n        text: '#ffffff'\n      })\n    }\n  },\n  emits: ['card-click', 'mode-changed', 'lesson-completed'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式状态\n    const selectedMode = ref('story');\n    const isPlaying = ref(false);\n    const selectedOption = ref(null);\n    const showAnswer = ref(false);\n    const gameNumber = ref(0);\n    const todayStars = ref(3);\n\n    // 当前课程数据\n    const currentLesson = ref({\n      title: '认识动物',\n      description: '学习各种动物的名称和特征',\n      icon: 'fas fa-paw',\n      progress: 65\n    });\n\n    // 学习模式\n    const learningModes = ref([{\n      id: 'story',\n      name: '故事',\n      icon: 'fas fa-book'\n    }, {\n      id: 'quiz',\n      name: '问答',\n      icon: 'fas fa-question-circle'\n    }, {\n      id: 'game',\n      name: '游戏',\n      icon: 'fas fa-gamepad'\n    }]);\n\n    // 故事内容\n    const stories = ref([{\n      title: '小兔子的冒险',\n      content: '从前有一只小兔子，它住在美丽的森林里...'\n    }, {\n      title: '勇敢的小狮子',\n      content: '在非洲大草原上，有一只勇敢的小狮子...'\n    }]);\n    const currentStoryIndex = ref(0);\n    const currentStory = computed(() => stories.value[currentStoryIndex.value]);\n\n    // 问答内容\n    const quizzes = ref([{\n      question: '小兔子最喜欢吃什么？',\n      options: ['胡萝卜', '肉', '鱼', '草'],\n      correct: 0\n    }, {\n      question: '狮子是什么动物？',\n      options: ['食草动物', '食肉动物', '杂食动物', '不知道'],\n      correct: 1\n    }]);\n    const currentQuizIndex = ref(0);\n    const currentQuiz = computed(() => quizzes.value[currentQuizIndex.value]);\n\n    // 事件处理\n    const handleCardClick = () => {\n      emit('card-click', 'kidEducation');\n    };\n    const selectLearningMode = mode => {\n      selectedMode.value = mode.id;\n      emit('mode-changed', mode.id);\n\n      // 重置相关状态\n      if (mode.id === 'quiz') {\n        selectedOption.value = null;\n        showAnswer.value = false;\n      }\n    };\n    const previousStory = () => {\n      if (currentStoryIndex.value > 0) {\n        currentStoryIndex.value--;\n      }\n    };\n    const nextStory = () => {\n      if (currentStoryIndex.value < stories.value.length - 1) {\n        currentStoryIndex.value++;\n      }\n    };\n    const toggleStoryPlay = () => {\n      isPlaying.value = !isPlaying.value;\n      // 这里可以集成TTS服务来朗读故事\n    };\n    const selectQuizOption = index => {\n      if (showAnswer.value) return;\n      selectedOption.value = index;\n      showAnswer.value = true;\n\n      // 2秒后自动进入下一题\n      setTimeout(() => {\n        if (currentQuizIndex.value < quizzes.value.length - 1) {\n          currentQuizIndex.value++;\n          selectedOption.value = null;\n          showAnswer.value = false;\n        } else {\n          // 完成所有问题\n          emit('lesson-completed', 'quiz');\n        }\n      }, 2000);\n    };\n    const gameAction = action => {\n      switch (action) {\n        case 'add':\n          gameNumber.value++;\n          break;\n        case 'subtract':\n          if (gameNumber.value > 0) {\n            gameNumber.value--;\n          }\n          break;\n        case 'reset':\n          gameNumber.value = 0;\n          break;\n      }\n    };\n    const openFullEducation = () => {\n      // 打开完整的教育界面\n      console.log('打开完整教育界面');\n    };\n\n    // 初始化\n    onMounted(() => {\n      // 可以在这里加载用户的学习进度\n      console.log('儿童教育卡片已加载');\n    });\n    return {\n      selectedMode,\n      isPlaying,\n      selectedOption,\n      showAnswer,\n      gameNumber,\n      todayStars,\n      currentLesson,\n      learningModes,\n      currentStory,\n      currentQuiz,\n      handleCardClick,\n      selectLearningMode,\n      previousStory,\n      nextStory,\n      toggleStoryPlay,\n      selectQuizOption,\n      gameAction,\n      openFullEducation\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "BaseCard", "name", "components", "props", "position", "type", "Object", "default", "x", "y", "theme", "String", "themeColors", "primary", "secondary", "background", "text", "emits", "setup", "emit", "selectedMode", "isPlaying", "selectedOption", "showAnswer", "gameNumber", "todayStars", "<PERSON><PERSON><PERSON><PERSON>", "title", "description", "icon", "progress", "learningModes", "id", "stories", "content", "currentStoryIndex", "currentStory", "value", "quizzes", "question", "options", "correct", "currentQuizIndex", "currentQuiz", "handleCardClick", "selectLearningMode", "mode", "previousStory", "nextStory", "length", "toggleStoryPlay", "selectQuizOption", "index", "setTimeout", "gameAction", "action", "openFullEducation", "console", "log"], "sources": ["D:\\code\\pythonWork\\theme\\ai-hmi\\src\\components\\cards\\KidEducationCard.vue"], "sourcesContent": ["<template>\r\n  <BaseCard\r\n    card-type=\"kidEducation\"\r\n    size=\"large\"\r\n    :position=\"position\"\r\n    :theme=\"theme\"\r\n    :theme-colors=\"themeColors\"\r\n    :title=\"'儿童教育'\"\r\n    :icon=\"'fas fa-graduation-cap'\"\r\n    :clickable=\"true\"\r\n    :show-footer=\"true\"\r\n    @click=\"handleCardClick\"\r\n    class=\"kid-education-card\"\r\n  >\r\n    <div class=\"education-content\">\r\n      <!-- 当前学习内容 -->\r\n      <div class=\"current-lesson\">\r\n        <div class=\"lesson-header\">\r\n          <div class=\"lesson-icon\">\r\n            <i :class=\"currentLesson.icon\"></i>\r\n          </div>\r\n          <div class=\"lesson-info\">\r\n            <h4 class=\"lesson-title\">{{ currentLesson.title }}</h4>\r\n            <p class=\"lesson-description\">{{ currentLesson.description }}</p>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 学习进度 -->\r\n        <div class=\"lesson-progress\">\r\n          <div class=\"progress-bar\">\r\n            <div \r\n              class=\"progress-fill\" \r\n              :style=\"{ width: `${currentLesson.progress}%` }\"\r\n            ></div>\r\n          </div>\r\n          <span class=\"progress-text\">{{ currentLesson.progress }}% 完成</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 学习模式选择 -->\r\n      <div class=\"learning-modes\">\r\n        <button \r\n          v-for=\"mode in learningModes\" \r\n          :key=\"mode.id\"\r\n          @click=\"selectLearningMode(mode)\"\r\n          :class=\"['mode-btn', { active: selectedMode === mode.id }]\"\r\n        >\r\n          <i :class=\"mode.icon\"></i>\r\n          <span>{{ mode.name }}</span>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 互动区域 -->\r\n      <div class=\"interaction-area\">\r\n        <div v-if=\"selectedMode === 'story'\" class=\"story-mode\">\r\n          <div class=\"story-content\">\r\n            <h5>{{ currentStory.title }}</h5>\r\n            <p>{{ currentStory.content }}</p>\r\n          </div>\r\n          <div class=\"story-controls\">\r\n            <button @click=\"previousStory\" class=\"story-btn\">\r\n              <i class=\"fas fa-step-backward\"></i>\r\n            </button>\r\n            <button @click=\"toggleStoryPlay\" class=\"story-btn play-btn\">\r\n              <i :class=\"isPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\r\n            </button>\r\n            <button @click=\"nextStory\" class=\"story-btn\">\r\n              <i class=\"fas fa-step-forward\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-else-if=\"selectedMode === 'quiz'\" class=\"quiz-mode\">\r\n          <div class=\"quiz-question\">\r\n            <h5>{{ currentQuiz.question }}</h5>\r\n            <div class=\"quiz-options\">\r\n              <button \r\n                v-for=\"(option, index) in currentQuiz.options\" \r\n                :key=\"index\"\r\n                @click=\"selectQuizOption(index)\"\r\n                :class=\"['quiz-option', { \r\n                  selected: selectedOption === index,\r\n                  correct: showAnswer && index === currentQuiz.correct,\r\n                  wrong: showAnswer && selectedOption === index && index !== currentQuiz.correct\r\n                }]\"\r\n              >\r\n                {{ option }}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-else-if=\"selectedMode === 'game'\" class=\"game-mode\">\r\n          <div class=\"game-content\">\r\n            <h5>数字游戏</h5>\r\n            <div class=\"number-game\">\r\n              <div class=\"game-display\">{{ gameNumber }}</div>\r\n              <div class=\"game-controls\">\r\n                <button @click=\"gameAction('add')\" class=\"game-btn\">+1</button>\r\n                <button @click=\"gameAction('subtract')\" class=\"game-btn\">-1</button>\r\n                <button @click=\"gameAction('reset')\" class=\"game-btn\">重置</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <div class=\"card-footer-content\">\r\n        <div class=\"achievement-info\">\r\n          <i class=\"fas fa-star\"></i>\r\n          <span>今日获得 {{ todayStars }} 颗星</span>\r\n        </div>\r\n        <button @click=\"openFullEducation\" class=\"expand-btn\">\r\n          <i class=\"fas fa-expand\"></i>\r\n          <span>展开</span>\r\n        </button>\r\n      </div>\r\n    </template>\r\n  </BaseCard>\r\n</template>\r\n\r\n<script>\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport BaseCard from '../BaseCard.vue'\r\n\r\nexport default {\r\n  name: 'KidEducationCard',\r\n  components: {\r\n    BaseCard\r\n  },\r\n  props: {\r\n    position: {\r\n      type: Object,\r\n      default: () => ({ x: 1, y: 2 })\r\n    },\r\n    theme: {\r\n      type: String,\r\n      default: 'glass'\r\n    },\r\n    themeColors: {\r\n      type: Object,\r\n      default: () => ({\r\n        primary: '#ff6b6b',\r\n        secondary: '#4ecdc4',\r\n        background: 'rgba(255, 107, 107, 0.1)',\r\n        text: '#ffffff'\r\n      })\r\n    }\r\n  },\r\n  \r\n  emits: ['card-click', 'mode-changed', 'lesson-completed'],\r\n  \r\n  setup(props, { emit }) {\r\n    // 响应式状态\r\n    const selectedMode = ref('story')\r\n    const isPlaying = ref(false)\r\n    const selectedOption = ref(null)\r\n    const showAnswer = ref(false)\r\n    const gameNumber = ref(0)\r\n    const todayStars = ref(3)\r\n    \r\n    // 当前课程数据\r\n    const currentLesson = ref({\r\n      title: '认识动物',\r\n      description: '学习各种动物的名称和特征',\r\n      icon: 'fas fa-paw',\r\n      progress: 65\r\n    })\r\n    \r\n    // 学习模式\r\n    const learningModes = ref([\r\n      { id: 'story', name: '故事', icon: 'fas fa-book' },\r\n      { id: 'quiz', name: '问答', icon: 'fas fa-question-circle' },\r\n      { id: 'game', name: '游戏', icon: 'fas fa-gamepad' }\r\n    ])\r\n    \r\n    // 故事内容\r\n    const stories = ref([\r\n      {\r\n        title: '小兔子的冒险',\r\n        content: '从前有一只小兔子，它住在美丽的森林里...'\r\n      },\r\n      {\r\n        title: '勇敢的小狮子',\r\n        content: '在非洲大草原上，有一只勇敢的小狮子...'\r\n      }\r\n    ])\r\n    \r\n    const currentStoryIndex = ref(0)\r\n    const currentStory = computed(() => stories.value[currentStoryIndex.value])\r\n    \r\n    // 问答内容\r\n    const quizzes = ref([\r\n      {\r\n        question: '小兔子最喜欢吃什么？',\r\n        options: ['胡萝卜', '肉', '鱼', '草'],\r\n        correct: 0\r\n      },\r\n      {\r\n        question: '狮子是什么动物？',\r\n        options: ['食草动物', '食肉动物', '杂食动物', '不知道'],\r\n        correct: 1\r\n      }\r\n    ])\r\n    \r\n    const currentQuizIndex = ref(0)\r\n    const currentQuiz = computed(() => quizzes.value[currentQuizIndex.value])\r\n    \r\n    // 事件处理\r\n    const handleCardClick = () => {\r\n      emit('card-click', 'kidEducation')\r\n    }\r\n    \r\n    const selectLearningMode = (mode) => {\r\n      selectedMode.value = mode.id\r\n      emit('mode-changed', mode.id)\r\n      \r\n      // 重置相关状态\r\n      if (mode.id === 'quiz') {\r\n        selectedOption.value = null\r\n        showAnswer.value = false\r\n      }\r\n    }\r\n    \r\n    const previousStory = () => {\r\n      if (currentStoryIndex.value > 0) {\r\n        currentStoryIndex.value--\r\n      }\r\n    }\r\n    \r\n    const nextStory = () => {\r\n      if (currentStoryIndex.value < stories.value.length - 1) {\r\n        currentStoryIndex.value++\r\n      }\r\n    }\r\n    \r\n    const toggleStoryPlay = () => {\r\n      isPlaying.value = !isPlaying.value\r\n      // 这里可以集成TTS服务来朗读故事\r\n    }\r\n    \r\n    const selectQuizOption = (index) => {\r\n      if (showAnswer.value) return\r\n      \r\n      selectedOption.value = index\r\n      showAnswer.value = true\r\n      \r\n      // 2秒后自动进入下一题\r\n      setTimeout(() => {\r\n        if (currentQuizIndex.value < quizzes.value.length - 1) {\r\n          currentQuizIndex.value++\r\n          selectedOption.value = null\r\n          showAnswer.value = false\r\n        } else {\r\n          // 完成所有问题\r\n          emit('lesson-completed', 'quiz')\r\n        }\r\n      }, 2000)\r\n    }\r\n    \r\n    const gameAction = (action) => {\r\n      switch (action) {\r\n        case 'add':\r\n          gameNumber.value++\r\n          break\r\n        case 'subtract':\r\n          if (gameNumber.value > 0) {\r\n            gameNumber.value--\r\n          }\r\n          break\r\n        case 'reset':\r\n          gameNumber.value = 0\r\n          break\r\n      }\r\n    }\r\n    \r\n    const openFullEducation = () => {\r\n      // 打开完整的教育界面\r\n      console.log('打开完整教育界面')\r\n    }\r\n    \r\n    // 初始化\r\n    onMounted(() => {\r\n      // 可以在这里加载用户的学习进度\r\n      console.log('儿童教育卡片已加载')\r\n    })\r\n    \r\n    return {\r\n      selectedMode,\r\n      isPlaying,\r\n      selectedOption,\r\n      showAnswer,\r\n      gameNumber,\r\n      todayStars,\r\n      currentLesson,\r\n      learningModes,\r\n      currentStory,\r\n      currentQuiz,\r\n      handleCardClick,\r\n      selectLearningMode,\r\n      previousStory,\r\n      nextStory,\r\n      toggleStoryPlay,\r\n      selectQuizOption,\r\n      gameAction,\r\n      openFullEducation\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.kid-education-card {\r\n  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);\r\n}\r\n\r\n.education-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  height: 100%;\r\n}\r\n\r\n/* 当前课程 */\r\n.current-lesson {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n}\r\n\r\n.lesson-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.lesson-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 18px;\r\n}\r\n\r\n.lesson-info h4 {\r\n  margin: 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 16px;\r\n}\r\n\r\n.lesson-info p {\r\n  margin: 5px 0 0 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n  font-size: 12px;\r\n}\r\n\r\n.lesson-progress {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.progress-bar {\r\n  flex: 1;\r\n  height: 6px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 学习模式 */\r\n.learning-modes {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.mode-btn {\r\n  flex: 1;\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 8px;\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 11px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.mode-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.mode-btn.active {\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  border-color: var(--card-primary-color, #ff6b6b);\r\n}\r\n\r\n.mode-btn i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 互动区域 */\r\n.interaction-area {\r\n  flex: 1;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 故事模式 */\r\n.story-content h5 {\r\n  margin: 0 0 10px 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 14px;\r\n}\r\n\r\n.story-content p {\r\n  margin: 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  opacity: 0.9;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.story-controls {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 10px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.story-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  color: white;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.story-btn:hover {\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n}\r\n\r\n.play-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n\r\n/* 问答模式 */\r\n.quiz-question h5 {\r\n  margin: 0 0 15px 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 14px;\r\n}\r\n\r\n.quiz-options {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 8px;\r\n}\r\n\r\n.quiz-option {\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 6px;\r\n  color: var(--card-text-color, #ffffff);\r\n  cursor: pointer;\r\n  font-size: 11px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.quiz-option:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.quiz-option.selected {\r\n  background: var(--card-primary-color, #ff6b6b);\r\n}\r\n\r\n.quiz-option.correct {\r\n  background: #2ecc71;\r\n  border-color: #2ecc71;\r\n}\r\n\r\n.quiz-option.wrong {\r\n  background: #e74c3c;\r\n  border-color: #e74c3c;\r\n}\r\n\r\n/* 游戏模式 */\r\n.game-content h5 {\r\n  margin: 0 0 15px 0;\r\n  color: var(--card-text-color, #ffffff);\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.number-game {\r\n  text-align: center;\r\n}\r\n\r\n.game-display {\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  color: var(--card-primary-color, #ff6b6b);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.game-controls {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.game-btn {\r\n  padding: 6px 12px;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  border: none;\r\n  border-radius: 6px;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-btn:hover {\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n}\r\n\r\n/* 卡片底部 */\r\n.card-footer-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.achievement-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  font-size: 12px;\r\n  color: var(--card-text-color, #ffffff);\r\n}\r\n\r\n.achievement-info i {\r\n  color: #f1c40f;\r\n}\r\n\r\n.expand-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  padding: 6px 12px;\r\n  background: var(--card-primary-color, #ff6b6b);\r\n  border: none;\r\n  border-radius: 6px;\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.expand-btn:hover {\r\n  background: var(--card-secondary-color, #4ecdc4);\r\n}\r\n</style>\r\n"], "mappings": "AA4HA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,OAAOC,QAAO,MAAO,iBAAgB;AAErC,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;IAChC,CAAC;IACDC,KAAK,EAAE;MACLL,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX,CAAC;IACDK,WAAW,EAAE;MACXP,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAEA,CAAA,MAAO;QACdM,OAAO,EAAE,SAAS;QAClBC,SAAS,EAAE,SAAS;QACpBC,UAAU,EAAE,0BAA0B;QACtCC,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;EAEDC,KAAK,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,kBAAkB,CAAC;EAEzDC,KAAKA,CAACf,KAAK,EAAE;IAAEgB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,YAAW,GAAIvB,GAAG,CAAC,OAAO;IAChC,MAAMwB,SAAQ,GAAIxB,GAAG,CAAC,KAAK;IAC3B,MAAMyB,cAAa,GAAIzB,GAAG,CAAC,IAAI;IAC/B,MAAM0B,UAAS,GAAI1B,GAAG,CAAC,KAAK;IAC5B,MAAM2B,UAAS,GAAI3B,GAAG,CAAC,CAAC;IACxB,MAAM4B,UAAS,GAAI5B,GAAG,CAAC,CAAC;;IAExB;IACA,MAAM6B,aAAY,GAAI7B,GAAG,CAAC;MACxB8B,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,cAAc;MAC3BC,IAAI,EAAE,YAAY;MAClBC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,aAAY,GAAIlC,GAAG,CAAC,CACxB;MAAEmC,EAAE,EAAE,OAAO;MAAE/B,IAAI,EAAE,IAAI;MAAE4B,IAAI,EAAE;IAAc,CAAC,EAChD;MAAEG,EAAE,EAAE,MAAM;MAAE/B,IAAI,EAAE,IAAI;MAAE4B,IAAI,EAAE;IAAyB,CAAC,EAC1D;MAAEG,EAAE,EAAE,MAAM;MAAE/B,IAAI,EAAE,IAAI;MAAE4B,IAAI,EAAE;IAAiB,EAClD;;IAED;IACA,MAAMI,OAAM,GAAIpC,GAAG,CAAC,CAClB;MACE8B,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAE;IACX,CAAC,EACD;MACEP,KAAK,EAAE,QAAQ;MACfO,OAAO,EAAE;IACX,EACD;IAED,MAAMC,iBAAgB,GAAItC,GAAG,CAAC,CAAC;IAC/B,MAAMuC,YAAW,GAAItC,QAAQ,CAAC,MAAMmC,OAAO,CAACI,KAAK,CAACF,iBAAiB,CAACE,KAAK,CAAC;;IAE1E;IACA,MAAMC,OAAM,GAAIzC,GAAG,CAAC,CAClB;MACE0C,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BC,OAAO,EAAE;IACX,CAAC,EACD;MACEF,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;MACxCC,OAAO,EAAE;IACX,EACD;IAED,MAAMC,gBAAe,GAAI7C,GAAG,CAAC,CAAC;IAC9B,MAAM8C,WAAU,GAAI7C,QAAQ,CAAC,MAAMwC,OAAO,CAACD,KAAK,CAACK,gBAAgB,CAACL,KAAK,CAAC;;IAExE;IACA,MAAMO,eAAc,GAAIA,CAAA,KAAM;MAC5BzB,IAAI,CAAC,YAAY,EAAE,cAAc;IACnC;IAEA,MAAM0B,kBAAiB,GAAKC,IAAI,IAAK;MACnC1B,YAAY,CAACiB,KAAI,GAAIS,IAAI,CAACd,EAAC;MAC3Bb,IAAI,CAAC,cAAc,EAAE2B,IAAI,CAACd,EAAE;;MAE5B;MACA,IAAIc,IAAI,CAACd,EAAC,KAAM,MAAM,EAAE;QACtBV,cAAc,CAACe,KAAI,GAAI,IAAG;QAC1Bd,UAAU,CAACc,KAAI,GAAI,KAAI;MACzB;IACF;IAEA,MAAMU,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIZ,iBAAiB,CAACE,KAAI,GAAI,CAAC,EAAE;QAC/BF,iBAAiB,CAACE,KAAK,EAAC;MAC1B;IACF;IAEA,MAAMW,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAIb,iBAAiB,CAACE,KAAI,GAAIJ,OAAO,CAACI,KAAK,CAACY,MAAK,GAAI,CAAC,EAAE;QACtDd,iBAAiB,CAACE,KAAK,EAAC;MAC1B;IACF;IAEA,MAAMa,eAAc,GAAIA,CAAA,KAAM;MAC5B7B,SAAS,CAACgB,KAAI,GAAI,CAAChB,SAAS,CAACgB,KAAI;MACjC;IACF;IAEA,MAAMc,gBAAe,GAAKC,KAAK,IAAK;MAClC,IAAI7B,UAAU,CAACc,KAAK,EAAE;MAEtBf,cAAc,CAACe,KAAI,GAAIe,KAAI;MAC3B7B,UAAU,CAACc,KAAI,GAAI,IAAG;;MAEtB;MACAgB,UAAU,CAAC,MAAM;QACf,IAAIX,gBAAgB,CAACL,KAAI,GAAIC,OAAO,CAACD,KAAK,CAACY,MAAK,GAAI,CAAC,EAAE;UACrDP,gBAAgB,CAACL,KAAK,EAAC;UACvBf,cAAc,CAACe,KAAI,GAAI,IAAG;UAC1Bd,UAAU,CAACc,KAAI,GAAI,KAAI;QACzB,OAAO;UACL;UACAlB,IAAI,CAAC,kBAAkB,EAAE,MAAM;QACjC;MACF,CAAC,EAAE,IAAI;IACT;IAEA,MAAMmC,UAAS,GAAKC,MAAM,IAAK;MAC7B,QAAQA,MAAM;QACZ,KAAK,KAAK;UACR/B,UAAU,CAACa,KAAK,EAAC;UACjB;QACF,KAAK,UAAU;UACb,IAAIb,UAAU,CAACa,KAAI,GAAI,CAAC,EAAE;YACxBb,UAAU,CAACa,KAAK,EAAC;UACnB;UACA;QACF,KAAK,OAAO;UACVb,UAAU,CAACa,KAAI,GAAI;UACnB;MACJ;IACF;IAEA,MAAMmB,iBAAgB,GAAIA,CAAA,KAAM;MAC9B;MACAC,OAAO,CAACC,GAAG,CAAC,UAAU;IACxB;;IAEA;IACA3D,SAAS,CAAC,MAAM;MACd;MACA0D,OAAO,CAACC,GAAG,CAAC,WAAW;IACzB,CAAC;IAED,OAAO;MACLtC,YAAY;MACZC,SAAS;MACTC,cAAc;MACdC,UAAU;MACVC,UAAU;MACVC,UAAU;MACVC,aAAa;MACbK,aAAa;MACbK,YAAY;MACZO,WAAW;MACXC,eAAe;MACfC,kBAAkB;MAClBE,aAAa;MACbC,SAAS;MACTE,eAAe;MACfC,gBAAgB;MAChBG,UAAU;MACVE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}